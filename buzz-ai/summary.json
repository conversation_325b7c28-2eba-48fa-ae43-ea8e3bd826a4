{"AbstractRunnable": [{"methodName": "run", "signature": "public void run()", "summary_en": "Execute the task and handle exceptions by calling onFailure", "summary_cn": "执行任务并通过调用onFailure处理异常"}, {"methodName": "doRun", "signature": "public abstract void doRun()", "summary_en": "Abstract method to be implemented for the actual task execution", "summary_cn": "需要实现的具体任务执行方法"}, {"methodName": "onFailure", "signature": "public abstract void onFailure(Exception e)", "summary_en": "Abstract method to handle exceptions occurred during task execution", "summary_cn": "处理任务执行过程中发生的异常的方法"}], "NettyHttpChannel": [{"methodName": "NettyHttpChannel", "signature": "public NettyHttpChannel(Channel channel)", "summary_en": "Constructor for NettyHttpChannel with a given channel", "summary_cn": "使用给定通道构造NettyHttpChannel"}, {"methodName": "setHttpRequest", "signature": "public void setHttpRequest(NettyHttpRequest httpRequest)", "summary_en": "Set the HTTP request for this channel", "summary_cn": "设置此通道的HTTP请求"}, {"methodName": "getClientIp", "signature": "public String getClientIp()", "summary_en": "Get the client IP address from the HTTP request or remote address", "summary_cn": "从HTTP请求或远程地址获取客户端IP地址"}, {"methodName": "getRemoteIp", "signature": "protected String getRemoteIp()", "summary_en": "Get the remote IP address from the channel's remote address", "summary_cn": "从通道的远程地址获取远程IP地址"}, {"methodName": "sendResponse", "signature": "public void sendResponse(RestResponse response)", "summary_en": "Send a REST response over the channel with appropriate headers and status", "summary_cn": "通过通道发送带有适当头和状态的REST响应"}, {"methodName": "close", "signature": "public void close()", "summary_en": "Close the channel", "summary_cn": "关闭通道"}, {"methodName": "addCloseListener", "signature": "public void addCloseListener(ActionListener<Void> listener)", "summary_en": "Add a listener to be notified when the channel is closed", "summary_cn": "添加监听器以在通道关闭时得到通知"}, {"methodName": "isOpen", "signature": "public boolean isOpen()", "summary_en": "Check if the channel is open", "summary_cn": "检查通道是否打开"}, {"methodName": "getRestRequest", "signature": "public RestRequest getRestRequest()", "summary_en": "Get the current HTTP request associated with this channel", "summary_cn": "获取与此通道关联的当前HTTP请求"}, {"methodName": "getHttpResponseStatus", "signature": "protected HttpResponseStatus getHttpResponseStatus(RestStatus restStatus)", "summary_en": "Convert a RestStatus to an HttpResponseStatus", "summary_cn": "将RestStatus转换为HttpResponseStatus"}], "EnvUtil": [{"methodName": "getEnv", "signature": "public static Env getEnv()", "summary_en": "Get the current environment", "summary_cn": "获取当前环境"}, {"methodName": "from", "signature": "public static Env from(String value)", "summary_en": "Convert string value to Env enum", "summary_cn": "将字符串值转换为Env枚举"}, {"methodName": "getValue", "signature": "public String getValue()", "summary_en": "Get the string value of the Env enum", "summary_cn": "获取Env枚举的字符串值"}], "Ordered": [{"methodName": "order", "signature": "default int order()", "summary_en": "Returns the order value, default is HIGH level value (1).", "summary_cn": "返回顺序值，默认为HIGH级别值(1)。"}, {"methodName": "getValue", "signature": "public int getValue()", "summary_en": "Returns the integer value of the Level enum.", "summary_cn": "返回Level枚举的整数值。"}, {"methodName": "compare", "signature": "public int compare(Ordered o1, Ordered o2)", "summary_en": "Compares two Ordered objects based on their order values.", "summary_cn": "根据顺序值比较两个Ordered对象。"}], "HermesRuntimeUtil": [{"methodName": "get", "signature": "public static HermesRuntime get()", "summary_en": "Get the current HermesRuntime instance", "summary_cn": "获取当前的HermesRuntime实例"}, {"methodName": "set", "signature": "public static HermesRuntime set(HermesRuntime runtime)", "summary_en": "Set the HermesRuntime instance and return it", "summary_cn": "设置HermesRuntime实例并返回该实例"}], "OffsetStorage": [{"methodName": "getOffset", "signature": "long getOffset(ConsumerVo consumerVo)", "summary_en": "Get the current stored offset value, returns -1 if not exists", "summary_cn": "获取当前存储的offset值，如果不存在则返回-1"}, {"methodName": "storeOffset", "signature": "void storeOffset(ConsumerVo consumerVo, long offset)", "summary_en": "Save the offset", "summary_cn": "保存offset"}, {"methodName": "resetOffset", "signature": "void resetOffset(ConsumerVo consumerVo, long offset)", "summary_en": "Reset the offset (difference with store is no validation)", "summary_cn": "重置offset（与保存的区别是不会校验）"}], "QueryTopicInfoCommand": [{"methodName": "buildUrl", "signature": "public String buildUrl(String centerUrl)", "summary_en": "Build the complete URL by appending the endpoint to the center URL", "summary_cn": "通过将端点附加到中心URL来构建完整的URL"}, {"methodName": "parseData", "signature": "public List<TopicInfo> parseData(JSONObject rootJson)", "summary_en": "Parse the JSON data to extract a list of TopicInfo objects", "summary_cn": "解析JSON数据以提取TopicInfo对象列表"}], "ExecutorBuilder": [{"methodName": "ExecutorBuilder", "signature": "public ExecutorBuilder(ISettings settings, String name, int coreSize, int maximumPoolSize, int queueSize)", "summary_en": "Constructor for ExecutorBuilder with settings, name, core size, maximum pool size, and queue size", "summary_cn": "ExecutorBuilder的构造函数，接收设置、名称、核心线程数、最大线程数和队列大小"}, {"methodName": "build", "signature": "public abstract ExecutorHolder build()", "summary_en": "Abstract method to build an ExecutorHolder", "summary_cn": "抽象方法，用于构建ExecutorHolder"}, {"methodName": "FixExecutorBuilder", "signature": "public FixExecutorBuilder(ISettings settings, String name, int size, int queueSize)", "summary_en": "Constructor for FixExecutorBuilder with settings, name, size, and queue size", "summary_cn": "FixExecutorBuilder的构造函数，接收设置、名称、线程池大小和队列大小"}, {"methodName": "build", "signature": "public ExecutorHolder build()", "summary_en": "Builds a fixed-size thread pool executor", "summary_cn": "构建一个固定大小的线程池执行器"}, {"methodName": "ScalingExecutorBuilder", "signature": "public ScalingExecutorBuilder(ISettings settings, String name, int coreSize, int maxSize, long keepAliveTime, TimeUnit unit, int queueSize)", "summary_en": "Constructor for ScalingExecutorBuilder with settings, name, core size, max size, keep alive time, time unit, and queue size", "summary_cn": "ScalingExecutorBuilder的构造函数，接收设置、名称、核心线程数、最大线程数、空闲线程存活时间、时间单位和队列大小"}, {"methodName": "build", "signature": "public ExecutorHolder build()", "summary_en": "Builds a scaling thread pool executor with dynamic thread adjustment", "summary_cn": "构建一个可动态调整线程数的线程池执行器"}, {"methodName": "rejectedExecution", "signature": "public void rejectedExecution(Runnable r, ThreadPoolExecutor e)", "summary_en": "<PERSON><PERSON> rejected execution by attempting to force the task into the queue", "summary_cn": "处理任务被拒绝执行的情况，尝试强制将任务放入队列"}, {"methodName": "ExecutorScalingQueue", "signature": "public ExecutorScalingQueue(int capacity)", "summary_en": "Constructor for ExecutorScalingQueue with specified capacity", "summary_cn": "ExecutorScalingQueue的构造函数，接收指定的队列容量"}, {"methodName": "offer", "signature": "public boolean offer(E e)", "summary_en": "Offers an element to the queue, checking for spare thread capacity first", "summary_cn": "尝试将元素放入队列，首先检查是否有空闲线程容量"}, {"methodName": "forcePut", "signature": "public boolean forcePut(E e)", "summary_en": "Forces an element into the queue without checking capacity", "summary_cn": "强制将元素放入队列，不检查容量"}, {"methodName": "ScheduledExecutorBuilder", "signature": "public ScheduledExecutorBuilder(ISettings settings, String name, int size)", "summary_en": "Constructor for ScheduledExecutorBuilder with settings, name, and size", "summary_cn": "ScheduledExecutorBuilder的构造函数，接收设置、名称和线程池大小"}, {"methodName": "build", "signature": "public ExecutorHolder build()", "summary_en": "Builds a scheduled thread pool executor", "summary_cn": "构建一个定时任务线程池执行器"}], "MethodHandler": [{"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "public MethodHandler(RestRequest.Method method, String path, RestHandler restHandler)", "summary_en": "Constructor for MethodHandler class with specified method, path and restHandler", "summary_cn": "MethodHandler类的构造函数，接收方法、路径和restHandler作为参数"}], "ProxyConfigMode": [], "OffsetResetPolicy": [{"methodName": "resetOffset", "signature": "public static long resetOffset(FetchRequest fetchRequest, ConsumerWrapper consumerWrapper, OffsetStorage offsetStorage)", "summary_en": "Reset the offset based on the fetch request's reset policy (earliest, latest) and update the offset storage.", "summary_cn": "根据获取请求的重置策略（最早或最新）重置偏移量，并更新偏移量存储。"}], "Assert": [{"methodName": "notNull", "signature": "public static void notNull(Object o, Errors error)", "summary_en": "Check if the object is not null, otherwise throw an exception with the specified error", "summary_cn": "检查对象是否不为空，否则抛出指定错误的异常"}, {"methodName": "notNull", "signature": "public static void notNull(Object o, Errors error, String msg)", "summary_en": "Check if the object is not null, otherwise throw an exception with the specified error and message", "summary_cn": "检查对象是否不为空，否则抛出指定错误和消息的异常"}, {"methodName": "isNotBlank", "signature": "public static void isNotBlank(String str, Errors error)", "summary_en": "Check if the string is not blank, otherwise throw an exception with the specified error", "summary_cn": "检查字符串是否不为空或空白，否则抛出指定错误的异常"}, {"methodName": "isNotBlank", "signature": "public static void isNotBlank(String str, Errors error, String msg)", "summary_en": "Check if the string is not blank, otherwise throw an exception with the specified error and message", "summary_cn": "检查字符串是否不为空或空白，否则抛出指定错误和消息的异常"}, {"methodName": "isTrue", "signature": "public static void isTrue(boolean o, Errors error)", "summary_en": "Check if the boolean condition is true, otherwise throw an exception with the specified error", "summary_cn": "检查布尔条件是否为真，否则抛出指定错误的异常"}, {"methodName": "isTrue", "signature": "public static void isTrue(boolean o, Errors error, String msg)", "summary_en": "Check if the boolean condition is true, otherwise throw an exception with the specified error and message", "summary_cn": "检查布尔条件是否为真，否则抛出指定错误和消息的异常"}], "AsyncAlert": [{"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "public AsyncAlert(Reporter report)", "summary_en": "Constructor that initializes the AsyncAlert with a reporter and starts a daemon thread for processing messages.", "summary_cn": "构造函数，初始化AsyncAlert并启动一个守护线程处理消息。"}, {"methodName": "send", "signature": "public void send(AlarmMsg msg)", "summary_en": "Sends an alarm message to the queue. Logs a warning if the queue is full.", "summary_cn": "发送告警消息到队列。如果队列已满则记录警告日志。"}, {"methodName": "run", "signature": "public void run()", "summary_en": "Continuously polls messages from the queue, batches them, and sends them at regular intervals. <PERSON>les interruptions and errors gracefully.", "summary_cn": "持续从队列中轮询消息，定期批量发送。优雅处理中断和错误。"}], "HermesHeader": [], "BaseBootstrap": [{"methodName": "registerStartListener", "signature": "public Bootstrap registerStartListener(StartListener startListener)", "summary_en": "Register a start listener to the bootstrap", "summary_cn": "注册一个启动监听器到引导程序"}, {"methodName": "registerStoppable", "signature": "public Bootstrap registerStoppable(Stoppable stoppable)", "summary_en": "Register a stoppable component to the bootstrap", "summary_cn": "注册一个可停止组件到引导程序"}, {"methodName": "install", "signature": "public Bootstrap install(Module module)", "summary_en": "Install a Guice module to the bootstrap", "summary_cn": "安装一个Guice模块到引导程序"}, {"methodName": "install", "signature": "public Bootstrap install(Module module, Module... more)", "summary_en": "Install a Guice module and additional modules to the bootstrap", "summary_cn": "安装一个Guice模块及额外模块到引导程序"}, {"methodName": "start", "signature": "public HermesRuntime start()", "summary_en": "Start the Hermes runtime, initialize components, and trigger start listeners", "summary_cn": "启动Hermes运行时，初始化组件并触发启动监听器"}, {"methodName": "doStart", "signature": "protected void doStart(HermesRuntime hermesRuntime)", "summary_en": "Hook method for custom startup logic", "summary_cn": "用于自定义启动逻辑的钩子方法"}, {"methodName": "getSettings", "signature": "public ISettings getSettings()", "summary_en": "Get the current settings", "summary_cn": "获取当前设置"}, {"methodName": "getStage", "signature": "protected Stage getStage()", "summary_en": "Get the Guice stage based on environment (DEV/TEST or PRODUCTION)", "summary_cn": "根据环境获取Guice阶段（开发/测试或生产环境）"}, {"methodName": "stop", "signature": "public void stop()", "summary_en": "Stop the server and trigger all stoppable components", "summary_cn": "停止服务器并触发所有可停止组件"}, {"methodName": "prepareStop", "signature": "public void prepareStop()", "summary_en": "Prepare for server stop by triggering stoppable components", "summary_cn": "通过触发可停止组件准备服务器停止"}, {"methodName": "doStop", "signature": "protected void doStop(HermesRuntime hermesRuntime)", "summary_en": "Hook method for custom stop logic", "summary_cn": "用于自定义停止逻辑的钩子方法"}], "RestUtils": [{"methodName": "decodeURL", "signature": "public static String decodeURL(String str)", "summary_en": "Decode URL string using UTF-8 encoding, return original string if decoding fails", "summary_cn": "使用UTF-8编码解码URL字符串，如果解码失败则返回原始字符串"}, {"methodName": "decodeQueryString", "signature": "public static void decodeQueryString(String s, int fromIndex, Map<String, String> params)", "summary_en": "Parse and decode query string parameters into a map, handling key-value pairs separated by '&' and '='", "summary_cn": "解析并解码查询字符串参数到Map中，处理由'&'和'='分隔的键值对"}], "ProducerController": [{"methodName": "sendSingle", "signature": "public void sendSingle(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Send a single message to the specified topic with optional acknowledgment and timeout settings.", "summary_cn": "向指定主题发送单条消息，可设置确认和超时参数。"}, {"methodName": "sendBatch", "signature": "public void sendBatch(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Send a batch of messages to the specified topic with optional acknowledgment, timeout, and trace settings.", "summary_cn": "向指定主题发送批量消息，可设置确认、超时和跟踪参数。"}, {"methodName": "publish", "summary_en": "Publish a message to a Kafka topic with optional message key and value, supporting UTF-8 encoding.", "signature": "public void publish(RestRequest restRequest, RestChannel restChannel) throws UnsupportedEncodingException", "summary_cn": "向Kafka主题发布消息，支持可选的键和值，使用UTF-8编码。"}], "FetchRequest": [{"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "public boolean isManual()", "summary_en": "Check if the request is manual by verifying manualOffset or timestamp", "summary_cn": "通过检查manualOffset或timestamp判断是否是手动请求"}, {"methodName": "isResetBegin", "signature": "public boolean isResetBegin()", "summary_en": "Check if the reset type is set to earliest (0)", "summary_cn": "判断重置类型是否为最早（0）"}], "Fetcher": [{"methodName": "isNoProducer", "signature": "boolean isNoProducer(String topic)", "summary_cn": "检查指定主题是否没有生产者", "summary_en": "Check if there is no producer for the specified topic"}, {"methodName": "fetch", "signature": "BatchMessage fetch(FetchRequest fetchRequest)", "summary_cn": "根据获取请求获取批量消息", "summary_en": "Fetch batch messages based on the fetch request"}, {"methodName": "ack", "signature": "AckResult ack(AckRequest ackRequest)", "summary_cn": "处理消息确认请求并返回确认结果", "summary_en": "Process the acknowledgment request and return the acknowledgment result"}, {"methodName": "renew", "signature": "boolean renew(LockRequest lockRequest)", "summary_cn": "续订锁请求", "summary_en": "Renew the lock request"}, {"methodName": "resetOffset", "signature": "boolean resetOffset(ResetVo resetVo, long offset)", "summary_cn": "根据重置对象和偏移量重置偏移量", "summary_en": "Reset the offset based on the reset object and the specified offset"}], "LockRequest": [{"methodName": "getLockMs", "signature": "public long getLockMs()", "summary_en": "Get the lock duration in milliseconds", "summary_cn": "获取锁定的持续时间（毫秒）"}, {"methodName": "setLockMs", "signature": "public void setLockMs(long lockMs)", "summary_en": "Set the lock duration in milliseconds", "summary_cn": "设置锁定的持续时间（毫秒）"}], "Settings": [{"methodName": "putAll", "signature": "public void putAll(Map<Object, Object> map)", "summary_cn": "将指定映射中的所有键值对添加到当前属性集合中", "summary_en": "Adds all key-value pairs from the specified map to the current properties"}, {"methodName": "set", "signature": "public void set(String key, Object value)", "summary_cn": "设置指定键的值", "summary_en": "Sets the value for the specified key"}, {"methodName": "getString", "signature": "public String getString(String key)", "summary_cn": "获取指定键的字符串值", "summary_en": "Gets the string value for the specified key"}, {"methodName": "<PERSON><PERSON><PERSON>", "signature": "public boolean containsKey(String key)", "summary_cn": "检查是否包含指定键", "summary_en": "Checks if the specified key exists"}, {"methodName": "get", "signature": "public Optional<Object> get(String key)", "summary_cn": "获取指定键的值", "summary_en": "Gets the value for the specified key"}, {"methodName": "getAllKeys", "signature": "public List<String> getAllKeys()", "summary_cn": "获取所有键的列表", "summary_en": "Gets a list of all keys"}, {"methodName": "getLong", "signature": "public Long getLong(String key)", "summary_cn": "获取指定键的长整型值", "summary_en": "Gets the long value for the specified key"}, {"methodName": "getLong", "signature": "public Long getLong(String key, Long defaultValue)", "summary_cn": "获取指定键的长整型值，如果不存在则返回默认值", "summary_en": "Gets the long value for the specified key, or returns the default value if not found"}, {"methodName": "getInt", "signature": "public Integer getInt(String key)", "summary_cn": "获取指定键的整型值", "summary_en": "Gets the integer value for the specified key"}, {"methodName": "getInt", "signature": "public Integer getInt(String key, Integer defaultValue)", "summary_cn": "获取指定键的整型值，如果不存在则返回默认值", "summary_en": "Gets the integer value for the specified key, or returns the default value if not found"}, {"methodName": "getBoolean", "signature": "public Boolean getBoolean(String key)", "summary_cn": "获取指定键的布尔值", "summary_en": "Gets the boolean value for the specified key"}, {"methodName": "getBoolean", "signature": "public Boolean getBoolean(String key, Boolean defaultValue)", "summary_cn": "获取指定键的布尔值，如果不存在则返回默认值", "summary_en": "Gets the boolean value for the specified key, or returns the default value if not found"}, {"methodName": "getEnum", "signature": "public <T extends Enum> T getEnum(Class<T> enumClass, String key)", "summary_cn": "获取指定键的枚举值", "summary_en": "Gets the enum value for the specified key"}, {"methodName": "getEnum", "signature": "public <T extends Enum> T getEnum(Class<T> enumClass, String key, T defaultValue)", "summary_cn": "获取指定键的枚举值，如果不存在则返回默认值", "summary_en": "Gets the enum value for the specified key, or returns the default value if not found"}, {"methodName": "remove", "signature": "public Object remove(String key)", "summary_cn": "移除指定键的值", "summary_en": "Removes the value for the specified key"}, {"methodName": "load", "signature": "public static ISettings load(Loader... loaders)", "summary_cn": "从默认配置文件加载设置", "summary_en": "Loads settings from the default configuration file"}, {"methodName": "load", "signature": "public static ISettings load(String configFileName, Loader... loaders)", "summary_cn": "从指定配置文件加载设置", "summary_en": "Loads settings from the specified configuration file"}], "NettyHttpServerTransport": [{"methodName": "NettyHttpServerTransport", "signature": "public NettyHttpServerTransport(ISettings settings, Dispatcher dispatcher)", "summary_en": "Constructor that initializes the transport with settings and dispatcher", "summary_cn": "构造函数，使用设置和调度器初始化传输"}, {"methodName": "doStart", "signature": "protected void doStart()", "summary_en": "Starts the Netty HTTP server by configuring and binding the server", "summary_cn": "通过配置和绑定服务器启动Netty HTTP服务器"}, {"methodName": "configureServerChannelHandler", "signature": "protected ChannelHandler configureServerChannelHandler()", "summary_en": "Configures and returns the HTTP channel handler", "summary_cn": "配置并返回HTTP通道处理器"}, {"methodName": "bindServer", "signature": "protected void bindServer()", "summary_en": "Binds the server to the specified port and handles exceptions", "summary_cn": "将服务器绑定到指定端口并处理异常"}, {"methodName": "dispatchRequest", "signature": "protected void dispatchRequest(final RestRequest request, final RestChannel channel)", "summary_en": "Dispatches the REST request to the appropriate handler", "summary_cn": "将REST请求分派给适当的处理器"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "protected void exception<PERSON><PERSON><PERSON>(ChannelHandlerContext ctx, Throwable cause) throws Exception", "summary_en": "Handles exceptions caught during channel operations", "summary_cn": "处理通道操作期间捕获的异常"}, {"methodName": "doStop", "signature": "protected void doStop()", "summary_en": "Stops the HTTP server gracefully", "summary_cn": "优雅地停止HTTP服务器"}, {"methodName": "addListener", "signature": "public static void addListener(ChannelFuture channelFuture, CompletableContext<Void> context)", "summary_en": "Adds a listener to the channel future that completes the given context", "summary_cn": "向通道未来添加一个监听器，完成给定的上下文"}], "ThreadDumpUtil": [{"methodName": "dump", "signature": "public static void dump()", "summary_en": "Trigger a thread dump event", "summary_cn": "触发线程转储事件"}, {"methodName": "handleEvent", "signature": "private static void handleEvent()", "summary_en": "Handle events from the queue, specifically thread dump events", "summary_cn": "处理队列中的事件，特别是线程转储事件"}, {"methodName": "handleThreadDump", "signature": "private static void handleThreadDump() throws IOException", "summary_en": "Perform the actual thread dump and write it to a file", "summary_cn": "执行实际的线程转储并将其写入文件"}], "FetcherScheduler": [{"methodName": "FetcherScheduler", "signature": "public FetcherScheduler(HermesRuntime runtime, FetchTaskManager fetchTaskManager)", "summary_en": "Constructor that initializes the scheduler with runtime and task manager, and registers the task type.", "summary_cn": "构造函数，使用运行时和任务管理器初始化调度器，并注册任务类型。"}, {"methodName": "FetcherScheduler", "signature": "protected FetcherScheduler(HermesRuntime runtime, String threadPoolName)", "summary_en": "Protected constructor that initializes the scheduler with runtime and thread pool name.", "summary_cn": "受保护的构造函数，使用运行时和线程池名称初始化调度器。"}, {"methodName": "process", "signature": "public void process(Task<FetchRequest, BatchMessage> task)", "summary_en": "Processes the fetch task, handles exceptions, and sets the task response or exception accordingly.", "summary_cn": "处理获取任务，处理异常，并根据情况设置任务响应或异常。"}, {"methodName": "determineDelayMs", "signature": "public int determineDelayMs(Task<FetchRequest, BatchMessage> task)", "summary_en": "Determines the delay in milliseconds for the task based on whether the producer is available.", "summary_cn": "根据生产者是否可用确定任务的延迟时间（毫秒）。"}, {"methodName": "onSendResponse", "signature": "public void onSendResponse(Task<FetchRequest, BatchMessage> task)", "summary_en": "Logs the response details if logging is enabled, including latency and message offsets.", "summary_cn": "如果日志启用，记录响应详情，包括延迟和消息偏移量。"}, {"methodName": "onSendException", "signature": "public void onSendException(Task<FetchRequest, BatchMessage> task)", "summary_en": "Logs the exception details if logging is enabled, including the exception message and latency.", "summary_cn": "如果日志启用，记录异常详情，包括异常消息和延迟。"}, {"methodName": "resetRequest", "signature": "public void resetRequest(Task<FetchRequest, BatchMessage> task)", "summary_en": "Resets the fetch request by clearing the partition and noProducer flag if necessary.", "summary_cn": "重置获取请求，必要时清除分区和noProducer标志。"}], "ZkException": [{"methodName": "ZkException", "signature": "public ZkException()", "summary_en": "Constructs a new ZkException with no detail message.", "summary_cn": "构造一个新的ZkException实例，不包含详细信息。"}, {"methodName": "ZkException", "signature": "public ZkException(String message)", "summary_en": "Constructs a new ZkException with the specified detail message.", "summary_cn": "构造一个新的ZkException实例，包含指定的详细信息。"}, {"methodName": "ZkException", "signature": "public ZkException(Throwable cause)", "summary_en": "Constructs a new ZkException with the specified cause.", "summary_cn": "构造一个新的ZkException实例，包含指定的原因。"}, {"methodName": "ZkException", "signature": "public ZkException(String message, Throwable cause)", "summary_en": "Constructs a new ZkException with the specified detail message and cause.", "summary_cn": "构造一个新的ZkException实例，包含指定的详细信息和原因。"}], "Harmony": [{"methodName": "onStart", "signature": "public void onStart(HermesRuntime runtime)", "summary_en": "Triggered when the application starts, registers with Harmony if enabled in settings.", "summary_cn": "应用启动时触发，如果设置中启用了注册功能，则向Harmony注册。"}, {"methodName": "stop", "signature": "public void stop(HermesRuntime runtime)", "summary_en": "Triggered when the application stops, unregisters from Harmony if enabled in settings.", "summary_cn": "应用停止时触发，如果设置中启用了注册功能，则从Harmony取消注册。"}, {"methodName": "prepareStop", "signature": "public void prepareStop(HermesRuntime runtime) throws Exception", "summary_en": "Prepares the application for stopping by invoking the stop method.", "summary_cn": "通过调用stop方法，准备停止应用。"}, {"methodName": "order", "signature": "public int order()", "summary_en": "Returns the priority order for the stop operation.", "summary_cn": "返回停止操作的优先级顺序。"}, {"methodName": "register", "signature": "public static void register(String domain, int port)", "summary_en": "Registers the application with Harmony using the specified domain and port.", "summary_cn": "使用指定的域名和端口向Harmony注册应用。"}, {"methodName": "unregister", "signature": "public static void unregister(String domain, int port)", "summary_en": "Unregisters the application from Harmony using the specified domain and port.", "summary_cn": "使用指定的域名和端口从Harmony取消注册应用。"}], "HttpUtil": [{"methodName": "builder", "signature": "public static Builder builder()", "summary_en": "Create a new Builder instance for HttpUtil configuration", "summary_cn": "创建一个新的Builder实例用于配置HttpUtil"}, {"methodName": "httpPostJson", "signature": "public String httpPostJson(String urlStr, Map<String, String> header, Object content)", "summary_en": "Send a POST request with JSON content and specified headers", "summary_cn": "发送带有JSON内容和指定请求头的POST请求"}, {"methodName": "httpPost", "signature": "public String httpPost(String url, Map<String, String> headers, String content)", "summary_en": "Send a POST request with specified content and headers, handling response and errors", "summary_cn": "发送带有指定内容和请求头的POST请求，处理响应和错误"}, {"methodName": "connectTimeout", "signature": "public Builder connectTimeout(int connectTimeout)", "summary_en": "Set the connection timeout for the Builder", "summary_cn": "设置Builder的连接超时时间"}, {"methodName": "readTimeout", "signature": "public Builder readTimeout(int readTimeout)", "summary_en": "Set the read timeout for the Builder", "summary_cn": "设置Builder的读取超时时间"}, {"methodName": "build", "signature": "public HttpUtil build()", "summary_en": "Build an HttpUtil instance with configured timeouts", "summary_cn": "构建一个带有配置超时时间的HttpUtil实例"}], "AckRequest": [], "ClientImpl": [{"methodName": "runAsync", "signature": "protected <T> void runAsync(String threadName, String actionName, Supplier<T> actionSupplier, ActionListener<T> taskListener)", "summary_cn": "异步执行任务，处理超时和异常情况", "summary_en": "Execute task asynchronously, handling timeout and exception cases"}, {"methodName": "send", "signature": "public void send(String ack, <PERSON>mesMessage message, long timeout, ActionListener<MessageMeta> actionListener)", "summary_cn": "异步发送单条消息", "summary_en": "Asynchronously send a single message"}, {"methodName": "sendBatch", "signature": "public void sendBatch(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener)", "summary_cn": "异步批量发送消息", "summary_en": "Asynchronously send messages in batch"}, {"methodName": "sendBatchByOneFlush", "signature": "public void sendBatchByOneFlush(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener)", "summary_cn": "异步批量发送消息，单次刷新", "summary_en": "Asynchronously send messages in batch with one flush"}, {"methodName": "fetch", "signature": "public void fetch(FetchRequest fetchRequest, ActionListener<BatchMessage> actionListener)", "summary_cn": "异步获取批量消息", "summary_en": "Asynchronously fetch batch messages"}, {"methodName": "long<PERSON>etch", "signature": "public void longFetch(FetchRequest fetchRequest, ActionListener<BatchMessage> actionListener)", "summary_cn": "异步长轮询获取批量消息", "summary_en": "Asynchronously long poll for batch messages"}, {"methodName": "ack", "signature": "public void ack(AckRequest ackRequest, ActionListener<AckResult> actionListener)", "summary_cn": "异步确认消息处理", "summary_en": "Asynchronously acknowledge message processing"}, {"methodName": "renew", "signature": "public void renew(LockRequest lockRequest, ActionListener<Boolean> actionListener)", "summary_cn": "异步续期锁", "summary_en": "Asynchronously renew lock"}, {"methodName": "getConsumerOffset", "signature": "public void getConsumerOffset(ConsumerVo consumerVo, ActionListener<ConsumerOffset> actionListener)", "summary_cn": "异步获取消费者偏移量", "summary_en": "Asynchronously get consumer offset"}, {"methodName": "getTopicOffsetRange", "signature": "public void getTopicOffsetRange(TopicPartition tp, ActionListener<OffsetRange> actionListener)", "summary_cn": "异步获取主题分区偏移量范围", "summary_en": "Asynchronously get topic partition offset range"}, {"methodName": "resetOffset", "signature": "public void resetOffset(ResetVo resetVo, long offset, ActionListener<Boolean> actionListener)", "summary_cn": "异步重置偏移量", "summary_en": "Asynchronously reset offset"}, {"methodName": "getOffset", "signature": "public void getOffset(ConsumerVo consumerVo, ActionListener<OffsetResp> actionListener)", "summary_cn": "异步获取偏移量信息", "summary_en": "Asynchronously get offset information"}], "RestChannel": [{"methodName": "sendResponse", "signature": "void sendResponse(RestResponse response)", "summary_en": "Sends an http response to the channel", "summary_cn": "向通道发送HTTP响应"}, {"methodName": "close", "signature": "void close()", "summary_en": "Close channel", "summary_cn": "关闭通道"}, {"methodName": "addCloseListener", "signature": "void addCloseListener(ActionListener<Void> listener)", "summary_en": "Adds a listener that will be executed when the channel is closed", "summary_cn": "添加通道关闭时执行的监听器"}, {"methodName": "isOpen", "signature": "boolean isOpen()", "summary_en": "Indicates whether a channel is currently open", "summary_cn": "指示通道当前是否打开"}, {"methodName": "getRestRequest", "signature": "RestRequest getRestRequest()", "summary_en": "Get RestRequest", "summary_cn": "获取RestRequest"}, {"methodName": "getClientIp", "signature": "String getClientIp()", "summary_en": "Get ClientIp", "summary_cn": "获取客户端IP"}], "FetchLockException": [{"methodName": "FetchLockException", "signature": "public FetchLockException(String message, Throwable cause)", "summary_cn": "构造一个带有详细消息和原因的FetchLockException异常", "summary_en": "Constructs a FetchLockException with the specified detail message and cause"}, {"methodName": "FetchLockException", "signature": "public FetchLockException(String message)", "summary_cn": "构造一个带有详细消息的FetchLockException异常", "summary_en": "Constructs a FetchLockException with the specified detail message"}], "RestModule": [{"methodName": "doConfigure", "signature": "protected void doConfigure() throws Exception", "summary_en": "Configures the REST module by binding necessary handlers and transport", "summary_cn": "配置REST模块，绑定必要的处理器和传输组件"}], "ZkClientImpl": [{"methodName": "getData", "signature": "public String getData(String path) throws Exception", "summary_en": "Retrieve data from the specified Zookeeper path", "summary_cn": "从指定的Zookeeper路径获取数据"}, {"methodName": "create", "signature": "public void create(String path, String data) throws Exception", "summary_en": "Create a new node at the specified path with the given data", "summary_cn": "在指定路径创建一个新节点并写入数据"}, {"methodName": "setData", "signature": "public void setData(String path, String value) throws Exception", "summary_en": "Update the data of the node at the specified path", "summary_cn": "更新指定路径节点的数据"}, {"methodName": "casUpdate", "signature": "public boolean casUpdate(String path, long offset)", "summary_en": "Compare-and-swap update for offset value at the specified path", "summary_cn": "在指定路径上对偏移量值进行CAS更新"}, {"methodName": "casUpdate", "signature": "public boolean casUpdate(String path, String oldVal, String newVal, long expired)", "summary_en": "Compare-and-swap update with expiration check for string values", "summary_cn": "带过期检查的字符串值CAS更新"}, {"methodName": "close", "signature": "public void close() throws IOException", "summary_en": "Close the Zookeeper client connection", "summary_cn": "关闭Zookeeper客户端连接"}, {"methodName": "stop", "signature": "public void stop(HermesRuntime runtime) throws Exception", "summary_en": "Stop the Zookeeper client as part of runtime shutdown", "summary_cn": "作为运行时关闭的一部分停止Zookeeper客户端"}, {"methodName": "order", "signature": "public int order()", "summary_en": "Get the shutdown priority order of this component", "summary_cn": "获取该组件的关闭优先级顺序"}], "KafkaModule": [{"methodName": "doConfigure", "signature": "protected void doConfigure() throws Exception", "summary_en": "Configure Kafka admin, consumer and producer settings", "summary_cn": "配置Kafka的管理员、消费者和生产者设置"}, {"methodName": "config<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "protected void configKafkaAdmin()", "summary_en": "Configure and bind KafkaAdminClient instance, register stoppable for closing the client", "summary_cn": "配置并绑定KafkaAdminClient实例，注册可停止项以关闭客户端"}, {"methodName": "configKafkaConsumer", "signature": "protected void configKafkaConsumer()", "summary_en": "Configure Kafka consumer by setting a supplier for ConsumerThreadLocal", "summary_cn": "通过为ConsumerThreadLocal设置供应商来配置Kafka消费者"}, {"methodName": "configKafkaProducer", "signature": "protected void configKafkaProducer()", "summary_en": "Configure Kafka producer pools with different ack levels and bind them, register stoppables", "summary_cn": "配置具有不同确认级别的Kafka生产者池并绑定它们，注册可停止项"}, {"methodName": "getProducerPool", "signature": "protected ProducerPool<KafkaProducerWrapper> getProducerPool(int poolSize, String ack, String flightCount)", "summary_en": "Create and return a ProducerPool of KafkaProducerWrapper instances with specified ack and flightCount settings", "summary_cn": "创建并返回一个具有指定确认和飞行计数设置的KafkaProducerWrapper实例的ProducerPool"}], "RestHandler": [{"methodName": "handRequest", "signature": "void handRequest(RestRequest restRequest, RestChannel restChannel) throws Throwable", "summary_en": "Handles a REST request and sends the response through the provided channel", "summary_cn": "处理REST请求并通过提供的通道发送响应"}], "BaseRestHandler": [{"methodName": "BaseRestHandler", "signature": "public BaseRest<PERSON><PERSON><PERSON>(RestController controller)", "summary_en": "Constructor that registers this handler with the provided controller.", "summary_cn": "构造函数，将当前处理器注册到提供的控制器中。"}, {"methodName": "handRequest", "signature": "public void handRequest(RestRequest restRequest, RestChannel restChannel) throws Throwable", "summary_en": "Handles the REST request. Currently, this method is empty and does not perform any operations.", "summary_cn": "处理REST请求。当前该方法为空，未执行任何操作。"}, {"methodName": "getActionListener", "signature": "protected <T> ActionListener<T> getActionListener(RestChannel restChannel)", "summary_en": "Creates and returns an ActionListener that handles responses and exceptions for REST operations. On successful response, it sends a success response; on exception, it logs the error, sends an alarm if applicable, and sends an error response.", "summary_cn": "创建并返回一个ActionListener，用于处理REST操作的响应和异常。成功响应时发送成功响应；异常时记录错误，如适用则发送警报，并发送错误响应。"}], "RestStatus": [{"methodName": "getStatus", "signature": "public int getStatus()", "summary_en": "Get the status code of the RestStatus enum", "summary_cn": "获取RestStatus枚举的状态码"}, {"methodName": "fromCode", "signature": "public static RestStatus fromCode(int code)", "summary_en": "Get the RestStatus enum corresponding to the given status code", "summary_cn": "根据给定的状态码获取对应的RestStatus枚举"}], "HermesMessage": [{"methodName": "HermesMessage", "signature": "public HermesMessage()", "summary_en": "Default constructor for HermesMessage", "summary_cn": "HermesMessage的默认构造函数"}, {"methodName": "HermesMessage", "signature": "public HermesMessage(byte[] key, byte[] data)", "summary_en": "Constructor for HermesMessage with key and data", "summary_cn": "使用key和data构造HermesMessage"}, {"methodName": "HermesMessage", "signature": "public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp)", "summary_en": "Constructor for HermesMessage with topic, partition, key, data and timestamp", "summary_cn": "使用topic、partition、key、data和timestamp构造HermesMessage"}, {"methodName": "builder", "signature": "public static Builder builder()", "summary_en": "Create a new Builder instance", "summary_cn": "创建一个新的Builder实例"}, {"methodName": "getPayLoad", "signature": "public long getPayLoad()", "summary_en": "Calculate the total size of key and data", "summary_cn": "计算key和data的总大小"}, {"methodName": "getTraceId", "signature": "public String getTraceId()", "summary_en": "Get trace id (currently returns null)", "summary_cn": "获取trace id（当前返回null）"}, {"methodName": "equals", "signature": "public boolean equals(Object o)", "summary_en": "Check if two HermesMessage objects are equal", "summary_cn": "检查两个HermesMessage对象是否相等"}, {"methodName": "hashCode", "signature": "public int hashCode()", "summary_en": "Generate hash code for HermesMessage", "summary_cn": "生成HermesMessage的哈希码"}, {"methodName": "toString", "signature": "public String toString()", "summary_en": "Convert <PERSON><PERSON><PERSON><PERSON><PERSON> to string representation", "summary_cn": "将HermesMessage转换为字符串表示"}, {"methodName": "setHeaders", "signature": "public Builder setHeaders(Map<String, byte[]> headers)", "summary_en": "Set headers for Builder", "summary_cn": "为Builder设置headers"}, {"methodName": "setTopic", "signature": "public Builder setTopic(String topic)", "summary_en": "Set topic for Builder", "summary_cn": "为Builder设置topic"}, {"methodName": "setPartition", "signature": "public Builder setPartition(Integer partition)", "summary_en": "Set partition for Builder", "summary_cn": "为Builder设置partition"}, {"methodName": "<PERSON><PERSON><PERSON>", "signature": "public Builder setKey(byte[] key)", "summary_en": "Set key for Builder", "summary_cn": "为Builder设置key"}, {"methodName": "setData", "signature": "public Builder setData(byte[] data)", "summary_en": "Set data for Builder", "summary_cn": "为Builder设置data"}, {"methodName": "setTimestamp", "signature": "public Builder setTimestamp(long timestamp)", "summary_en": "Set timestamp for Builder", "summary_cn": "为Builder设置timestamp"}, {"methodName": "setTag", "signature": "public Builder setTag(String tag)", "summary_en": "Set tag for Builder", "summary_cn": "为Builder设置tag"}, {"methodName": "build", "signature": "public HermesMessage build()", "summary_en": "Build HermesMessage instance", "summary_cn": "构建HermesMessage实例"}], "TopicPartition": [{"methodName": "getTopic", "signature": "public String getTopic()", "summary_en": "Get the topic name", "summary_cn": "获取主题名称"}, {"methodName": "setTopic", "signature": "public void setTopic(String topic)", "summary_en": "Set the topic name", "summary_cn": "设置主题名称"}, {"methodName": "getPartition", "signature": "public int getPartition()", "summary_en": "Get the partition number", "summary_cn": "获取分区号"}, {"methodName": "setPartition", "signature": "public void setPartition(int partition)", "summary_en": "Set the partition number", "summary_cn": "设置分区号"}, {"methodName": "toString", "signature": "public String toString()", "summary_en": "Get the string representation of the object", "summary_cn": "获取对象的字符串表示"}, {"methodName": "equals", "signature": "public boolean equals(Object o)", "summary_en": "Check if the object is equal to another object", "summary_cn": "判断对象是否相等"}, {"methodName": "hashCode", "signature": "public int hashCode()", "summary_en": "Get the hash code of the object", "summary_cn": "获取对象的哈希码"}], "BaseModule": [{"methodName": "setBootstrap", "signature": "public void setBootstrap(Bootstrap bootstrap)", "summary_en": "Sets the bootstrap instance and retrieves settings from it.", "summary_cn": "设置引导程序实例并从其中获取设置。"}, {"methodName": "bindSPI", "signature": "public <I, S extends I> void bindSPI(Class<I> interfaceType, Class<S>... subClasses)", "summary_en": "Binds multiple implementations to a service provider interface (SPI) using Guice's Multibinder.", "summary_cn": "使用Guice的Multibinder将多个实现绑定到服务提供接口(SPI)。"}, {"methodName": "configure", "signature": "protected void configure()", "summary_en": "Configures the module by calling the abstract doConfigure method, handling any exceptions.", "summary_cn": "通过调用抽象的doConfigure方法配置模块，处理任何异常。"}, {"methodName": "doConfigure", "signature": "protected abstract void doConfigure() throws Exception", "summary_en": "Abstract method to be implemented by subclasses for module-specific configuration.", "summary_cn": "抽象方法，由子类实现以进行模块特定的配置。"}], "AbstractLifecycleComponent": [{"methodName": "start", "signature": "public void start()", "summary_en": "Start the component lifecycle, triggering beforeStart and afterStart callbacks", "summary_cn": "启动组件生命周期，触发beforeStart和afterStart回调"}, {"methodName": "doStart", "signature": "protected abstract void doStart()", "summary_en": "Abstract method to be implemented for component-specific startup logic", "summary_cn": "抽象方法，需实现组件特定的启动逻辑"}, {"methodName": "stop", "signature": "public void stop()", "summary_en": "Stop the component lifecycle, triggering beforeStop and afterStop callbacks", "summary_cn": "停止组件生命周期，触发beforeStop和afterStop回调"}, {"methodName": "doStop", "signature": "protected abstract void doStop()", "summary_en": "Abstract method to be implemented for component-specific stop logic", "summary_cn": "抽象方法，需实现组件特定的停止逻辑"}, {"methodName": "addLifecycleListener", "signature": "public void addLifecycleListener(LifecycleListener listener)", "summary_en": "Add a lifecycle listener to the component", "summary_cn": "添加一个生命周期监听器到组件"}, {"methodName": "removeLifecycleListener", "signature": "public void removeLifecycleListener(LifecycleListener listener)", "summary_en": "Remove a lifecycle listener from the component", "summary_cn": "从组件中移除一个生命周期监听器"}], "PrometheusHandler": [{"methodName": "PrometheusHandler", "signature": "public Prometheus<PERSON><PERSON><PERSON>(RestController controller)", "summary_en": "Constructor for PrometheusHandler with RestController injection", "summary_cn": "PrometheusHandler的构造函数，注入RestController"}, {"methodName": "checkReadiness", "signature": "public void checkReadiness(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Handles readiness check request and responds with 'ok'", "summary_cn": "处理就绪检查请求并返回'ok'响应"}], "BatchMessageMeta": [{"methodName": "getMessageMetaList", "signature": "public List<MessageMeta> getMessageMetaList()", "summary_en": "Get the list of message metadata", "summary_cn": "获取消息元数据列表"}, {"methodName": "setMessageMetaList", "signature": "public void setMessageMetaList(List<MessageMeta> messageMetaList)", "summary_en": "Set the list of message metadata", "summary_cn": "设置消息元数据列表"}, {"methodName": "getProxyCostUs", "signature": "public long getProxyCostUs()", "summary_en": "Get the proxy cost in microseconds", "summary_cn": "获取代理耗时（微秒）"}, {"methodName": "setProxyCostUs", "signature": "public void setProxyCostUs(long proxyCostUs)", "summary_en": "Set the proxy cost in microseconds", "summary_cn": "设置代理耗时（微秒）"}], "AbstractFetcher": [{"methodName": "rewriteFetchRequest", "signature": "protected void rewriteFetchRequest(FetchRequest fetchRequest)", "summary_cn": "重写FetchRequest，设置分区数量", "summary_en": "Rewrite FetchRequest to set partition number"}, {"methodName": "getOffsetStorage", "signature": "protected abstract OffsetStorage getOffsetStorage()", "summary_cn": "获取OffsetStorage实例", "summary_en": "Get OffsetStorage instance"}, {"methodName": "tryLock0", "signature": "protected abstract boolean tryLock0(ConsumerVo consumerVo)", "summary_cn": "尝试获取锁", "summary_en": "Try to acquire lock"}, {"methodName": "releaseLock0", "signature": "public abstract boolean releaseLock0(ConsumerVo consumerVo)", "summary_cn": "释放锁", "summary_en": "Release lock"}, {"methodName": "tryLock", "signature": "protected void tryLock(FetchRequest fetchRequest)", "summary_cn": "尝试获取锁，非手动设置offset的场景需要加锁", "summary_en": "Try to acquire lock, required for non-manual offset scenarios"}, {"methodName": "releaseLock", "signature": "protected void releaseLock(FetchRequest fetchRequest)", "summary_cn": "释放锁，非手动设置offset的场景需要释放锁", "summary_en": "Release lock, required for non-manual offset scenarios"}, {"methodName": "validate", "signature": "protected void validate(FetchRequest fetchRequest)", "summary_cn": "校验请求参数，确保分区存在", "summary_en": "Validate request parameters to ensure partition exists"}, {"methodName": "fetch", "signature": "public BatchMessage fetch(FetchRequest fetchRequest) throws FetchLockException", "summary_cn": "拉取消息，包括重写请求、加锁、校验、计算offset和从MQ拉取消息", "summary_en": "Fetch messages, including rewriting request, acquiring lock, validating, calculating offset, and pulling messages from MQ"}, {"methodName": "ack", "signature": "public AckResult ack(AckRequest ackRequest)", "summary_cn": "确认消息，存储offset并释放锁", "summary_en": "Acknowledge messages, store offset and release lock"}, {"methodName": "getNextOffset", "signature": "protected long getNextOffset(FetchRequest fetchRequest, ConsumerWrapper consumerWrapper)", "summary_cn": "获取下一个offset，支持手动设置offset或从缓存获取", "summary_en": "Get next offset, supports manual offset or fetching from cache"}, {"methodName": "buildPullParam", "signature": "protected PullParam buildPullParam(FetchRequest fetchRequest, long startNextOffset)", "summary_cn": "构建PullParam对象，设置拉取消息的参数", "summary_en": "Build PullParam object, set parameters for pulling messages"}], "Scheduler": [{"methodName": "isRunning", "signature": "boolean isRunning()", "summary_en": "Check if the scheduler is currently running", "summary_cn": "检查调度器是否正在运行"}, {"methodName": "submit", "signature": "boolean submit(Task<Req, Res> task)", "summary_en": "Submit a task to the scheduler for processing", "summary_cn": "提交一个任务给调度器进行处理"}], "TopicNotFoundException": [{"methodName": "TopicNotFoundException", "signature": "public TopicNotFoundException(String message, Throwable cause)", "summary_en": "Constructs a new exception with the specified detail message and cause", "summary_cn": "构造一个新的异常，包含指定的详细信息和原因"}, {"methodName": "TopicNotFoundException", "signature": "public TopicNotFoundException(String message)", "summary_en": "Constructs a new exception with the specified detail message", "summary_cn": "构造一个新的异常，包含指定的详细信息"}], "ActionTimeoutException": [{"methodName": "ActionTimeoutException", "signature": "public ActionTimeoutException(String message)", "summary_en": "Constructs an ActionTimeoutException with the specified detail message.", "summary_cn": "构造一个带有指定详细消息的ActionTimeoutException异常。"}, {"methodName": "ActionTimeoutException", "signature": "public ActionTimeoutException(Throwable cause)", "summary_en": "Constructs an ActionTimeoutException with the specified cause.", "summary_cn": "构造一个带有指定原因的ActionTimeoutException异常。"}], "ThreadPool": [{"methodName": "submit", "signature": "public void submit(String threadName, AbstractRunnable runnable)", "summary_en": "Submit a task to the specified thread pool and send an alert if the queue size exceeds 150", "summary_cn": "提交任务到指定线程池，如果队列大小超过150则发送警报"}, {"methodName": "executor", "signature": "public ExecutorService executor(String name)", "summary_en": "Retrieve the executor service by name, throws IllegalArgumentException if not found", "summary_cn": "根据名称获取执行器服务，如果未找到则抛出IllegalArgumentException"}, {"methodName": "getAllExecutor", "signature": "public List<ExecutorHolder> getAllExecutor()", "summary_en": "Get all executor holders in the thread pool", "summary_cn": "获取线程池中所有的执行器持有者"}, {"methodName": "executor", "signature": "public ExecutorService executor()", "summary_en": "Get the executor service from the holder", "summary_cn": "从持有者中获取执行器服务"}, {"methodName": "getName", "signature": "public String getName()", "summary_en": "Get the name of the executor info", "summary_cn": "获取执行器信息的名称"}], "AuthInterceptor": [{"methodName": "beforeHandRequest", "signature": "public boolean beforeHandRequest(RestRequest request, RestChannel restChannel)", "summary_en": "Perform authentication before handling the request. Checks appName, topic, and token validity.", "summary_cn": "在处理请求前进行认证。检查appName、topic和token的有效性。"}, {"methodName": "afterHandRequest", "signature": "public void afterHandRequest(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Post-processing after handling the request. Currently empty implementation.", "summary_cn": "请求处理后的后置处理。当前为空实现。"}, {"methodName": "registerHandlerInterceptor", "signature": "public void registerHandlerInterceptor(HandlerInterceptorRegistry registry)", "summary_en": "Register this interceptor with specified URL patterns for authentication.", "summary_cn": "将当前拦截器注册到指定的URL模式进行认证。"}], "RedisLockFetcher": [{"methodName": "tryLock0", "signature": "protected boolean tryLock0(ConsumerVo consumerVo)", "summary_en": "Attempt to acquire a lock for the specified consumer.", "summary_cn": "尝试为指定的消费者获取锁"}, {"methodName": "releaseLock0", "signature": "public boolean releaseLock0(ConsumerVo consumerVo)", "summary_en": "Release the lock held by the specified consumer.", "summary_cn": "释放指定消费者持有的锁"}, {"methodName": "rewriteFetchRequest", "signature": "protected void rewriteFetchRequest(FetchRequest fetchRequest)", "summary_en": "Rewrite the fetch request, setting the partition if not already set.", "summary_cn": "重写获取请求，如果未设置分区则设置分区"}, {"methodName": "getOffsetStorage", "signature": "protected OffsetStorage getOffsetStorage()", "summary_en": "Get the offset storage instance.", "summary_cn": "获取偏移量存储实例"}, {"methodName": "renew", "signature": "public boolean renew(LockRequest lockRequest)", "summary_en": "Renew the lock with the specified lock duration.", "summary_cn": "使用指定的锁持续时间续期锁"}, {"methodName": "reset", "signature": "public boolean reset(ResetVo resetVo, long offset)", "summary_en": "Reset to the specified offset, acquiring a lock if necessary.", "summary_cn": "重置到指定的偏移量，必要时获取锁"}], "ProducerPool": [{"methodName": "ProducerPool", "signature": "public ProducerPool(List<T> list)", "summary_cn": "构造函数，初始化生产者池", "summary_en": "Con<PERSON><PERSON><PERSON>, initializes the producer pool"}, {"methodName": "hasNext", "signature": "public boolean hasNext()", "summary_cn": "检查是否还有下一个生产者", "summary_en": "Check if there is a next producer"}, {"methodName": "next", "signature": "public T next()", "summary_cn": "获取下一个生产者", "summary_en": "Get the next producer"}, {"methodName": "stop", "signature": "public void stop(HermesRuntime runtime) throws Exception", "summary_cn": "停止所有生产者并关闭资源", "summary_en": "Stop all producers and close resources"}, {"methodName": "order", "signature": "public int order()", "summary_cn": "获取停止优先级", "summary_en": "Get the stop priority"}], "RestController": [{"methodName": "registerHandler", "signature": "public void registerHandler(BaseRestHandler restHandler)", "summary_en": "Automatically registers handlers by scanning methods annotated with @RequestMapping in BaseRestHandler", "summary_cn": "通过扫描 BaseRestHandler 方法上的@RequestMapping自动注册处理器"}, {"methodName": "registerHandler", "signature": "public void registerHandler(String path, RestHandler restHandler)", "summary_en": "Registers a handler for the specified path with default HTTP method", "summary_cn": "为指定路径注册一个处理器，使用默认的HTTP方法"}, {"methodName": "registerHandler", "signature": "public void registerHandler(RestRequest.Method method, String path, RestHandler restHandler)", "summary_en": "Registers a handler for the specified path and HTTP method", "summary_cn": "为指定路径和HTTP方法注册一个处理器"}, {"methodName": "dispatchRequest", "signature": "public void dispatchRequest(final RestRequest request, final RestChannel channel)", "summary_en": "Dispatches the incoming request to the appropriate handler", "summary_cn": "将传入的请求分派给适当的处理器"}, {"methodName": "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "protected MethodHandler findMethod<PERSON>andler(RestRequest request)", "summary_en": "Finds the appropriate method handler for the given request path", "summary_cn": "为给定的请求路径查找适当的方法处理器"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "void tryHandler(MethodHandler methodHandler, RestRequest request, RestChannel channel)", "summary_en": "Attempts to handle the request with the given method handler, performing validation and executing interceptor chain", "summary_cn": "尝试用给定的方法处理器处理请求，执行验证并运行拦截器链"}, {"methodName": "handleBadRequest", "signature": "protected void handleBadRequest(String uri, RestChannel channel)", "summary_en": "Handles cases where the requested resource is not found (404)", "summary_cn": "处理请求资源未找到的情况(404)"}, {"methodName": "handleMethodNotAllowed", "signature": "protected void handleMethodNotAllowed(String uri, RestChannel channel)", "summary_en": "Handles cases where the HTTP method is not allowed for the resource (405)", "summary_cn": "处理HTTP方法不被允许的情况(405)"}], "LifecycleListener": [{"methodName": "beforeStart", "signature": "default void beforeStart()", "summary_cn": "在启动前执行的操作", "summary_en": "Operations to perform before starting"}, {"methodName": "afterStart", "signature": "default void afterStart()", "summary_cn": "在启动后执行的操作", "summary_en": "Operations to perform after starting"}, {"methodName": "beforeStop", "signature": "default void beforeStop()", "summary_cn": "在停止前执行的操作", "summary_en": "Operations to perform before stopping"}, {"methodName": "afterStop", "signature": "default void afterStop()", "summary_cn": "在停止后执行的操作", "summary_en": "Operations to perform after stopping"}], "ReflectException": [{"methodName": "ReflectException", "signature": "public ReflectException(String message)", "summary_en": "Constructs a new ReflectException with the specified detail message.", "summary_cn": "使用指定的详细消息构造一个新的ReflectException。"}, {"methodName": "ReflectException", "signature": "public ReflectException(Throwable cause)", "summary_en": "Constructs a new ReflectException with the specified cause.", "summary_cn": "使用指定的原因构造一个新的ReflectException。"}, {"methodName": "ReflectException", "signature": "public ReflectException(String message, Throwable cause)", "summary_en": "Constructs a new ReflectException with the specified detail message and cause.", "summary_cn": "使用指定的详细消息和原因构造一个新的ReflectException。"}], "RedisOffsetStorage": [{"methodName": "getOffset", "signature": "public long getOffset(ConsumerVo consumerVo)", "summary_en": "Retrieve the offset for a given consumer from Redis storage", "summary_cn": "从Redis存储中获取指定消费者的偏移量"}, {"methodName": "storeOffset", "signature": "public void storeOffset(ConsumerVo consumerVo, long offset)", "summary_en": "Store the offset for a consumer in Redis if it's newer than the existing one", "summary_cn": "将消费者的偏移量存储到Redis中（仅当新偏移量大于现有值时）"}, {"methodName": "resetOffset", "signature": "public void resetOffset(ConsumerVo consumerVo, long offset)", "summary_en": "Forcefully reset the offset for a consumer in Redis storage", "summary_cn": "强制重置Redis中指定消费者的偏移量"}], "LoaderException": [{"methodName": "LoaderException", "signature": "public LoaderException(Throwable cause)", "summary_en": "Constructs a new LoaderException with the specified cause.", "summary_cn": "构造一个新的LoaderException，指定其引发原因。"}], "HealthImpl": [{"methodName": "down", "signature": "public void down()", "summary_en": "Set health status to down", "summary_cn": "将健康状态设置为不健康"}, {"methodName": "health", "signature": "public boolean health()", "summary_en": "Get current health status", "summary_cn": "获取当前健康状态"}, {"methodName": "onInject", "signature": "public void onInject(HermesRuntime hermesRuntime)", "summary_en": "Register this instance as a stoppable component in HermesRuntime", "summary_cn": "在HermesRuntime中注册此实例为可停止组件"}, {"methodName": "stop", "signature": "public void stop(HermesRuntime runtime) throws Exception", "summary_en": "Stop method implementation (currently no operation)", "summary_cn": "停止方法实现（当前无操作）"}, {"methodName": "order", "signature": "public int order()", "summary_en": "Get the priority order of this component", "summary_cn": "获取此组件的优先级顺序"}], "LifecycleComponent": [{"methodName": "start", "signature": "void start()", "summary_cn": "启动组件", "summary_en": "Start the component"}, {"methodName": "stop", "signature": "void stop()", "summary_cn": "停止组件", "summary_en": "Stop the component"}, {"methodName": "addLifecycleListener", "signature": "void addLifecycleListener(LifecycleListener listener)", "summary_cn": "添加生命周期监听器", "summary_en": "Add a lifecycle listener"}, {"methodName": "removeLifecycleListener", "signature": "void removeLifecycleListener(LifecycleListener listener)", "summary_cn": "移除生命周期监听器", "summary_en": "Remove a lifecycle listener"}], "ProduceServiceImpl": [{"methodName": "getActionListener", "signature": "ActionListener<MessageMeta> getActionListener()", "summary_en": "Get an action listener for synchronizing offsets", "summary_cn": "获取用于同步offset的回调监听器"}, {"methodName": "check", "signature": "public void check(HermesMessage message)", "summary_en": "Validate the message's partition information", "summary_cn": "校验消息的分区信息"}, {"methodName": "send", "signature": "public MessageMeta send(String ack, HermesMessage message, long timeout)", "summary_en": "Send a message with specified acknowledgment level and timeout", "summary_cn": "根据指定的确认级别和超时时间发送消息"}, {"methodName": "sendBatchByOneFlush", "signature": "public BatchMessageMeta sendBatchByOneFlush(String ack, List<HermesMessage> messages, long timeout)", "summary_en": "Send a batch of messages with one flush operation", "summary_cn": "批量发送消息，一次性刷新"}, {"methodName": "sendBatch", "signature": "public BatchMessageMeta sendBatch(String ack, List<HermesMessage> messages, long timeout)", "summary_en": "Send a batch of messages individually", "summary_cn": "批量发送消息，逐个发送"}], "AuthExpiredException": [{"methodName": "AuthExpiredException", "signature": "public AuthExpiredException(String message, Throwable cause)", "summary_en": "Constructs a new AuthExpiredException with the specified detail message and cause.", "summary_cn": "构造一个新的AuthExpiredException实例，包含指定的详细消息和原因。"}, {"methodName": "AuthExpiredException", "signature": "public AuthExpiredException(String message)", "summary_en": "Constructs a new AuthExpiredException with the specified detail message.", "summary_cn": "构造一个新的AuthExpiredException实例，包含指定的详细消息。"}], "HermesProxyLoader": [{"methodName": "load", "signature": "public void load(ISettings settings) throws LoaderException", "summary_en": "Load proxy configuration from center or local based on the mode, and setup the settings accordingly.", "summary_cn": "根据模式从中心或本地加载代理配置，并相应地设置配置。"}, {"methodName": "getClusterInfoFromLocal", "signature": "protected ClusterInfo getClusterInfoFromLocal(String clusterId, String brokerAddress, String zkAddress)", "summary_en": "Create and return a ClusterInfo object with the given local broker and zookeeper addresses.", "summary_cn": "根据给定的本地broker和zookeeper地址创建并返回一个ClusterInfo对象。"}, {"methodName": "getClusterInfoFromCenter", "signature": "protected ClusterInfo getClusterInfoFromCenter(String centerUrl, String clusterId)", "summary_en": "Retrieve cluster information from the center using the given URL and cluster ID.", "summary_cn": "使用给定的URL和集群ID从中心获取集群信息。"}, {"methodName": "setupSettings", "signature": "protected void setupSettings(ISettings settings, ClusterInfo clusterInfo)", "summary_en": "Update the settings with the cluster information including broker list, zookeeper server, and producer pool sizes.", "summary_cn": "使用集群信息更新配置，包括broker列表、zookeeper服务器和生产者池大小。"}], "HandlerInterceptorRegistry": [{"methodName": "registerHandlerInterceptor", "signature": "public HandlerInterceptorInfo registerHandlerInterceptor(HandlerInterceptor interceptor)", "summary_en": "Register a handler interceptor and return its info object.", "summary_cn": "注册一个处理器拦截器并返回其信息对象。"}, {"methodName": "lookup", "signature": "public List<HandlerInterceptor> lookup(RestRequest restRequest)", "summary_en": "Look up and return a list of handler interceptors sorted by order, matching the request path against include patterns.", "summary_cn": "查找并返回按顺序排序的处理器拦截器列表，根据请求路径匹配包含模式。"}], "BatchMessage": [{"methodName": "getTopic", "signature": "public String getTopic()", "summary_cn": "获取消息主题", "summary_en": "Get the message topic"}, {"methodName": "setTopic", "signature": "public void setTopic(String topic)", "summary_cn": "设置消息主题", "summary_en": "Set the message topic"}, {"methodName": "getPartition", "signature": "public int getPartition()", "summary_cn": "获取分区号", "summary_en": "Get the partition number"}, {"methodName": "setPartition", "signature": "public void setPartition(int partition)", "summary_cn": "设置分区号", "summary_en": "Set the partition number"}, {"methodName": "getStartOffset", "signature": "public long getStartOffset()", "summary_cn": "获取起始偏移量", "summary_en": "Get the start offset"}, {"methodName": "setStartOffset", "signature": "public void setStartOffset(long startOffset)", "summary_cn": "设置起始偏移量", "summary_en": "Set the start offset"}, {"methodName": "getEndOffset", "signature": "public long getEndOffset()", "summary_cn": "获取结束偏移量", "summary_en": "Get the end offset"}, {"methodName": "setEndOffset", "signature": "public void setEndOffset(long endOffset)", "summary_cn": "设置结束偏移量", "summary_en": "Set the end offset"}, {"methodName": "getPayLoad", "signature": "public long getPayLoad()", "summary_cn": "获取载荷大小", "summary_en": "Get the payload size"}, {"methodName": "setPayLoad", "signature": "public void setPayLoad(long payLoad)", "summary_cn": "设置载荷大小", "summary_en": "Set the payload size"}, {"methodName": "getTotal", "signature": "public int getTotal()", "summary_cn": "获取总记录数", "summary_en": "Get the total number of records"}, {"methodName": "setTotal", "signature": "public void setTotal(int total)", "summary_cn": "设置总记录数", "summary_en": "Set the total number of records"}, {"methodName": "getMessageList", "signature": "public List<Message> getMessageList()", "summary_cn": "获取消息列表", "summary_en": "Get the message list"}, {"methodName": "setMessageList", "signature": "public void setMessageList(List<Message> messageList)", "summary_cn": "设置消息列表", "summary_en": "Set the message list"}, {"methodName": "getAckOffset", "signature": "public long getAckOffset()", "summary_cn": "获取ACK偏移量", "summary_en": "Get the ACK offset"}, {"methodName": "setAckOffset", "signature": "public void setAckOffset(long ackOffset)", "summary_cn": "设置ACK偏移量", "summary_en": "Set the ACK offset"}, {"methodName": "getOrignTotal", "signature": "public long getOrignTotal()", "summary_cn": "获取未过滤前的总记录数", "summary_en": "Get the original total number of records before filtering"}, {"methodName": "setOrignTotal", "signature": "public void setOrignTotal(long orignTotal)", "summary_cn": "设置未过滤前的总记录数", "summary_en": "Set the original total number of records before filtering"}], "GetClusterInfoCommand": [{"methodName": "GetClusterInfoCommand", "signature": "public GetClusterInfoCommand(String centerUrl)", "summary_en": "Constructor for GetClusterInfoCommand with centerUrl parameter", "summary_cn": "构造函数，初始化GetClusterInfoCommand并设置centerUrl"}, {"methodName": "buildUrl", "signature": "public String buildUrl(String clusterId)", "summary_en": "Build the URL for cluster information by appending clusterId to centerUrl", "summary_cn": "通过将clusterId附加到centerUrl来构建集群信息的URL"}, {"methodName": "parseData", "signature": "public ClusterInfo parseData(JSONObject rootJson)", "summary_en": "Parse JSON data to extract ClusterInfo object", "summary_cn": "解析JSON数据以提取ClusterInfo对象"}], "SendErrorException": [{"methodName": "SendErrorException", "signature": "public SendErrorException(String message, Throwable cause)", "summary_en": "Constructs a new SendErrorException with the specified detail message and cause.", "summary_cn": "构造一个新的SendErrorException实例，包含指定的详细消息和原因。"}, {"methodName": "SendErrorException", "signature": "public SendErrorException(String message)", "summary_en": "Constructs a new SendErrorException with the specified detail message.", "summary_cn": "构造一个新的SendErrorException实例，包含指定的详细消息。"}], "FetcherImpl": [{"methodName": "isNoProducer", "signature": "public boolean isNoProducer(String topic)", "summary_en": "Check if there is no producer for the given topic", "summary_cn": "检查指定主题是否有生产者"}, {"methodName": "fetch", "signature": "public BatchMessage fetch(FetchRequest fetchRequest)", "summary_en": "Fetch batch messages using Redis lock fetcher, fallback to ZK fetcher if Redis fails", "summary_cn": "使用Redis锁获取批量消息，若Redis失败则回退到ZK获取"}, {"methodName": "ack", "signature": "public AckResult ack(AckRequest ackRequest)", "summary_en": "Acknowledge message processing using Redis lock fetcher, fallback to ZK fetcher if Redis fails", "summary_cn": "使用Redis锁确认消息处理，若Redis失败则回退到ZK确认"}, {"methodName": "renew", "signature": "public boolean renew(LockRequest lockRequest)", "summary_en": "Renew the lock with specified lock request", "summary_cn": "根据指定的锁请求续期锁"}, {"methodName": "resetOffset", "signature": "public boolean resetOffset(ResetVo resetVo, long offset)", "summary_en": "Reset offset after acquiring lock", "summary_cn": "获取锁后重置偏移量"}], "PullParam": [{"methodName": "getTopic", "signature": "public String getTopic()", "summary_en": "Get the topic name", "summary_cn": "获取主题名称"}, {"methodName": "setTopic", "signature": "public void setTopic(String topic)", "summary_en": "Set the topic name", "summary_cn": "设置主题名称"}, {"methodName": "getPartition", "signature": "public int getPartition()", "summary_en": "Get the partition number", "summary_cn": "获取分区号"}, {"methodName": "setPartition", "signature": "public void setPartition(int partition)", "summary_en": "Set the partition number", "summary_cn": "设置分区号"}, {"methodName": "getStartNextOffset", "signature": "public long getStartNextOffset()", "summary_en": "Get the start next offset", "summary_cn": "获取起始下一个偏移量"}, {"methodName": "setStartNextOffset", "signature": "public void setStartNextOffset(long startNextOffset)", "summary_en": "Set the start next offset", "summary_cn": "设置起始下一个偏移量"}, {"methodName": "getPollMs", "signature": "public long getPollMs()", "summary_en": "Get the poll time in milliseconds", "summary_cn": "获取轮询时间（毫秒）"}, {"methodName": "setPollMs", "signature": "public void setPollMs(long pollMs)", "summary_en": "Set the poll time in milliseconds", "summary_cn": "设置轮询时间（毫秒）"}, {"methodName": "getPullBeginTime", "signature": "public long getPullBeginTime()", "summary_en": "Get the pull begin time", "summary_cn": "获取拉取开始时间"}, {"methodName": "setPullBeginTime", "signature": "public void setPullBeginTime(long pullBeginTime)", "summary_en": "Set the pull begin time", "summary_cn": "设置拉取开始时间"}, {"methodName": "getFetchMax", "signature": "public int getFetchMax()", "summary_en": "Get the maximum fetch size", "summary_cn": "获取最大拉取数量"}, {"methodName": "setFetchMax", "signature": "public void setFetchMax(int fetchMax)", "summary_en": "Set the maximum fetch size", "summary_cn": "设置最大拉取数量"}], "HandlerInterceptor": [{"methodName": "beforeHandRequest", "signature": "boolean beforeHandRequest(RestRequest restRequest, RestChannel restChannel)", "summary_cn": "在Handler执行前调用，返回true继续执行后续Handler，返回false则终止执行", "summary_en": "Called before the handler execution. Returns true to continue with the handler chain, false to terminate."}, {"methodName": "afterHandRequest", "signature": "void afterHandRequest(RestRequest restRequest, RestChannel restChannel)", "summary_cn": "在Handler执行后调用，用于执行后续处理逻辑", "summary_en": "Called after the handler execution, used for post-processing logic."}], "Reporter": [{"methodName": "send", "signature": "void send(Collection<AlarmMsg> messages) throws Exception", "summary_en": "Send messages", "summary_cn": "发送消息"}], "OffsetCache": [{"methodName": "init", "signature": "public void init(HermesRuntime runtime)", "summary_en": "Initialize the OffsetCache with settings and schedule periodic flushing to Redis", "summary_cn": "初始化OffsetCache，设置参数并定时将缓存刷新到Redis"}, {"methodName": "putOffset", "signature": "public void putOffset(String topic, int partition, final long offset)", "summary_en": "Update the offset for a given topic and partition, keeping the maximum value", "summary_cn": "更新指定主题和分区的偏移量，保留最大值"}, {"methodName": "flushCacheToRedis", "signature": "public void flushCacheToRedis()", "summary_en": "Flush the cached offsets to Redis, updating only if the new offset is greater", "summary_cn": "将缓存的偏移量刷新到Redis，仅在新偏移量更大时更新"}, {"methodName": "close", "signature": "public void close() throws Exception", "summary_en": "Close the OffsetCache, flush remaining offsets to Redis and shutdown the executor", "summary_cn": "关闭OffsetCache，将剩余偏移量刷新到Redis并关闭执行器"}, {"methodName": "stop", "signature": "public void stop(HermesRuntime runtime) throws Exception", "summary_en": "Stop the OffsetCache by closing it", "summary_cn": "通过关闭操作停止OffsetCache"}, {"methodName": "order", "signature": "public int order()", "summary_en": "Get the stop order level of the OffsetCache", "summary_cn": "获取OffsetCache的停止优先级"}], "Stoppable": [{"methodName": "prepareStop", "signature": "default void prepareStop(HermesRuntime runtime) throws Exception", "summary_en": "Prepare to stop the component with the given HermesRuntime.", "summary_cn": "准备停止组件，使用给定的HermesRuntime。"}, {"methodName": "stop", "signature": "void stop(HermesRuntime runtime) throws Exception", "summary_en": "Stop the component with the given HermesRuntime.", "summary_cn": "停止组件，使用给定的HermesRuntime。"}], "ISettings": [{"methodName": "putAll", "signature": "void putAll(Map<Object, Object> map)", "summary_en": "Add all key-value pairs from the specified map to the settings", "summary_cn": "将指定映射中的所有键值对添加到设置中"}, {"methodName": "set", "signature": "void set(String key, Object value)", "summary_en": "Set the value for the specified key", "summary_cn": "为指定的键设置值"}, {"methodName": "get", "signature": "Optional<Object> get(String key)", "summary_en": "Get the value associated with the specified key, wrapped in an Optional", "summary_cn": "获取与指定键关联的值，包装在Optional中"}, {"methodName": "getString", "signature": "String getString(String key)", "summary_en": "Get the string value associated with the specified key", "summary_cn": "获取与指定键关联的字符串值"}, {"methodName": "<PERSON><PERSON><PERSON>", "signature": "boolean containsKey(String key)", "summary_en": "Check if the settings contain the specified key", "summary_cn": "检查设置中是否包含指定的键"}, {"methodName": "getLong", "signature": "Long getLong(String key)", "summary_en": "Get the long value associated with the specified key", "summary_cn": "获取与指定键关联的长整型值"}, {"methodName": "getLong", "signature": "Long getLong(String key, Long defaultValue)", "summary_en": "Get the long value associated with the specified key, or a default value if the key is not found", "summary_cn": "获取与指定键关联的长整型值，如果键不存在则返回默认值"}, {"methodName": "getInt", "signature": "Integer getInt(String key)", "summary_en": "Get the integer value associated with the specified key", "summary_cn": "获取与指定键关联的整数值"}, {"methodName": "getInt", "signature": "Integer getInt(String key, Integer defaultValue)", "summary_en": "Get the integer value associated with the specified key, or a default value if the key is not found", "summary_cn": "获取与指定键关联的整数值，如果键不存在则返回默认值"}, {"methodName": "getBoolean", "signature": "Boolean getBoolean(String key)", "summary_en": "Get the boolean value associated with the specified key", "summary_cn": "获取与指定键关联的布尔值"}, {"methodName": "getBoolean", "signature": "Boolean getBoolean(String key, Boolean defaultValue)", "summary_en": "Get the boolean value associated with the specified key, or a default value if the key is not found", "summary_cn": "获取与指定键关联的布尔值，如果键不存在则返回默认值"}, {"methodName": "getEnum", "signature": "<T extends Enum> T getEnum(Class<T> enumClass, String key)", "summary_en": "Get the enum value associated with the specified key", "summary_cn": "获取与指定键关联的枚举值"}, {"methodName": "getEnum", "signature": "<T extends Enum> T getEnum(Class<T> enumClass, String key, T defaultValue)", "summary_en": "Get the enum value associated with the specified key, or a default value if the key is not found", "summary_cn": "获取与指定键关联的枚举值，如果键不存在则返回默认值"}, {"methodName": "remove", "signature": "Object remove(String key)", "summary_en": "Remove the value associated with the specified key", "summary_cn": "移除与指定键关联的值"}, {"methodName": "getAllKeys", "signature": "List<String> getAllKeys()", "summary_en": "Get all keys in the settings", "summary_cn": "获取设置中的所有键"}], "RedisLock": [{"methodName": "tryLock", "signature": "public String tryLock(String monitor, long maxOperationTime)", "summary_cn": "尝试获取锁，只有当锁不存在时才成功。成功时返回生成的UUID，否则返回null。", "summary_en": "Attempt to acquire a lock, only successful if the lock does not exist. Returns the generated UUID on success, otherwise returns null."}, {"methodName": "tryLock", "signature": "public boolean tryLock(String monitor, String monitorVal, long maxOperationTime)", "summary_cn": "尝试获取锁，只有当锁不存在时才成功。成功时返回true，否则返回false。", "summary_en": "Attempt to acquire a lock, only successful if the lock does not exist. Returns true on success, otherwise returns false."}, {"methodName": "tryEnter", "signature": "public boolean tryEnter(String monitor, String monitorVal, long maxOperationTime)", "summary_cn": "尝试获取锁（重入），只有当锁存在时才成功。成功时返回true，否则返回false。", "summary_en": "Attempt to acquire a lock (reentrant), only successful if the lock exists. Returns true on success, otherwise returns false."}, {"methodName": "releaseLock", "signature": "public boolean releaseLock(String monitor, String monitorVal)", "summary_cn": "释放锁，只有当锁的内容匹配时才成功。成功时返回true，否则返回false。", "summary_en": "Release the lock, only successful if the lock content matches. Returns true on success, otherwise returns false."}], "RestRequest": [{"methodName": "header", "signature": "String header(String name)", "summary_cn": "获取指定名称的请求头值", "summary_en": "Get the value of the specified request header"}, {"methodName": "headers", "signature": "List<String> headers(String name)", "summary_cn": "获取指定名称的所有请求头值列表", "summary_en": "Get all values of the specified request header as a list"}, {"methodName": "method", "signature": "Method method()", "summary_cn": "获取HTTP请求方法", "summary_en": "Get the HTTP request method"}, {"methodName": "uri", "signature": "String uri()", "summary_cn": "获取包含查询字符串的URI", "summary_en": "Get the URI with the query string"}, {"methodName": "rawPath", "signature": "String rawPath()", "summary_cn": "获取未解码的原始路径", "summary_en": "Get the non-decoded, raw path"}, {"methodName": "path", "signature": "String path()", "summary_cn": "获取解码后的URI路径部分（不包含查询字符串）", "summary_en": "Get the decoded path part of the URI (without the query string)"}, {"methodName": "clientIp", "signature": "String clientIp()", "summary_cn": "获取客户端IP地址", "summary_en": "Get the client IP address"}, {"methodName": "getContentType", "signature": "ContentType getContentType()", "summary_cn": "获取请求的内容类型", "summary_en": "Get the content type of the request"}, {"methodName": "param", "signature": "String param(String key)", "summary_cn": "获取指定键的请求参数值", "summary_en": "Get the value of the specified request parameter"}, {"methodName": "param", "signature": "String param(String key, String defaultValue)", "summary_cn": "获取指定键的请求参数值，若不存在则返回默认值", "summary_en": "Get the value of the specified request parameter, or return the default value if it does not exist"}, {"methodName": "paramAsInt", "signature": "int paramAsInt(String key, int defaultValue)", "summary_cn": "获取指定键的请求参数值并转换为整数，若不存在则返回默认值", "summary_en": "Get the value of the specified request parameter as an integer, or return the default value if it does not exist"}, {"methodName": "paramAsLong", "signature": "long paramAsLong(String key, long defaultValue)", "summary_cn": "获取指定键的请求参数值并转换为长整数，若不存在则返回默认值", "summary_en": "Get the value of the specified request parameter as a long, or return the default value if it does not exist"}, {"methodName": "paramAsObject", "signature": "<T> T paramAsObject(Class<T> type)", "summary_cn": "将请求参数转换为指定类型的对象", "summary_en": "Convert the request parameters to an object of the specified type"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "boolean hasContent()", "summary_cn": "判断请求是否有内容", "summary_en": "Check if the request has content"}, {"methodName": "contentAsObject", "signature": "<T> T contentAsObject(Class<T> type)", "summary_cn": "将请求内容转换为指定类型的对象", "summary_en": "Convert the request content to an object of the specified type"}, {"methodName": "contentAsList", "signature": "<T> List<T> contentAsList(Class<T> type)", "summary_cn": "将请求内容转换为指定类型的对象列表", "summary_en": "Convert the request content to a list of objects of the specified type"}, {"methodName": "contentAsString", "signature": "String contentAsString()", "summary_cn": "获取请求内容的字符串表示", "summary_en": "Get the string representation of the request content"}], "HandlerInterceptorRegistrar": [{"methodName": "registerHandlerInterceptor", "signature": "void registerHandlerInterceptor(HandlerInterceptorRegistry registry)", "summary_en": "Register a HandlerInterceptor", "summary_cn": "注册一个HandlerInterceptor"}], "FetchModule": [{"methodName": "doConfigure", "signature": "protected void doConfigure()", "summary_en": "Configures the bindings for various fetcher and scheduler implementations.", "summary_cn": "配置各种抓取器和调度器的实现绑定。"}], "StartListener": [{"methodName": "onStart", "signature": "void onStart(HermesRuntime runtime) throws Exception", "summary_en": "Callback method invoked when <PERSON><PERSON> runtime starts", "summary_cn": "当Hermes运行时启动时调用的回调方法"}], "OffsetOutOfRangeException": [{"methodName": "OffsetOutOfRangeException", "signature": "public OffsetOutOfRangeException(String message, Throwable cause)", "summary_en": "Constructs a new exception with the specified detail message and cause", "summary_cn": "构造一个新的异常，包含指定的详细消息和原因"}, {"methodName": "OffsetOutOfRangeException", "signature": "public OffsetOutOfRangeException(String message)", "summary_en": "Constructs a new exception with the specified detail message", "summary_cn": "构造一个新的异常，包含指定的详细消息"}], "ThreadUtil": [{"methodName": "numberOfProcessors", "signature": "public static int numberOfProcessors()", "summary_en": "Get the number of available processors", "summary_cn": "获取可用的处理器数量"}, {"methodName": "shutdownExecutor", "signature": "public static void shutdownExecutor(String name, ExecutorService executorService, int waitSec) throws InterruptedException", "summary_en": "Shutdown the ExecutorService and wait for termination", "summary_cn": "关闭ExecutorService并等待终止"}, {"methodName": "threadName", "signature": "public static String threadName(final String namePrefix)", "summary_en": "Generate a thread name with the given prefix", "summary_cn": "生成带有给定前缀的线程名称"}, {"methodName": "daemonThreadFactory", "signature": "public static ThreadFactory daemonThreadFactory(String namePrefix)", "summary_en": "Create a daemon ThreadFactory with the given name prefix", "summary_cn": "创建一个带有给定名称前缀的守护线程工厂"}], "FilterLoader": [{"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "public FilterLoader(Loader... loaders)", "summary_en": "Constructs a FilterLoader with an array of Loaders.", "summary_cn": "使用Loader数组构造FilterLoader。"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "public FilterLoader(List<Loader> loaderList)", "summary_en": "Constructs a FilterLoader with a list of Loaders.", "summary_cn": "使用Loader列表构造FilterLoader。"}, {"methodName": "load", "signature": "public void load(ISettings settings)", "summary_en": "Loads settings by delegating to each Loader in the loaderList.", "summary_cn": "通过委托给loaderList中的每个Loader来加载设置。"}], "HttpCommand": [{"methodName": "execute", "signature": "public T execute(V v, int retryCnt)", "summary_cn": "执行HTTP请求，支持重试机制", "summary_en": "Execute HTTP request with retry mechanism"}, {"methodName": "execute", "signature": "public T execute(V v)", "summary_cn": "执行HTTP请求并处理响应", "summary_en": "Execute HTTP request and process response"}, {"methodName": "buildUrl", "signature": "protected abstract String buildUrl(V v)", "summary_cn": "构建请求URL", "summary_en": "Build request URL"}, {"methodName": "parseData", "signature": "protected abstract T parseData(JSONObject rootJson)", "summary_cn": "解析响应数据", "summary_en": "Parse response data"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "public void setHeader(String key, String value)", "summary_cn": "设置HTTP请求头", "summary_en": "Set HTTP request header"}], "ApiException": [{"methodName": "ApiException", "signature": "public ApiException(String message, Throwable cause)", "summary_en": "Constructs a new API exception with the specified detail message and cause.", "summary_cn": "构造一个新的API异常，包含指定的详细消息和原因。"}, {"methodName": "ApiException", "signature": "public ApiException(String message)", "summary_en": "Constructs a new API exception with the specified detail message.", "summary_cn": "构造一个新的API异常，包含指定的详细消息。"}, {"methodName": "ApiException", "signature": "public ApiException(Throwable cause)", "summary_en": "Constructs a new API exception with the specified cause.", "summary_cn": "构造一个新的API异常，包含指定的原因。"}, {"methodName": "ApiException", "signature": "public ApiException()", "summary_en": "Constructs a new API exception with no detail message or cause.", "summary_cn": "构造一个新的API异常，不包含详细消息或原因。"}, {"methodName": "fillInStackTrace", "signature": "public Throwable fillInStackTrace()", "summary_en": "Avoids the expensive and useless stack trace for API exceptions by returning this instance.", "summary_cn": "通过返回当前实例，避免为API异常生成昂贵且无用的堆栈跟踪。"}], "ZkLock": [{"methodName": "tryLock", "signature": "public boolean tryLock(String monitor, String monitorVal, long maxOperationTime)", "summary_en": "Attempt to acquire a lock on the specified monitor with the given value and maximum operation time.", "summary_cn": "尝试获取指定监控器的锁，使用给定的值和最大操作时间。"}, {"methodName": "tryEnter", "signature": "public boolean tryEnter(String monitor, String monitorVal, long maxOperationTime)", "summary_en": "Attempt to enter the specified monitor with the given value and maximum operation time. Unlike Redis, it returns success if the monitor does not exist or has timed out.", "summary_cn": "尝试进入指定监控器，使用给定的值和最大操作时间。与Redis不同，如果监控器不存在或已超时，也会返回成功。"}, {"methodName": "releaseLock", "signature": "public boolean releaseLock(String monitor, String monitorVal)", "summary_en": "Release the lock on the specified monitor with the given value. Returns true even if the resource is not locked or has already been released.", "summary_cn": "释放指定监控器的锁，使用给定的值。即使资源未锁定或已释放，也会返回true。"}], "HandlerInterceptorInfo": [{"methodName": "HandlerInterceptorInfo", "signature": "public HandlerInterceptorInfo(HandlerInterceptor handlerInterceptor)", "summary_cn": "构造方法，初始化HandlerInterceptorInfo实例并设置handlerInterceptor", "summary_en": "Constructor, initializes HandlerInterceptorInfo instance and sets handlerInterceptor"}, {"methodName": "addPathPattern", "signature": "public HandlerInterceptorInfo addPathPattern(String regex)", "summary_cn": "添加路径匹配模式，将正则表达式编译为Pattern并添加到includePatterns列表中", "summary_en": "Adds a path pattern, compiles the regex into a Pattern and adds it to the includePatterns list"}, {"methodName": "order", "signature": "public HandlerInterceptorInfo order(Integer order)", "summary_cn": "设置拦截器的执行顺序，并返回当前实例", "summary_en": "Sets the interceptor's execution order and returns the current instance"}, {"methodName": "order", "signature": "public Integer order()", "summary_cn": "获取拦截器的执行顺序", "summary_en": "Gets the interceptor's execution order"}, {"methodName": "getIncludePatterns", "signature": "public List<Pattern> getIncludePatterns()", "summary_cn": "获取包含的路径匹配模式列表", "summary_en": "Gets the list of included path patterns"}, {"methodName": "getHandlerInterceptor", "signature": "public HandlerInterceptor getHandlerInterceptor()", "summary_cn": "获取当前实例关联的HandlerInterceptor", "summary_en": "Gets the HandlerInterceptor associated with the current instance"}], "OffsetIgnoreException": [{"methodName": "OffsetIgnoreException", "signature": "public OffsetIgnoreException()", "summary_en": "Constructs a new OffsetIgnoreException with null as its detail message", "summary_cn": "构造一个新的OffsetIgnoreException异常，详细信息为null"}, {"methodName": "OffsetIgnoreException", "signature": "public OffsetIgnoreException(String message, Throwable cause)", "summary_en": "Constructs a new OffsetIgnoreException with the specified detail message and cause", "summary_cn": "构造一个新的OffsetIgnoreException异常，包含指定的详细信息和原因"}, {"methodName": "OffsetIgnoreException", "signature": "public OffsetIgnoreException(String message)", "summary_en": "Constructs a new OffsetIgnoreException with the specified detail message", "summary_cn": "构造一个新的OffsetIgnoreException异常，包含指定的详细信息"}], "Task": [{"methodName": "Task", "signature": "public Task(String type, Req payload, ActionListener<Res> actionListener, int timeoutMs)", "summary_en": "Constructor to initialize a Task with type, payload, action listener and timeout", "summary_cn": "构造函数，用于初始化一个带有类型、负载、动作监听器和超时时间的任务"}, {"methodName": "timeout", "signature": "public boolean timeout()", "summary_en": "Check if the task has timed out or been aborted", "summary_cn": "检查任务是否已超时或被中止"}, {"methodName": "isExecuted", "signature": "public boolean isExecuted()", "summary_en": "Check if the task has been executed at least once", "summary_cn": "检查任务是否至少执行过一次"}, {"methodName": "incrementExecutedCount", "signature": "public void incrementExecutedCount()", "summary_en": "Increment the execution count of the task", "summary_cn": "增加任务的执行次数"}, {"methodName": "getExecutedCount", "signature": "public int getExecutedCount()", "summary_en": "Get the current execution count of the task", "summary_cn": "获取任务的当前执行次数"}, {"methodName": "complete", "signature": "public void complete()", "summary_en": "Mark the task as completed and notify the task listener", "summary_cn": "将任务标记为完成并通知任务监听器"}, {"methodName": "process", "signature": "public void process()", "summary_en": "Mark the task as running", "summary_cn": "将任务标记为运行中"}, {"methodName": "idle", "signature": "public void idle()", "summary_en": "Mark the task as idle", "summary_cn": "将任务标记为空闲"}, {"methodName": "idleLock", "signature": "public void idleLock()", "summary_en": "Mark the task as idle with lock", "summary_cn": "将任务标记为带锁的空闲状态"}, {"methodName": "isComplete", "signature": "public boolean isComplete()", "summary_en": "Check if the task is completed", "summary_cn": "检查任务是否已完成"}, {"methodName": "toString", "signature": "public String toString()", "summary_en": "Get a string representation of the task including payload, status and start time", "summary_cn": "获取任务的字符串表示，包括负载、状态和开始时间"}], "ProduceModule": [{"methodName": "doConfigure", "signature": "protected void doConfigure() throws Exception", "summary_en": "Configures the module by binding OffsetCache and ProduceService to ProduceServiceImpl", "summary_cn": "配置模块，将OffsetCache和ProduceService绑定到ProduceServiceImpl"}], "AckResult": [{"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "public AckResult()", "summary_en": "De<PERSON>ult constructor for AckResult", "summary_cn": "AckResult的默认构造函数"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "public AckResult(boolean released)", "summary_en": "Constructor for AckResult with released parameter", "summary_cn": "带有released参数的AckResult构造函数"}], "ConsumerVo": [{"methodName": "getFetcherMonitor", "signature": "public String getFetcherMonitor()", "summary_en": "Get the monitor information of the fetcher", "summary_cn": "获取fetcher的监控信息"}, {"methodName": "toString", "signature": "public String toString()", "summary_en": "Get the string representation of the consumer", "summary_cn": "获取消费者的字符串表示"}], "ClassPathLoader": [{"methodName": "ClassPathLoader", "signature": "public ClassPathLoader(String configFileName)", "summary_en": "Constructor for ClassPathLoader with specified configuration file name", "summary_cn": "使用指定的配置文件名构造ClassPathLoader"}, {"methodName": "load", "signature": "public void load(ISettings settings)", "summary_en": "Load settings from the configuration file specified in the constructor and apply them to the provided ISettings object", "summary_cn": "从构造函数中指定的配置文件中加载设置，并将其应用到提供的ISettings对象中"}], "ConsumerWrapper": [{"methodName": "pull", "signature": "BatchMessage pull(PullParam pullParam)", "summary_en": "Pull messages based on the given pull parameters", "summary_cn": "根据给定的拉取参数拉取消息"}, {"methodName": "getBeginOffset", "signature": "long getBeginOffset(TopicPartition tp)", "summary_en": "Get the beginning offset of the specified topic partition", "summary_cn": "获取指定主题分区的起始偏移量"}, {"methodName": "getEndOffset", "signature": "long getEndOffset(TopicPartition tp)", "summary_en": "Get the end offset of the specified topic partition", "summary_cn": "获取指定主题分区的结束偏移量"}, {"methodName": "getBeginOffsetByTimestamp", "signature": "long getBeginOffsetByTimestamp(TopicPartition tp, Long timestamp)", "summary_en": "Get the beginning offset of the specified topic partition by timestamp", "summary_cn": "根据时间戳获取指定主题分区的起始偏移量"}], "ProduceService": [{"methodName": "send", "signature": "MessageMeta send(String ack, HermesMessage message, long timeout)", "summary_en": "Send a single message with specified acknowledgment and timeout", "summary_cn": "单条发送消息，带有指定的确认和超时设置"}, {"methodName": "sendBatchByOneFlush", "signature": "BatchMessageMeta sendBatchByOneFlush(String ack, List<HermesMessage> messages, long timeout)", "summary_en": "Send a batch of messages with one flush operation, with specified acknowledgment and timeout", "summary_cn": "批量发送消息，底层使用一次性flush操作，带有指定的确认和超时设置"}, {"methodName": "sendBatch", "signature": "BatchMessageMeta sendBatch(String ack, List<HermesMessage> messages, long timeout)", "summary_en": "Send a batch of messages by iteratively calling single send, with specified acknowledgment and timeout", "summary_cn": "批量发送消息，底层通过循环调用单条发送实现，带有指定的确认和超时设置"}], "ZkOffsetStorage": [{"methodName": "getOffsetPath", "signature": "public String getOffsetPath(ConsumerVo fetcherVo)", "summary_en": "Generate the Zookeeper path for storing offset based on the consumer information", "summary_cn": "根据消费者信息生成用于存储偏移量的Zookeeper路径"}, {"methodName": "getOffset", "signature": "public long getOffset(ConsumerVo consumerVo)", "summary_en": "Retrieve the offset from Zookeeper for the specified consumer", "summary_cn": "从Zookeeper中获取指定消费者的偏移量"}, {"methodName": "storeOffset", "signature": "public void storeOffset(ConsumerVo consumerVo, long offset)", "summary_en": "Store the offset in Zookeeper for the specified consumer", "summary_cn": "将指定消费者的偏移量存储到Zookeeper中"}, {"methodName": "storeOffsetByPath", "signature": "public void storeOffsetByPath(String path, long offset)", "summary_en": "Store the offset in Zookeeper at the specified path using CAS operation", "summary_cn": "使用CAS操作将偏移量存储到Zookeeper的指定路径中"}, {"methodName": "resetOffset", "signature": "public void resetOffset(ConsumerVo consumerVo, long offset)", "summary_en": "Reset the offset in Zookeeper for the specified consumer, create node if not exists", "summary_cn": "重置指定消费者在Zookeeper中的偏移量，如果节点不存在则创建"}], "ProducerWrapper": [{"methodName": "asyncSend", "signature": "MessageMeta asyncSend(HermesMessage message, ActionListener<MessageMeta> actionListener)", "summary_cn": "异步发送消息，不关心消息是否发送成功，配合参数ack=0", "summary_en": "Asynchronously send a message without caring whether the message is sent successfully, matching parameter ack=0"}, {"methodName": "asyncSend", "signature": "BatchMessageMeta asyncSend(List<HermesMessage> hermesMessageList, ActionListener<MessageMeta> actionListener)", "summary_cn": "异步批量发送消息", "summary_en": "Asynchronously send a batch of messages"}, {"methodName": "syncSend", "signature": "MessageMeta syncSend(HermesMessage message, long timeout, ActionListener<MessageMeta> actionListener)", "summary_cn": "同步发送消息", "summary_en": "Synchronously send a message"}, {"methodName": "syncSend", "signature": "BatchMessageMeta syncSend(List<HermesMessage> messages, long timeout, ActionListener<MessageMeta> actionListener)", "summary_cn": "同步批量发送消息", "summary_en": "Synchronously send a batch of messages"}], "HttpServerTransport": [{"methodName": "dispatchRequest", "signature": "void dispatchRequest(RestRequest request, RestChannel channel)", "summary_en": "Dispatch the request to the appropriate handler", "summary_cn": "将请求分发到适当的处理器"}], "KafkaClientBuilder": [{"methodName": "createKafkaAdminClient", "signature": "public static KafkaAdminClient createKafkaAdminClient(String brokerList)", "summary_en": "Creates a KafkaAdminClient with the specified broker list and default timeout settings.", "summary_cn": "创建一个KafkaAdminClient，使用指定的broker列表和默认超时设置。"}, {"methodName": "createKafkaConsumer", "signature": "public static KafkaConsumer createKafkaConsumer(String brokerList)", "summary_en": "Creates a KafkaConsumer with the specified broker list, byte array deserializers, and default fetch settings.", "summary_cn": "创建一个KafkaConsumer，使用指定的broker列表、字节数组反序列化器和默认的fetch设置。"}, {"methodName": "createKafkaProducer", "signature": "public static KafkaProducer createKafkaProducer(String brokerList, String ack, String flightCount)", "summary_en": "Creates a KafkaProducer with the specified broker list, acknowledgment mode, and in-flight request settings.", "summary_cn": "创建一个KafkaProducer，使用指定的broker列表、确认模式和飞行请求设置。"}], "Result": [{"methodName": "getCode", "signature": "public int getCode()", "summary_en": "Get the code value", "summary_cn": "获取code值"}, {"methodName": "setCode", "signature": "public void setCode(int code)", "summary_en": "Set the code value", "summary_cn": "设置code值"}, {"methodName": "getData", "signature": "public Object getData()", "summary_en": "Get the data object", "summary_cn": "获取data对象"}, {"methodName": "setData", "signature": "public void setData(Object data)", "summary_en": "Set the data object", "summary_cn": "设置data对象"}, {"methodName": "getError", "signature": "public String getError()", "summary_en": "Get the error message", "summary_cn": "获取错误信息"}, {"methodName": "setError", "signature": "public void setError(String error)", "summary_en": "Set the error message", "summary_cn": "设置错误信息"}], "StatusController": [{"methodName": "StatusController", "signature": "public StatusController(RestController controller)", "summary_en": "Constructor for StatusController with dependency injection", "summary_cn": "StatusController的构造函数，使用依赖注入"}, {"methodName": "status", "signature": "public void status(RestRequest restRequest, RestChannel restChannel) throws Exception", "summary_en": "Handles status request and responds with 'ok'", "summary_cn": "处理状态请求并返回'ok'响应"}, {"methodName": "online", "signature": "public void online(RestRequest restRequest, RestChannel restChannel) throws Exception", "summary_en": "Handles online request and responds with success status", "summary_cn": "处理上线请求并返回成功状态"}, {"methodName": "offline", "signature": "public void offline(RestRequest restRequest, RestChannel restChannel) throws Exception", "summary_en": "Handles offline request and responds with success status", "summary_cn": "处理下线请求并返回成功状态"}], "HttpServerBootstrap": [{"methodName": "HttpServerBootstrap", "signature": "public HttpServerBootstrap(Loader... loaders)", "summary_en": "Constructor with loaders", "summary_cn": "构造函数，传入加载器"}, {"methodName": "HttpServerBootstrap", "signature": "public HttpServerBootstrap(String configFile, Loader... loaders)", "summary_en": "Constructor with config file and loaders", "summary_cn": "构造函数，传入配置文件和加载器"}, {"methodName": "HttpServerBootstrap", "signature": "public HttpServerBootstrap(ISettings settings)", "summary_en": "Constructor with settings", "summary_cn": "构造函数，传入设置"}, {"methodName": "doStart", "signature": "public void doStart(HermesRuntime hermesRuntime)", "summary_en": "Start the HTTP server transport", "summary_cn": "启动HTTP服务器传输"}, {"methodName": "doStop", "signature": "public void doStop(HermesRuntime hermesRuntime)", "summary_en": "Stop the HTTP server transport", "summary_cn": "停止HTTP服务器传输"}], "Encoder": [{"methodName": "encode", "signature": "String encode(AlarmMsg messages) throws Exception", "summary_en": "Encode the alarm message into a specific format.", "summary_cn": "将告警消息编码为特定格式。"}], "HandlerInterceptorChain": [{"methodName": "HandlerInterceptorChain", "signature": "public HandlerInterceptorChain(MethodHandler methodHandler, List<HandlerInterceptor> handlerInterceptorList)", "summary_en": "Constructor to initialize HandlerInterceptorChain with methodH<PERSON><PERSON> and handlerInterceptorList", "summary_cn": "构造函数，使用methodHandler和handlerInterceptorList初始化HandlerInterceptorChain"}, {"methodName": "beforeHandRequest", "signature": "protected boolean beforeHandRequest(int pos, RestRequest restRequest, RestChannel restChannel)", "summary_en": "Recursively execute beforeHandRequest method of each interceptor in the list", "summary_cn": "递归执行拦截器列表中的每个拦截器的beforeHandRequest方法"}, {"methodName": "afterHandRequest", "signature": "protected void afterHandRequest(int pos, RestRequest restRequest, RestChannel restChannel)", "summary_en": "Recursively execute afterHandRequest method of each interceptor in the list", "summary_cn": "递归执行拦截器列表中的每个拦截器的afterHandRequest方法"}, {"methodName": "handRequest", "signature": "public void handRequest(RestRequest restRequest, RestChannel restChannel) throws Throwable", "summary_en": "Handle the request by executing interceptors and the actual handler", "summary_cn": "通过执行拦截器和实际处理器来处理请求"}], "Client": [{"methodName": "send", "signature": "void send(String ack, <PERSON>mes<PERSON>essage message, long timeout, ActionListener<MessageMeta> actionListener)", "summary_cn": "发送消息", "summary_en": "Send a message"}, {"methodName": "sendBatch", "signature": "void sendBatch(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener)", "summary_cn": "批量发送消息（一条条发送）", "summary_en": "Send messages in batch (one by one)"}, {"methodName": "sendBatchByOneFlush", "signature": "void sendBatchByOneFlush(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener)", "summary_cn": "批量发送消息（一次性flush）", "summary_en": "Send messages in batch (flush at once)"}, {"methodName": "fetch", "signature": "void fetch(FetchRequest fetchRequest, ActionListener<BatchMessage> actionListener)", "summary_cn": "拉取消息", "summary_en": "Fetch messages"}, {"methodName": "long<PERSON>etch", "signature": "void longFetch(FetchRequest fetchRequest, ActionListener<BatchMessage> actionListener)", "summary_cn": "拉取消息（长轮训方式）", "summary_en": "Fetch messages (long polling)"}, {"methodName": "ack", "signature": "void ack(AckRequest ackRequest, ActionListener<AckResult> actionListener)", "summary_cn": "确认消息", "summary_en": "Acknowledge messages"}, {"methodName": "renew", "signature": "void renew(LockRequest lockRequest, ActionListener<Boolean> actionListener)", "summary_cn": "锁续期", "summary_en": "Renew lock"}, {"methodName": "getConsumerOffset", "signature": "void getConsumerOffset(ConsumerVo consumerVo, ActionListener<ConsumerOffset> actionListener)", "summary_cn": "获取消费组的offset点位（按partition区分）", "summary_en": "Get consumer offset by partition"}, {"methodName": "getTopicOffsetRange", "signature": "void getTopicOffsetRange(TopicPartition tp, ActionListener<OffsetRange> actionListener)", "summary_cn": "获取topic的offset区间", "summary_en": "Get topic offset range"}, {"methodName": "resetOffset", "signature": "void resetOffset(ResetVo resetVo, long offset, ActionListener<Boolean> actionListener)", "summary_cn": "重置offset", "summary_en": "Reset offset"}, {"methodName": "getOffset", "signature": "void getOffset(ConsumerVo consumerVo, ActionListener<OffsetResp> actionListener)", "summary_cn": "获取消费组下某个分区的offset点位", "summary_en": "Get offset of a partition under a consumer group"}], "AccessLogInterceptor": [{"methodName": "beforeHandRequest", "signature": "public boolean beforeHandRequest(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Log the request path and client IP before handling the request.", "summary_cn": "在处理请求前记录请求路径和客户端IP。"}, {"methodName": "afterHandRequest", "signature": "public void afterHandRequest(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Log the request path and client IP after handling the request.", "summary_cn": "在处理请求后记录请求路径和客户端IP。"}, {"methodName": "registerHandlerInterceptor", "signature": "public void registerHandlerInterceptor(HandlerInterceptorRegistry registry)", "summary_en": "Register this interceptor with the given registry and set its order to 0.", "summary_cn": "将当前拦截器注册到给定的注册表中，并设置其顺序为0。"}], "OffsetController": [{"methodName": "range", "signature": "public void range(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Get the offset range for a topic partition", "summary_cn": "获取主题分区的偏移量范围"}, {"methodName": "reset", "signature": "public void reset(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Reset the offset for a topic partition", "summary_cn": "重置主题分区的偏移量"}, {"methodName": "consumer", "signature": "public void consumer(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Query the consumer offset for a topic partition", "summary_cn": "查询消费者在主题分区的偏移量"}, {"methodName": "query", "signature": "public void query(RestRequest restRequest, RestChannel restChannel) throws Exception", "summary_en": "Query the offset for a topic partition", "summary_cn": "查询主题分区的偏移量"}], "ZkClient": [{"methodName": "getData", "signature": "String getData(String path) throws Exception", "summary_en": "Get data from the specified path in Zoo<PERSON>eeper", "summary_cn": "从Zoo<PERSON>eeper中指定路径获取数据"}, {"methodName": "create", "signature": "void create(String path, String data) throws Exception", "summary_en": "Create a node with the specified path and data in Zoo<PERSON>eeper", "summary_cn": "在ZooKeeper中创建指定路径和数据的节点"}, {"methodName": "setData", "signature": "void setData(String path, String value) throws Exception", "summary_en": "Set data for the specified path in Zoo<PERSON>eeper", "summary_cn": "为Zoo<PERSON>eeper中指定路径设置数据"}, {"methodName": "casUpdate", "signature": "boolean casUpdate(String path, long offset)", "summary_en": "Compare-and-swap update with the specified offset for the given path", "summary_cn": "使用指定的偏移量对给定路径进行比较并交换更新"}, {"methodName": "casUpdate", "signature": "boolean casUpdate(String path, String oldVal, String newVal, long expired)", "summary_en": "Compare-and-swap update with the specified old value, new value, and expiration time for the given path", "summary_cn": "使用指定的旧值、新值和过期时间对给定路径进行比较并交换更新"}], "BootstrapAware": [{"methodName": "setBootstrap", "signature": "void setBootstrap(Bootstrap bootstrap)", "summary_en": "Set the Bootstrap instance for the current context", "summary_cn": "设置当前上下文的Bootstrap实例"}], "AdminModule": [{"methodName": "doConfigure", "signature": "protected void doConfigure()", "summary_en": "Configures the bindings for MetadataManager and OffsetStorage implementations.", "summary_cn": "配置MetadataManager和OffsetStorage实现的绑定。"}], "KafkaConsumerWrapper": [{"methodName": "KafkaConsumerWrapper", "signature": "public KafkaConsumerWrapper(Consumer<byte[], byte[]> kafkaConsumer)", "summary_en": "Constructor that initializes the Kafka consumer and fetches internal fields via reflection.", "summary_cn": "构造函数，初始化Kafka消费者并通过反射获取内部字段。"}, {"methodName": "pull", "signature": "public BatchMessage pull(PullParam pullParam)", "summary_en": "Pulls messages from Ka<PERSON><PERSON> based on the provided pull parameters, sets the max poll records, seeks to the specified offset, and converts the fetched records into a BatchMessage.", "summary_cn": "根据提供的拉取参数从Kafka拉取消息，设置最大拉取记录数，定位到指定偏移量，并将获取的记录转换为BatchMessage。"}, {"methodName": "getBeginOffset", "signature": "public long getBeginOffset(TopicPartition tp)", "summary_en": "Retrieves the beginning offset for the specified topic partition.", "summary_cn": "获取指定主题分区的起始偏移量。"}, {"methodName": "getEndOffset", "signature": "public long getEndOffset(TopicPartition tp)", "summary_en": "Retrieves the end offset for the specified topic partition.", "summary_cn": "获取指定主题分区的结束偏移量。"}, {"methodName": "getBeginOffsetByTimestamp", "signature": "public long getBeginOffsetByTimestamp(TopicPartition tp, Long timestamp)", "summary_en": "Retrieves the beginning offset for the specified topic partition based on the given timestamp.", "summary_cn": "根据给定的时间戳获取指定主题分区的起始偏移量。"}], "QueryTokenCommand": [{"methodName": "buildUrl", "signature": "public String buildUrl(String centerUrl)", "summary_en": "Build the complete URL by appending the endpoint to the center URL", "summary_cn": "通过将端点附加到中心URL来构建完整的URL"}, {"methodName": "parseData", "signature": "public Map<String, String> parseData(JSONObject rootJson)", "summary_en": "Parse the JSON data and extract the 'data' field as a Map", "summary_cn": "解析JSON数据并提取'data'字段作为Map返回"}], "MetadataManagerImpl": [{"methodName": "getPartitionNum", "signature": "public int getPartitionNum(String topic)", "summary_cn": "获取指定主题的分区数量", "summary_en": "Get the number of partitions for the specified topic"}, {"methodName": "getTopicOffsetRange", "signature": "public OffsetRange getTopicOffsetRange(TopicPartition topicPartition)", "summary_cn": "获取指定主题分区的偏移量范围", "summary_en": "Get the offset range for the specified topic partition"}, {"methodName": "resetOffset", "signature": "public boolean resetOffset(ConsumerVo consumerVo, long offset)", "summary_cn": "重置消费者的偏移量", "summary_en": "Reset the offset for the consumer"}, {"methodName": "isNoProducer", "signature": "public boolean isNoProducer(String topic)", "summary_cn": "判断指定主题是否有生产者", "summary_en": "Check if the specified topic has no producer"}, {"methodName": "getTopicInfo", "signature": "public TopicInfo getTopicInfo(String topic)", "summary_cn": "获取指定主题的详细信息", "summary_en": "Get detailed information for the specified topic"}, {"methodName": "getConsumerOffset", "signature": "public ConsumerOffset getConsumerOffset(ConsumerVo consumer)", "summary_cn": "获取消费者的偏移量信息", "summary_en": "Get the offset information for the consumer"}, {"methodName": "getOffset", "signature": "public OffsetResp getOffset(ConsumerVo consumer)", "summary_cn": "获取消费者的偏移量响应", "summary_en": "Get the offset response for the consumer"}, {"methodName": "stop", "signature": "public void stop(HermesRuntime runtime) throws Exception", "summary_cn": "停止分区同步和无生产者主题同步", "summary_en": "Stop partition sync and no producer topic sync"}, {"methodName": "order", "signature": "public int order()", "summary_cn": "获取当前类的优先级顺序", "summary_en": "Get the priority order of the current class"}], "ZkLockFetcher": [{"methodName": "ZkLockFetcher", "signature": "public ZkLockFetcher(ISettings settings)", "summary_en": "Constructor for ZkLockFetcher with settings", "summary_cn": "ZkLockFetcher的构造函数，接收设置参数"}, {"methodName": "tryLock0", "signature": "protected boolean tryLock0(ConsumerVo consumerVo)", "summary_en": "Attempt to acquire a lock for the given consumer", "summary_cn": "尝试为给定的消费者获取锁"}, {"methodName": "releaseLock0", "signature": "public boolean releaseLock0(ConsumerVo consumerVo)", "summary_en": "Release the lock for the given consumer", "summary_cn": "释放给定消费者的锁"}, {"methodName": "rewriteFetchRequest", "signature": "protected void rewriteFetchRequest(FetchRequest fetchRequest)", "summary_en": "Rewrite the fetch request, setting partition if not set", "summary_cn": "重写获取请求，如果未设置分区则设置分区"}, {"methodName": "getOffsetStorage", "signature": "protected OffsetStorage getOffsetStorage()", "summary_en": "Get the offset storage instance", "summary_cn": "获取偏移量存储实例"}], "IllegalParameterException": [{"methodName": "IllegalParameterException", "signature": "public IllegalParameterException(String message, Throwable cause)", "summary_cn": "构造一个带有详细消息和原因的非法参数异常", "summary_en": "Constructs an IllegalParameterException with the specified detail message and cause"}, {"methodName": "IllegalParameterException", "signature": "public IllegalParameterException(String message)", "summary_cn": "构造一个带有详细消息的非法参数异常", "summary_en": "Constructs an IllegalParameterException with the specified detail message"}], "UnknownServerException": [{"methodName": "UnknownServerException", "signature": "public UnknownServerException()", "summary_en": "Constructs a new UnknownServerException with no detail message", "summary_cn": "构造一个新的UnknownServerException实例，不带详细消息"}, {"methodName": "UnknownServerException", "signature": "public UnknownServerException(String message)", "summary_en": "Constructs a new UnknownServerException with the specified detail message", "summary_cn": "构造一个新的UnknownServerException实例，带有指定的详细消息"}, {"methodName": "UnknownServerException", "signature": "public UnknownServerException(Throwable cause)", "summary_en": "Constructs a new UnknownServerException with the specified cause", "summary_cn": "构造一个新的UnknownServerException实例，带有指定的原因"}, {"methodName": "UnknownServerException", "signature": "public UnknownServerException(String message, Throwable cause)", "summary_en": "Constructs a new UnknownServerException with the specified detail message and cause", "summary_cn": "构造一个新的UnknownServerException实例，带有指定的详细消息和原因"}], "FilePathLoader": [{"methodName": "FilePath<PERSON><PERSON>der", "signature": "public FilePathLoader(String configFileName)", "summary_en": "Constructor that initializes the loader with a configuration file name.", "summary_cn": "构造函数，使用配置文件名初始化加载器。"}, {"methodName": "load", "signature": "public void load(ISettings settings)", "summary_en": "Loads settings from a configuration file into the provided ISettings object. The file is read from the default config directory.", "summary_cn": "从默认配置目录中的配置文件加载设置到提供的 ISettings 对象中。"}], "Errors": [{"methodName": "forCode", "signature": "public static Errors forCode(int code)", "summary_cn": "根据错误码获取对应的错误枚举实例，若未找到则返回未知服务器错误", "summary_en": "Get the corresponding error enum instance based on the error code, return unknown server error if not found"}, {"methodName": "forException", "signature": "public static Errors forException(Throwable t)", "summary_cn": "根据异常或其父类获取对应的错误枚举实例，若未找到则返回null", "summary_en": "Get the corresponding error enum instance based on the exception or its superclass, return null if not found"}, {"methodName": "exception", "signature": "public ApiException exception()", "summary_cn": "获取当前错误枚举实例关联的异常实例", "summary_en": "Get the exception instance associated with the current error enum instance"}, {"methodName": "exception", "signature": "public ApiException exception(String message)", "summary_cn": "根据给定消息创建并返回异常实例，若消息为空则返回默认异常实例", "summary_en": "Create and return an exception instance with the given message, return the default exception instance if the message is empty"}, {"methodName": "exceptionName", "signature": "public String exceptionName()", "summary_cn": "获取当前错误枚举实例关联的异常类名，若无关联则返回null", "summary_en": "Get the class name of the exception associated with the current error enum instance, return null if there is none"}, {"methodName": "code", "signature": "public int code()", "summary_cn": "获取当前错误枚举实例的错误码", "summary_en": "Get the error code of the current error enum instance"}, {"methodName": "logType", "signature": "public LogType logType()", "summary_cn": "获取当前错误枚举实例的日志类型", "summary_en": "Get the log type of the current error enum instance"}, {"methodName": "maybeThrow", "signature": "public void maybeThrow()", "summary_cn": "若当前错误枚举实例关联异常则抛出该异常", "summary_en": "Throw the exception if the current error enum instance has one associated"}, {"methodName": "message", "signature": "public String message()", "summary_cn": "获取当前错误枚举实例的友好描述信息", "summary_en": "Get a friendly description of the current error enum instance"}], "ActionListener": [{"methodName": "onResponse", "signature": "void onResponse(R response)", "summary_en": "Called when a response is received", "summary_cn": "当收到响应时调用"}, {"methodName": "onException", "signature": "void onException(Exception e)", "summary_en": "Called when an exception occurs", "summary_cn": "当发生异常时调用"}, {"methodName": "wrap", "signature": "static <R> ActionListener<R> wrap(BiConsumer<R, Exception> consumer)", "summary_en": "Wraps a BiConsumer into an ActionListener", "summary_cn": "将BiConsumer包装成ActionListener"}, {"methodName": "wrap", "signature": "static <R> ActionListener<R> wrap(ActionListener<R> listener, Consumer<R> before)", "summary_en": "Wraps an ActionListener with a Consumer to be executed before the listener", "summary_cn": "包装一个ActionListener，并在监听器之前执行Consumer"}], "HermesCenterApplication": [{"methodName": "main", "signature": "public static void main(String[] args)", "summary_en": "Entry point of the HermesCenterApplication, initializes and starts the HTTP server with CoreModule and RestModule.", "summary_cn": "HermesCenterApplication的入口点，初始化并启动带有CoreModule和RestModule的HTTP服务器。"}], "CoreModule": [{"methodName": "doConfigure", "signature": "protected void doConfigure() throws Exception", "summary_en": "Configure the core module by binding health, thread pool, Redis, Zookeeper, and alert configurations.", "summary_cn": "配置核心模块，绑定健康检查、线程池、<PERSON><PERSON>、Zookeeper和告警配置。"}, {"methodName": "configRed<PERSON><PERSON><PERSON>", "signature": "protected void config<PERSON>ed<PERSON><PERSON><PERSON>() throws Exception", "summary_en": "Configure the alert system by setting up the Crow HTTP reporter and binding the alert instance.", "summary_cn": "配置告警系统，设置Crow HTTP报告器并绑定告警实例。"}, {"methodName": "setCommonProperties", "signature": "protected void setCommonProperties(ClusterConfig clusterConfig, ISettings settings)", "summary_en": "Set common properties for the Redis cluster configuration.", "summary_cn": "为Redis集群配置设置通用属性。"}, {"methodName": "config<PERSON><PERSON><PERSON>", "signature": "protected void configRedis() throws IOException", "summary_en": "Configure the Redis cluster based on settings and bind the RedisCluster instance.", "summary_cn": "根据配置设置Redis集群并绑定RedisCluster实例。"}, {"methodName": "configZk", "signature": "protected void configZk() throws IOException", "summary_en": "Configure the Zookeeper client based on settings and bind the ZkClient instance.", "summary_cn": "根据配置设置Zookeeper客户端并绑定ZkClient实例。"}], "NettyHttpRequest": [{"methodName": "NettyHttpRequest", "signature": "public NettyHttpRequest(FullHttpRequest request)", "summary_cn": "构造函数，通过FullHttpRequest初始化NettyHttpRequest对象", "summary_en": "Constructor, initializes NettyHttpRequest object with FullHttpRequest"}, {"methodName": "NettyHttpRequest", "signature": "public NettyHttpRequest(String uri, Map<String, List<String>> headers, ByteBuf content, HttpMethod httpMethod)", "summary_cn": "构造函数，通过URI、请求头、内容和HTTP方法初始化NettyHttpRequest对象", "summary_en": "Constru<PERSON>, initializes NettyHttpRequest object with URI, headers, content and HTTP method"}, {"methodName": "param", "signature": "public String param(String key)", "summary_cn": "获取指定键的请求参数值", "summary_en": "Get the request parameter value for the specified key"}, {"methodName": "param", "signature": "public String param(String key, String defaultValue)", "summary_cn": "获取指定键的请求参数值，若不存在则返回默认值", "summary_en": "Get the request parameter value for the specified key, return default value if not exists"}, {"methodName": "paramAsInt", "signature": "public int paramAsInt(String key, int defaultValue)", "summary_cn": "将指定键的请求参数值转为整数，若不存在或转换失败则返回默认值", "summary_en": "Convert the request parameter value for the specified key to integer, return default value if not exists or conversion fails"}, {"methodName": "paramAsLong", "signature": "public long paramAsLong(String key, long defaultValue)", "summary_cn": "将指定键的请求参数值转为长整数，若不存在或转换失败则返回默认值", "summary_en": "Convert the request parameter value for the specified key to long, return default value if not exists or conversion fails"}, {"methodName": "paramAsObject", "signature": "public <T> T paramAsObject(Class<T> type)", "summary_cn": "将请求参数转换为指定类型的对象", "summary_en": "Convert request parameters to an object of the specified type"}, {"methodName": "header", "signature": "public String header(String name)", "summary_cn": "获取指定名称的请求头值", "summary_en": "Get the request header value for the specified name"}, {"methodName": "headerContainsValue", "signature": "public boolean headerContainsValue(String name, String value)", "summary_cn": "检查请求头是否包含指定值", "summary_en": "Check if the request header contains the specified value"}, {"methodName": "headers", "signature": "public List<String> headers(String name)", "summary_cn": "获取指定名称的所有请求头值", "summary_en": "Get all request header values for the specified name"}, {"methodName": "clientIp", "signature": "public String clientIp()", "summary_cn": "获取客户端IP地址", "summary_en": "Get the client IP address"}, {"methodName": "path", "signature": "public String path()", "summary_cn": "获取解码后的URI路径部分（不包含查询字符串）", "summary_en": "Get the decoded URI path part (without query string)"}, {"methodName": "uri", "signature": "public String uri()", "summary_cn": "获取包含查询字符串的完整URI", "summary_en": "Get the complete URI with query string"}, {"methodName": "rawPath", "signature": "public String rawPath()", "summary_cn": "获取未解码的原始路径", "summary_en": "Get the undecoded raw path"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "public boolean hasContent()", "summary_cn": "检查请求是否包含内容", "summary_en": "Check if the request has content"}, {"methodName": "getContentType", "signature": "public ContentType getContentType()", "summary_cn": "获取请求的内容类型", "summary_en": "Get the content type of the request"}, {"methodName": "contentAsObject", "signature": "public <T> T contentAsObject(Class<T> type)", "summary_cn": "将请求内容转换为指定类型的对象", "summary_en": "Convert request content to an object of the specified type"}, {"methodName": "contentAsList", "signature": "public <T> List<T> contentAsList(Class<T> type)", "summary_cn": "将请求内容转换为指定类型的对象列表", "summary_en": "Convert request content to a list of objects of the specified type"}, {"methodName": "contentAsString", "signature": "public String contentAsString()", "summary_cn": "将请求内容转换为字符串", "summary_en": "Convert request content to string"}, {"methodName": "method", "signature": "public Method method()", "summary_cn": "获取HTTP请求方法", "summary_en": "Get the HTTP request method"}], "HermesProxyApplication": [{"methodName": "main", "signature": "public static void main(String[] args)", "summary_en": "The main entry point for the Hermes Proxy application. It initializes the HTTP server bootstrap, installs necessary modules, registers start and stop listeners, and starts the server.", "summary_cn": "Hermes Proxy 应用程序的主入口点。它初始化 HTTP 服务器引导程序，安装必要的模块，注册启动和停止监听器，并启动服务器。"}], "PartitionNotAssignException": [{"methodName": "PartitionNotAssignException", "signature": "public PartitionNotAssignException(String message, Throwable cause)", "summary_en": "Constructs a new PartitionNotAssignException with the specified detail message and cause.", "summary_cn": "构造一个新的PartitionNotAssignException实例，包含指定的详细消息和原因。"}, {"methodName": "PartitionNotAssignException", "signature": "public PartitionNotAssignException(String message)", "summary_en": "Constructs a new PartitionNotAssignException with the specified detail message.", "summary_cn": "构造一个新的PartitionNotAssignException实例，包含指定的详细消息。"}], "HealthHandler": [{"methodName": "checkReadiness", "signature": "public void checkReadiness(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Performs a readiness check and responds with health status", "summary_cn": "执行就绪检查并返回健康状态"}, {"methodName": "checkLiveness", "signature": "public void checkLiveness(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Performs a liveness check and responds with 'ok'", "summary_cn": "执行存活检查并返回'ok'"}, {"methodName": "prepareStop", "signature": "public void prepareStop(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Prepares the system for stopping and logs the request", "summary_cn": "准备停止系统并记录请求"}], "ProxyModule": [{"methodName": "doConfigure", "signature": "protected void doConfigure() throws Exception", "summary_en": "Configures the module by binding classes and setting up dependencies.", "summary_cn": "配置模块，绑定类并设置依赖关系。"}], "Timers": [{"methodName": "newTimeout", "signature": "public static TimeoutFuture newTimeout(TimeUnit timeUnit, int timeout, Runnable callback)", "summary_en": "Creates a new timeout task with the specified time unit, timeout duration, and callback.", "summary_cn": "创建一个新的超时任务，指定时间单位、超时时长和回调函数。"}, {"methodName": "newTimeout", "signature": "public TimeoutFuture newTimeout(TimeUnit timeUnit, int timeout, Run<PERSON><PERSON> callback)", "summary_en": "Creates a new timeout task with the specified time unit, timeout duration, and callback.", "summary_cn": "创建一个新的超时任务，指定时间单位、超时时长和回调函数。"}, {"methodName": "cancel", "signature": "public void cancel(DelayedTask delayedTask)", "summary_en": "Cancels the specified delayed task by removing it from the delay queue.", "summary_cn": "通过从延迟队列中移除指定的延迟任务来取消该任务。"}, {"methodName": "run", "signature": "public void run()", "summary_en": "Continuously takes and executes delayed tasks from the delay queue.", "summary_cn": "持续从延迟队列中取出并执行延迟任务。"}, {"methodName": "cancel", "signature": "public void cancel()", "summary_en": "Cancels the current timeout task by removing it from the delay queue.", "summary_cn": "通过从延迟队列中移除当前超时任务来取消该任务。"}, {"methodName": "compareTo", "signature": "public int compareTo(Delayed other)", "summary_en": "Compares this delayed task with another to determine the order of execution.", "summary_cn": "将此延迟任务与另一个延迟任务进行比较以确定执行顺序。"}, {"methodName": "get<PERSON>elay", "signature": "public long getDelay(TimeUnit unit)", "summary_en": "Calculates the remaining delay time for this task in the specified time unit.", "summary_cn": "计算此任务在指定时间单位下的剩余延迟时间。"}, {"methodName": "run", "signature": "public void run()", "summary_en": "Executes the callback function associated with this delayed task.", "summary_cn": "执行与此延迟任务关联的回调函数。"}], "AuthHandler": [{"methodName": "notNeedAuth", "signature": "public boolean notNeedAuth(String topic, String appName)", "summary_en": "Check if authentication is not required for the given topic and application name.", "summary_cn": "检查给定的主题和应用名称是否不需要认证。"}, {"methodName": "auth", "signature": "public void auth(String topic, String appName, String compactJws, String requestURI)", "summary_en": "Authenticate the request based on the topic, application name, JWT token, and request URI.", "summary_cn": "根据主题、应用名称、JWT令牌和请求URI对请求进行认证。"}, {"methodName": "checkWhiteApp", "signature": "private boolean checkWhiteApp(String appName, String whiteAppConfig)", "summary_en": "Check if the application name is in the whitelist configuration.", "summary_cn": "检查应用名称是否在白名单配置中。"}, {"methodName": "getSecretKeyByUserId", "signature": "public String getSecretKeyByUserId(String userId)", "summary_en": "Retrieve the secret key associated with the given user ID.", "summary_cn": "获取与给定用户ID关联的密钥。"}, {"methodName": "exist", "signature": "public boolean exist(String token)", "summary_en": "Check if the token exists in the cache and is still valid.", "summary_cn": "检查令牌是否存在于缓存中且仍然有效。"}, {"methodName": "put", "signature": "public void put(String token, Long exp)", "summary_en": "Store the token in the cache with its expiration time.", "summary_cn": "将令牌及其过期时间存储在缓存中。"}], "Message": [{"methodName": "getHeaderStringByKey", "signature": "public String getHeaderStringByKey(String headerKey)", "summary_en": "Get the header value as a string by the given key, returns empty string if not found", "summary_cn": "根据给定的键获取头信息的值（字符串形式），如果未找到则返回空字符串"}], "IndexController": [{"methodName": "IndexController", "signature": "public IndexController(RestController controller)", "summary_en": "Constructor for IndexController with dependency injection", "summary_cn": "IndexController的构造函数，使用依赖注入"}, {"methodName": "index", "signature": "public void index(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Handles the root path request and sends a success response", "summary_cn": "处理根路径请求并发送成功响应"}], "AuthException": [{"methodName": "AuthException", "signature": "public AuthException(String message, Throwable cause)", "summary_en": "Constructs a new authentication exception with the specified detail message and cause.", "summary_cn": "构造一个带有指定详细消息和原因的认证异常。"}, {"methodName": "AuthException", "signature": "public AuthException(String message)", "summary_en": "Constructs a new authentication exception with the specified detail message.", "summary_cn": "构造一个带有指定详细消息的认证异常。"}], "CrowHttpReporter": [{"methodName": "send", "signature": "public void send(Collection<AlarmMsg> messages) throws Exception", "summary_en": "Send a collection of alarm messages after setting default values", "summary_cn": "发送一组告警消息，发送前会设置默认值"}, {"methodName": "send", "signature": "protected void send(String msg) throws Exception", "summary_en": "Send a single encoded alarm message via HTTP POST", "summary_cn": "通过HTTP POST发送单个编码后的告警消息"}, {"methodName": "setDefaultValue", "signature": "private void setDefaultValue()", "summary_en": "Set default values for encoder, contentType, method and receivers", "summary_cn": "为encoder、contentType、method和receivers设置默认值"}, {"methodName": "checkCrowURL", "signature": "private String checkCrowURL()", "summary_en": "Format the crow URL by appending '/' and method if needed", "summary_cn": "格式化crow URL，必要时追加'/'和方法名"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "private Map<String, String> getHeader()", "summary_en": "Create HTTP headers with login user and token", "summary_cn": "创建包含登录用户和token的HTTP头"}, {"methodName": "convert", "signature": "private Map<String, Object> convert(String message)", "summary_en": "Convert message into a map with content, type and receivers", "summary_cn": "将消息转换为包含内容、类型和接收者的映射"}], "TopicInfo": [{"methodName": "getClusterId", "signature": "public String getClusterId()", "summary_en": "Get the cluster ID", "summary_cn": "获取集群ID"}, {"methodName": "setClusterId", "signature": "public void setClusterId(String clusterId)", "summary_en": "Set the cluster ID", "summary_cn": "设置集群ID"}, {"methodName": "getTopic", "signature": "public String getTopic()", "summary_en": "Get the topic name", "summary_cn": "获取主题名称"}, {"methodName": "setTopic", "signature": "public void setTopic(String topic)", "summary_en": "Set the topic name", "summary_cn": "设置主题名称"}, {"methodName": "getPartitions", "signature": "public int getPartitions()", "summary_en": "Get the number of partitions", "summary_cn": "获取分区数量"}, {"methodName": "setPartitions", "signature": "public void setPartitions(int partitions)", "summary_en": "Set the number of partitions", "summary_cn": "设置分区数量"}, {"methodName": "getReplicas", "signature": "public int getReplicas()", "summary_en": "Get the number of replicas", "summary_cn": "获取副本数量"}, {"methodName": "setReplicas", "signature": "public void setReplicas(int replicas)", "summary_en": "Set the number of replicas", "summary_cn": "设置副本数量"}, {"methodName": "get<PERSON>wner", "signature": "public String getOwner()", "summary_en": "Get the owner of the topic", "summary_cn": "获取主题所有者"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "public void setOwner(String owner)", "summary_en": "Set the owner of the topic", "summary_cn": "设置主题所有者"}, {"methodName": "isNeedAuth", "signature": "public boolean isNeedAuth()", "summary_en": "Check if authentication is needed", "summary_cn": "检查是否需要认证"}, {"methodName": "setNeedAuth", "signature": "public void setNeedAuth(boolean needAuth)", "summary_en": "Set if authentication is needed", "summary_cn": "设置是否需要认证"}, {"methodName": "isEsSearch", "signature": "public boolean isEsSearch()", "summary_en": "Check if Elastic Search is enabled", "summary_cn": "检查是否启用Elastic Search"}, {"methodName": "setEsSearch", "signature": "public void setEsSearch(boolean esSearch)", "summary_en": "Set if Elastic Search is enabled", "summary_cn": "设置是否启用Elastic Search"}, {"methodName": "getDescription", "signature": "public String getDescription()", "summary_en": "Get the description of the topic", "summary_cn": "获取主题描述"}, {"methodName": "setDescription", "signature": "public void setDescription(String description)", "summary_en": "Set the description of the topic", "summary_cn": "设置主题描述"}], "RequestMapping": [{"methodName": "value", "signature": "String[] value()", "summary_en": "Get the request paths for RestHandler binding", "summary_cn": "获取用于RestHandler绑定的请求路径"}, {"methodName": "method", "signature": "Method[] method()", "summary_en": "Get the HTTP methods for RestHandler binding", "summary_cn": "获取用于RestHandler绑定的HTTP方法"}], "KafkaProducerWrapper": [{"methodName": "asyncSend", "signature": "public BatchMessageMeta asyncSend(List<HermesMessage> hermesMessageList, ActionListener<MessageMeta> actionListener)", "summary_cn": "异步发送批量消息，返回批量消息元数据", "summary_en": "Asynchronously send a batch of messages and return batch message metadata"}, {"methodName": "asyncSend", "signature": "public MessageMeta asyncSend(HermesMessage message, ActionListener<MessageMeta> actionListener)", "summary_cn": "异步发送单条消息，返回消息元数据", "summary_en": "Asynchronously send a single message and return message metadata"}, {"methodName": "doSend", "signature": "protected SendFuture<RecordMetadata> doSend(HermesMessage message, ActionListener<MessageMeta> actionListener)", "summary_cn": "执行消息发送操作，返回发送结果的Future对象", "summary_en": "Perform message sending operation and return a Future object for the send result"}, {"methodName": "buildFromHermesMessage", "signature": "protected ProducerRecord buildFromHermesMessage(HermesMessage message)", "summary_cn": "将HermesMessage转换为Kafka ProducerRecord", "summary_en": "Convert Her<PERSON><PERSON><PERSON>age to Kafka ProducerRecord"}, {"methodName": "toMessageMeta", "signature": "private MessageMeta toMessageMeta(RecordMetadata metadata)", "summary_cn": "将Kafka RecordMetadata转换为消息元数据", "summary_en": "Convert Kafka RecordMetadata to message metadata"}, {"methodName": "syncSend", "signature": "public MessageMeta syncSend(HermesMessage message, long timeout, ActionListener<MessageMeta> actionListener)", "summary_cn": "同步发送单条消息，带超时控制和异步回调", "summary_en": "Synchronously send a single message with timeout control and async callback"}, {"methodName": "syncSend", "signature": "public BatchMessageMeta syncSend(List<HermesMessage> messages, long timeout, ActionListener<MessageMeta> actionListener)", "summary_cn": "同步批量发送消息，带超时控制和异步回调", "summary_en": "Synchronously send a batch of messages with timeout control and async callback"}, {"methodName": "close", "signature": "public void close() throws IOException", "summary_cn": "关闭Kafka生产者", "summary_en": "Close the Kafka producer"}], "ConsumerOffset": [{"methodName": "getConsumerGroupOffset", "signature": "public Map<TopicPartition, Long> getConsumerGroupOffset()", "summary_en": "Get the consumer group offset map", "summary_cn": "获取消费者组偏移量映射"}, {"methodName": "setConsumerGroupOffset", "signature": "public void setConsumerGroupOffset(Map<TopicPartition, Long> consumerGroupOffset)", "summary_en": "Set the consumer group offset map", "summary_cn": "设置消费者组偏移量映射"}], "ResetVo": [{"methodName": "getLock", "signature": "public boolean getLock()", "summary_en": "Get the value of lock", "summary_cn": "获取lock的值"}, {"methodName": "setLock", "signature": "public void setLock(boolean lock)", "summary_en": "Set the value of lock", "summary_cn": "设置lock的值"}], "StringUtils": [{"methodName": "isBlank", "signature": "public static boolean isBlank(final CharSequence cs)", "summary_en": "Check if a CharSequence is empty or contains only whitespace characters", "summary_cn": "判断一个字符序列是否为空或仅包含空白字符"}], "OffsetResp": [{"methodName": "getRedisOffset", "signature": "public long getRedisOffset()", "summary_en": "Get the redis offset value", "summary_cn": "获取redis偏移量"}, {"methodName": "setRedisOffset", "signature": "public void setRedisOffset(long redisOffset)", "summary_en": "Set the redis offset value", "summary_cn": "设置redis偏移量"}, {"methodName": "getZkOffset", "signature": "public long getZkOffset()", "summary_en": "Get the zookeeper offset value", "summary_cn": "获取zookeeper偏移量"}, {"methodName": "setZkOffset", "signature": "public void setZkOffset(long zkOffset)", "summary_en": "Set the zookeeper offset value", "summary_cn": "设置zookeeper偏移量"}], "NettyHttpRequestHandler": [{"methodName": "NettyHttpRequestHandler", "signature": "public NettyHttpRequestHandler(NettyHttpServerTransport serverTransport)", "summary_en": "Constructor for NettyHttpRequestHandler with serverTransport parameter", "summary_cn": "构造函数，初始化NettyHttpRequestHandler并传入serverTransport参数"}, {"methodName": "channelRead0", "signature": "protected void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception", "summary_en": "Handles incoming messages, processes HTTP requests, and dispatches them to the server transport", "summary_cn": "处理传入的消息，处理HTTP请求并将其分发到服务器传输层"}, {"methodName": "<PERSON><PERSON><PERSON><PERSON>", "signature": "public void exception<PERSON><PERSON><PERSON>(ChannelHandlerContext ctx, Throwable cause) throws Exception", "summary_en": "Handles exceptions caught during channel operations", "summary_cn": "处理通道操作期间捕获的异常"}], "MessageMeta": [{"methodName": "create", "signature": "public static MessageMeta create(String topic)", "summary_cn": "创建一个MessageMeta实例并设置topic属性", "summary_en": "Create a MessageMeta instance and set the topic property"}], "TaskListener": [{"methodName": "onComplete", "signature": "void onComplete(Task<Req,Res> task)", "summary_en": "Callback after task execution is completed", "summary_cn": "任务执行完成后回调"}], "LongFetcherScheduler": [{"methodName": "LongFetcherScheduler", "signature": "public LongFetcherScheduler(HermesRuntime runtime, FetchTaskManager fetchTaskManager)", "summary_en": "Constructor that initializes the LongFetcherScheduler with runtime and registers it with the fetch task manager.", "summary_cn": "构造函数，初始化LongFetcherScheduler并注册到fetch任务管理器。"}, {"methodName": "determineDelayMs", "signature": "protected int determineDelayMs(Task<FetchRequest, BatchMessage> task)", "summary_en": "Determines the delay in milliseconds based on the task's payload and exception status.", "summary_cn": "根据任务的负载和异常状态确定延迟时间（毫秒）。"}, {"methodName": "prepareStop", "signature": "public void prepareStop(HermesRuntime runtime) throws Exception", "summary_en": "Prepares the scheduler for stopping by closing connections to release network resources.", "summary_cn": "准备停止调度器，通过关闭连接释放网络资源。"}], "Bootstrap": [{"methodName": "registerStartListener", "signature": "Bootstrap registerStartListener(StartListener startListener)", "summary_cn": "注册启动监听器", "summary_en": "Register a start listener"}, {"methodName": "registerStoppable", "signature": "Bootstrap registerStoppable(Stoppable stoppable)", "summary_cn": "注册可停止对象", "summary_en": "Register a stoppable object"}, {"methodName": "install", "signature": "Bootstrap install(Module module)", "summary_cn": "安装单个模块", "summary_en": "Install a single module"}, {"methodName": "install", "signature": "Bootstrap install(Module module, Module... more)", "summary_cn": "安装一个主模块和多个附加模块", "summary_en": "Install a main module and additional modules"}, {"methodName": "start", "signature": "HermesRuntime start()", "summary_cn": "启动系统并返回运行时实例", "summary_en": "Start the system and return the runtime instance"}, {"methodName": "stop", "signature": "void stop()", "summary_cn": "停止系统", "summary_en": "Stop the system"}, {"methodName": "prepareStop", "signature": "void prepareStop()", "summary_cn": "准备停止系统", "summary_en": "Prepare to stop the system"}, {"methodName": "getSettings", "signature": "ISettings getSettings()", "summary_cn": "获取系统配置", "summary_en": "Get system settings"}], "TaskHandler": [{"methodName": "TaskHandler", "signature": "public TaskHandler(Task<Req, Res> task, AbstractScheduler<Req, Res> scheduler)", "summary_en": "Constructor for TaskHandler with task and scheduler parameters", "summary_cn": "TaskHandler的构造函数，接收任务和调度器参数"}, {"methodName": "run", "signature": "public void run()", "summary_en": "Executes the task handling logic, catches and logs exceptions", "summary_cn": "执行任务处理逻辑，捕获并记录异常"}, {"methodName": "handle", "signature": "protected void handle()", "summary_en": "Handles the task execution, checks latency, processes task, and schedules next execution if needed", "summary_cn": "处理任务执行，检查延迟，处理任务，并在需要时调度下一次执行"}], "AlarmMsg": [{"methodName": "equals", "signature": "public boolean equals(Object o)", "summary_cn": "比较两个AlarmMsg对象是否相等，比较message和exception字段", "summary_en": "Compare two AlarmMsg objects for equality, comparing message and exception fields"}, {"methodName": "hashCode", "signature": "public int hashCode()", "summary_cn": "计算AlarmMsg对象的哈希码，基于message和exception.getMessage()", "summary_en": "Calculate the hash code of the AlarmMsg object based on message and exception.getMessage()"}], "AbstractScheduler": [{"methodName": "submit", "signature": "public boolean submit(Task<Req, Res> task)", "summary_en": "Submit a task for execution, handling rejection and sending alerts if necessary.", "summary_cn": "提交任务执行，处理拒绝情况并在必要时发送告警。"}, {"methodName": "doExecute", "signature": "protected void doExecute(TaskHandler<Req, Res> taskHandler)", "summary_en": "Execute the given task handler using the worker pool.", "summary_cn": "使用工作线程池执行给定的任务处理器。"}, {"methodName": "invokeNext", "signature": "protected void invokeNext(Runnable task, long delay, TimeUnit unit)", "summary_en": "Schedule the next task to run after a specified delay.", "summary_cn": "在指定的延迟后调度下一个任务运行。"}, {"methodName": "sendResponse", "signature": "protected void sendResponse(Task<Req, Res> task)", "summary_en": "Send the response or exception from the task to the action listener, handling locks and edge cases.", "summary_cn": "将任务的响应或异常发送给动作监听器，处理锁和边缘情况。"}, {"methodName": "onSendResponse", "signature": "protected void onSendResponse(Task<Req, Res> task)", "summary_en": "Hook method called when sending a response, can be overridden by subclasses.", "summary_cn": "发送响应时调用的钩子方法，可由子类覆盖。"}, {"methodName": "onSendException", "signature": "protected void onSendException(Task<Req, Res> task)", "summary_en": "Hook method called when sending an exception, can be overridden by subclasses.", "summary_cn": "发送异常时调用的钩子方法，可由子类覆盖。"}, {"methodName": "isRunning", "signature": "public boolean isRunning()", "summary_en": "Check if the scheduler is currently running.", "summary_cn": "检查调度器当前是否正在运行。"}, {"methodName": "close", "signature": "public void close() throws Exception", "summary_en": "Close the scheduler by setting the running state to false.", "summary_cn": "通过将运行状态设置为false来关闭调度器。"}, {"methodName": "stop", "signature": "public void stop(HermesRuntime runtime) throws Exception", "summary_en": "Stop the scheduler and shutdown the worker pool gracefully.", "summary_cn": "停止调度器并优雅地关闭工作线程池。"}, {"methodName": "order", "signature": "public int order()", "summary_en": "Get the priority order of the scheduler.", "summary_cn": "获取调度器的优先级顺序。"}, {"methodName": "process", "signature": "protected abstract void process(Task<Req, Res> task)", "summary_en": "Abstract method to process a task, must be implemented by subclasses.", "summary_cn": "处理任务的抽象方法，必须由子类实现。"}, {"methodName": "determineDelayMs", "signature": "protected abstract int determineDelayMs(Task<Req, Res> task)", "summary_en": "Abstract method to determine the delay in milliseconds for a task, must be implemented by subclasses.", "summary_cn": "确定任务延迟毫秒数的抽象方法，必须由子类实现。"}, {"methodName": "resetRequest", "signature": "protected abstract void resetRequest(Task<Req, Res> task)", "summary_en": "Abstract method to reset the request of a task, must be implemented by subclasses.", "summary_cn": "重置任务请求的抽象方法，必须由子类实现。"}], "ConsumerThreadLocal": [{"methodName": "setSupplier", "signature": "public static void setSupplier(Supplier<ConsumerWrapper> supplier)", "summary_en": "Set a supplier for initializing the ThreadLocal ConsumerWrapper instance.", "summary_cn": "设置一个用于初始化ThreadLocal ConsumerWrapper实例的supplier。"}, {"methodName": "getConsumerWrapper", "signature": "public static ConsumerWrapper getConsumerWrapper()", "summary_en": "Retrieve the ConsumerWrapper instance from ThreadLocal. Throws an exception if ThreadLocal is not initialized.", "summary_cn": "从ThreadLocal中获取ConsumerWrapper实例。如果ThreadLocal未初始化，则抛出异常。"}], "RedisErrorException": [{"methodName": "RedisErrorException", "signature": "public RedisErrorException()", "summary_en": "Constructs a new RedisErrorException without detail message", "summary_cn": "构造一个新的RedisErrorException，不带详细消息"}, {"methodName": "RedisErrorException", "signature": "public RedisErrorException(String message)", "summary_en": "Constructs a new RedisErrorException with the specified detail message", "summary_cn": "构造一个新的RedisErrorException，带有指定的详细消息"}, {"methodName": "RedisErrorException", "signature": "public RedisErrorException(Throwable cause)", "summary_en": "Constructs a new RedisErrorException with the specified cause", "summary_cn": "构造一个新的RedisErrorException，带有指定的原因"}, {"methodName": "RedisErrorException", "signature": "public RedisErrorException(String message, Throwable cause)", "summary_en": "Constructs a new RedisErrorException with the specified detail message and cause", "summary_cn": "构造一个新的RedisErrorException，带有指定的详细消息和原因"}], "FaviconHandler": [{"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signature": "public Favicon<PERSON><PERSON><PERSON>(RestController controller)", "summary_en": "Constructor for FaviconHandler with a RestController parameter", "summary_cn": "FaviconHandler的构造函数，接受一个RestController参数"}, {"methodName": "handRequest", "signature": "public void handRequest(RestRequest restRequest, RestChannel restChannel) throws Throwable", "summary_en": "Handles the favicon request by sending an empty response", "summary_cn": "处理favicon请求，发送空响应"}], "Health": [{"methodName": "health", "signature": "boolean health()", "summary_en": "Check the health status", "summary_cn": "检查健康状态"}], "RestResponse": [{"methodName": "text", "signature": "public static RestResponse text(String text)", "summary_en": "Create a successful text response with OK status.", "summary_cn": "创建一个状态为OK的文本响应。"}, {"methodName": "text", "signature": "public static RestResponse text(RestStatus status, String text)", "summary_en": "Create a text response with specified status.", "summary_cn": "创建一个指定状态的文本响应。"}, {"methodName": "success", "signature": "public static RestResponse success(Object data)", "summary_en": "Create a successful JSON response with OK status and provided data.", "summary_cn": "创建一个状态为OK且包含提供数据的成功JSON响应。"}, {"methodName": "error", "signature": "public static RestResponse error(RestStatus status, int code, String message)", "summary_en": "Create an error JSON response with specified status, error code, and message.", "summary_cn": "创建一个指定状态、错误代码和消息的错误JSON响应。"}, {"methodName": "error", "signature": "public static RestResponse error(Throwable t)", "summary_en": "Create an error JSON response based on the provided throwable, extracting code and message.", "summary_cn": "根据提供的异常创建一个错误JSON响应，提取错误代码和消息。"}], "IPUtil": [{"methodName": "localIp", "signature": "public static String localIp()", "summary_en": "Get the first non-loopback IPv4 address of the local machine", "summary_cn": "获取本地机器的第一个非回环IPv4地址"}, {"methodName": "localIpCached", "signature": "public static String localIpCached()", "summary_en": "Get the cached local IP address (thread-safe)", "summary_cn": "获取缓存的本地IP地址（线程安全）"}, {"methodName": "getPID", "signature": "public static long getPID()", "summary_en": "Get the current process ID (PID)", "summary_cn": "获取当前进程ID（PID）"}], "FetcherController": [{"methodName": "validateConsumer", "signature": "protected void validateConsumer(ConsumerVo consumerVo)", "summary_en": "Validate the consumer information", "summary_cn": "验证消费者信息"}, {"methodName": "fetch", "signature": "public void fetch(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Handle fetch request and decide whether to use long fetch based on conditions", "summary_cn": "处理获取请求并根据条件决定是否使用长轮询"}, {"methodName": "useLongFetch", "signature": "protected boolean useLongFetch(String flag, FetchRequest fetchRequest)", "summary_en": "Determine whether to use long fetch based on flag and fetch request", "summary_cn": "根据标志和获取请求决定是否使用长轮询"}, {"methodName": "ack", "signature": "public void ack(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Handle acknowledgment request for consumed messages", "summary_cn": "处理消息消费的确认请求"}, {"methodName": "renew", "signature": "public void renew(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Handle lock renewal request", "summary_cn": "处理锁续期请求"}], "HermesEncoder": [{"methodName": "encode", "signature": "public String encode(AlarmMsg msg) throws Exception", "summary_en": "Encodes the alarm message into a formatted string with details like app name, environment, time, host, message content, and exception.", "summary_cn": "将告警消息编码为格式化的字符串，包含应用名称、环境、时间、主机、消息内容和异常等详细信息。"}, {"methodName": "getAppName", "signature": "protected String getAppName()", "summary_en": "Retrieves the application name, optionally appended with the cluster ID if settings are available.", "summary_cn": "获取应用程序名称，如果设置可用，可选择附加集群ID。"}], "FetchTaskManager": [{"methodName": "register", "signature": "public void register(String taskType, Scheduler<FetchRequest, BatchMessage> scheduler)", "summary_en": "Register a scheduler for a specific task type", "summary_cn": "为特定任务类型注册调度器"}, {"methodName": "submitTask", "signature": "public void submitTask(Task<FetchRequest, BatchMessage> task)", "summary_en": "Submit a task to the corresponding scheduler and manage its lifecycle", "summary_cn": "提交任务到对应的调度器并管理其生命周期"}, {"methodName": "addTask", "signature": "protected synchronized void addTask(Task<FetchRequest, BatchMessage> task)", "summary_en": "Add a task to the task list with size limit check", "summary_cn": "将任务添加到任务列表，并进行大小限制检查"}, {"methodName": "removeTask", "signature": "protected synchronized void removeTask(Task<FetchRequest, BatchMessage> task)", "summary_en": "Remove a task from the task list", "summary_cn": "从任务列表中移除任务"}, {"methodName": "onComplete", "signature": "public void onComplete(Task<FetchRequest, BatchMessage> task)", "summary_en": "Callback method invoked when a task is completed", "summary_cn": "任务完成时调用的回调方法"}, {"methodName": "getTaskList", "signature": "public List<Task<FetchRequest, BatchMessage>> getTaskList()", "summary_en": "Get a snapshot of the current task list", "summary_cn": "获取当前任务列表的快照"}], "HermesRuntime": [{"methodName": "getInstance", "signature": "<T> T getInstance(Class<T> type)", "summary_en": "Get an instance of the specified type", "summary_cn": "获取指定类型的实例"}, {"methodName": "getBootstrap", "signature": "Bootstrap getBootstrap()", "summary_en": "Get the bootstrap instance", "summary_cn": "获取引导程序实例"}], "StopWatch": [{"methodName": "create", "signature": "public static StopWatch create()", "summary_en": "Create a new instance of StopWatch", "summary_cn": "创建一个新的StopWatch实例"}, {"methodName": "start", "signature": "public StopWatch start()", "summary_en": "Start the stopwatch", "summary_cn": "启动计时器"}, {"methodName": "stop", "signature": "public StopWatch stop()", "summary_en": "Stop the stopwatch and calculate elapsed time", "summary_cn": "停止计时器并计算经过的时间"}, {"methodName": "elapsed", "signature": "public long elapsed()", "summary_en": "Get the elapsed time in milliseconds", "summary_cn": "获取经过的时间（毫秒）"}, {"methodName": "trigger", "signature": "public StopWatch trigger(int timeout, LongConsumer function)", "summary_en": "Trigger a function if elapsed time exceeds the timeout", "summary_cn": "如果经过的时间超过超时时间，则触发一个函数"}], "OffsetRange": [{"methodName": "getTopic", "signature": "public String getTopic()", "summary_en": "Get the topic name", "summary_cn": "获取主题名称"}, {"methodName": "setTopic", "signature": "public void setTopic(String topic)", "summary_en": "Set the topic name", "summary_cn": "设置主题名称"}, {"methodName": "getPartition", "signature": "public int getPartition()", "summary_en": "Get the partition number", "summary_cn": "获取分区号"}, {"methodName": "setPartition", "signature": "public void setPartition(int partition)", "summary_en": "Set the partition number", "summary_cn": "设置分区号"}, {"methodName": "getBeiginOffset", "signature": "public long getBeiginOffset()", "summary_en": "Get the beginning offset", "summary_cn": "获取起始偏移量"}, {"methodName": "setBeiginOffset", "signature": "public void setBeiginOffset(long beiginOffset)", "summary_en": "Set the beginning offset", "summary_cn": "设置起始偏移量"}, {"methodName": "getEndOffset", "signature": "public long getEndOffset()", "summary_en": "Get the end offset", "summary_cn": "获取结束偏移量"}, {"methodName": "setEndOffset", "signature": "public void setEndOffset(long endOffset)", "summary_en": "Set the end offset", "summary_cn": "设置结束偏移量"}], "ReflectUtil": [{"methodName": "getFieldByFieldName", "signature": "public static Field getFieldByFieldName(Object object, String fieldName)", "summary_en": "Get the Field object of the specified field name from the given object, with access permission set to allow access to private fields.", "summary_cn": "从给定对象中获取指定字段名的Field对象，并设置访问权限以允许访问私有字段。"}, {"methodName": "getFieldValueByFieldName", "signature": "public static Object getFieldValueByFieldName(Object object, String fieldName)", "summary_en": "Get the value of the specified field name from the given object, with access permission set to allow access to private fields.", "summary_cn": "从给定对象中获取指定字段名的值，并设置访问权限以允许访问私有字段。"}], "BeanCopyUtil": [{"methodName": "copyFromMap", "signature": "public static <T> T copyFromMap(Class<T> type, Map<? extends String, ? extends Object> source)", "summary_en": "Copy properties from a map to an instance of the specified class", "summary_cn": "将Map中的属性拷贝到指定类的实例中"}, {"methodName": "convert", "signature": "private static Object convert(Object source, Class<?> type)", "summary_en": "Convert the source object to the specified type", "summary_cn": "将源对象转换为指定类型"}, {"methodName": "getBeanInfo", "signature": "public static BeanInfo getBeanInfo(Class type)", "summary_en": "Get BeanInfo for the specified class from cache or create a new one if not exists", "summary_cn": "从缓存中获取指定类的BeanInfo，如果不存在则创建新的"}, {"methodName": "visit", "signature": "public static BeanInfo visit(Class type)", "summary_en": "Visit the specified class and create a BeanInfo for it", "summary_cn": "访问指定类并为其创建BeanInfo"}, {"methodName": "doVisit", "signature": "public static void doVisit(Class type, BeanInfo beanInfo)", "summary_en": "Recursively visit the class and its superclasses to collect field information", "summary_cn": "递归访问类及其父类以收集字段信息"}, {"methodName": "add", "signature": "public void add(Field field)", "summary_en": "Add a field to the field list of BeanInfo", "summary_cn": "将字段添加到BeanInfo的字段列表中"}], "CompletableContext": [{"methodName": "addListener", "signature": "public void addListener(BiConsumer<T, ? super Throwable> listener)", "summary_en": "Add a listener to be notified when the future completes", "summary_cn": "添加一个监听器，当future完成时通知"}, {"methodName": "complete", "signature": "public void complete(T value)", "summary_en": "Complete the future with the given value", "summary_cn": "使用给定值完成future"}, {"methodName": "completeExceptionally", "signature": "public void completeExceptionally(Throwable e)", "summary_en": "Complete the future exceptionally with the given exception", "summary_cn": "使用给定异常异常完成future"}, {"methodName": "isDone", "signature": "public void isDone()", "summary_en": "Check if the future is done", "summary_cn": "检查future是否完成"}, {"methodName": "isCompletedExceptionally", "signature": "public boolean isCompletedExceptionally()", "summary_en": "Check if the future completed exceptionally", "summary_cn": "检查future是否异常完成"}], "ClusterInfo": [{"methodName": "getClusterId", "signature": "public String getClusterId()", "summary_cn": "获取集群ID", "summary_en": "Get the cluster ID"}, {"methodName": "setClusterId", "signature": "public void setClusterId(String clusterId)", "summary_cn": "设置集群ID", "summary_en": "Set the cluster ID"}, {"methodName": "getDescription", "signature": "public String getDescription()", "summary_cn": "获取集群描述", "summary_en": "Get the cluster description"}, {"methodName": "setDescription", "signature": "public void setDescription(String description)", "summary_cn": "设置集群描述", "summary_en": "Set the cluster description"}, {"methodName": "getBrokerList", "signature": "public String getBrokerList()", "summary_cn": "获取Broker列表", "summary_en": "Get the broker list"}, {"methodName": "setBrokerList", "signature": "public void setBrokerList(String brokerList)", "summary_cn": "设置Broker列表", "summary_en": "Set the broker list"}, {"methodName": "getZkServer", "signature": "public String getZkServer()", "summary_cn": "获取Zookeeper服务器地址", "summary_en": "Get the Zookeeper server address"}, {"methodName": "setZkServer", "signature": "public void setZkServer(String zkServer)", "summary_cn": "设置Zookeeper服务器地址", "summary_en": "Set the Zookeeper server address"}, {"methodName": "getProducerOneSize", "signature": "public int getProducerOneSize()", "summary_cn": "获取ACK0生产者池大小", "summary_en": "Get the ACK0 producer pool size"}, {"methodName": "setProducerOneSize", "signature": "public void setProducerOneSize(int producerOneSize)", "summary_cn": "设置ACK0生产者池大小", "summary_en": "Set the ACK0 producer pool size"}, {"methodName": "getProducerTwoSize", "signature": "public int getProducerTwoSize()", "summary_cn": "获取ACK1生产者池大小", "summary_en": "Get the ACK1 producer pool size"}, {"methodName": "setProducerTwoSize", "signature": "public void setProducerTwoSize(int producerTwoSize)", "summary_cn": "设置ACK1生产者池大小", "summary_en": "Set the ACK1 producer pool size"}, {"methodName": "getProducerThreeSize", "signature": "public int getProducerThreeSize()", "summary_cn": "获取ACK All生产者池大小", "summary_en": "Get the ACK All producer pool size"}, {"methodName": "setProducerThreeSize", "signature": "public void setProducerThreeSize(int producerThreeSize)", "summary_cn": "设置ACK All生产者池大小", "summary_en": "Set the ACK All producer pool size"}, {"methodName": "getConsumerPoolSize", "signature": "public int getConsumerPoolSize()", "summary_cn": "获取消费者池大小", "summary_en": "Get the consumer pool size"}, {"methodName": "setConsumerPoolSize", "signature": "public void setConsumerPoolSize(int consumerPoolSize)", "summary_cn": "设置消费者池大小", "summary_en": "Set the consumer pool size"}, {"methodName": "getMq", "signature": "public String getMq()", "summary_cn": "获取消息队列类型", "summary_en": "Get the message queue type"}, {"methodName": "setMq", "signature": "public void setMq(String mq)", "summary_cn": "设置消息队列类型", "summary_en": "Set the message queue type"}], "MetadataManager": [{"methodName": "getPartitionNum", "signature": "int getPartitionNum(String topic)", "summary_cn": "通过topic获取分区数", "summary_en": "Get the number of partitions by topic"}, {"methodName": "getConsumerOffset", "signature": "ConsumerOffset getConsumerOffset(ConsumerVo consumer)", "summary_cn": "返回一个消费组的offset点位，按照partition区分", "summary_en": "Get the offset points of a consumer group, distinguished by partition"}, {"methodName": "getOffset", "signature": "OffsetResp getOffset(ConsumerVo consumer)", "summary_cn": "返回一个消费组某个partition下的offset点位(已废弃)", "summary_en": "Get the offset point of a partition under a consumer group (deprecated)"}, {"methodName": "getTopicOffsetRange", "signature": "OffsetRange getTopicOffsetRange(TopicPartition tp)", "summary_cn": "返回一个topic的offset区间", "summary_en": "Get the offset range of a topic"}, {"methodName": "resetOffset", "signature": "boolean resetOffset(ConsumerVo consumerVo, long offset)", "summary_cn": "重置offset(包括zk和redis)", "summary_en": "Reset offset (including zk and redis)"}, {"methodName": "isNoProducer", "signature": "boolean isNoProducer(String topic)", "summary_cn": "判断某个topic是否已没有producer", "summary_en": "Check if a topic has no producer"}, {"methodName": "getTopicInfo", "signature": "TopicInfo getTopicInfo(String topic)", "summary_cn": "返回TopicInfo，提供给auth使用", "summary_en": "Get TopicInfo for auth use"}], "MethodRestHandler": [{"methodName": "MethodRestHandler", "signature": "public MethodRestHandler(RestHandler restHandler, Method invokeMethod)", "summary_en": "Constructor for MethodRestHandler with specified RestHandler and Method.", "summary_cn": "使用指定的RestHandler和Method构造MethodRestHandler。"}, {"methodName": "handRequest", "signature": "public void handRequest(RestRequest restRequest, RestChannel restChannel) throws Throwable", "summary_en": "Handles the RestRequest by invoking the specified method on the RestHandler, and manages exceptions.", "summary_cn": "通过在RestHandler上调用指定的方法来处理RestRequest，并管理异常。"}, {"methodName": "toString", "signature": "public String toString()", "summary_en": "Returns a string representation of the method's declaring class and method name.", "summary_cn": "返回方法声明类和方法名的字符串表示。"}], "CatController": [{"methodName": "cat", "signature": "public void cat(RestRequest restRequest, RestChannel restChannel)", "summary_en": "List all available paths with @RequestMapping annotation in the controller", "summary_cn": "列出控制器中所有带有@RequestMapping注解的可用路径"}, {"methodName": "settings", "signature": "public void settings(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Display all settings properties and their values", "summary_cn": "显示所有设置属性及其值"}, {"methodName": "threadPool", "signature": "public void threadPool(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Display thread pool information including core size, max pool size, queue size, etc.", "summary_cn": "显示线程池信息，包括核心大小、最大池大小、队列大小等"}, {"methodName": "consumers", "signature": "public void consumers(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Display consumer group offset information for a specific topic and group", "summary_cn": "显示特定主题和组的消费者组偏移量信息"}, {"methodName": "tasks", "signature": "public void tasks(RestRequest restRequest, RestChannel restChannel)", "summary_en": "Display task information including app name, group, topic, status, etc.", "summary_cn": "显示任务信息，包括应用名称、组、主题、状态等"}], "DefaultHermesRuntime": [{"methodName": "DefaultHermesRuntime", "signature": "public DefaultHermesRuntime(Bootstrap bootstrap)", "summary_en": "Constructor that initializes the runtime with a Bootstrap instance.", "summary_cn": "构造函数，使用Bootstrap实例初始化运行时。"}, {"methodName": "onInject", "signature": "public void onInject(Injector injector)", "summary_en": "Injects the Guice Injector into the runtime.", "summary_cn": "将Guice Injector注入到运行时中。"}, {"methodName": "getInstance", "signature": "public <T> T getInstance(Class<T> type)", "summary_en": "Retrieves an instance of the specified type from the injector.", "summary_cn": "从注入器中获取指定类型的实例。"}, {"methodName": "getBootstrap", "signature": "public Bootstrap getBootstrap()", "summary_en": "Returns the Bootstrap instance associated with this runtime.", "summary_cn": "返回与此运行时关联的Bootstrap实例。"}], "Alert": [{"methodName": "send", "signature": "void send(AlarmMsg msg)", "summary_en": "Send an alarm message", "summary_cn": "发送告警消息"}]}