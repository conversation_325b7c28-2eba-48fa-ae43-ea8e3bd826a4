from transformers import AutoModelForCausalLM, AutoTokenizer
import torch


model = AutoModelForCausalLM.from_pretrained("Kortix/FastApply-1.5B-v1.0", device_map="auto")
tokenizer = AutoTokenizer.from_pretrained("Kortix/FastApply-1.5B-v1.0")


def read_file_content(file_path):
    try:
        with open(file_path, 'r') as file:
            return file.read()
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        exit(1)
    except Exception as e:
        print(f"Error reading file {file_path}: {str(e)}")
        exit(1)

# Prepare your input following the prompt structure mentioned above
input_text = """<|im_start|>system
You are a coding assistant that helps merge code updates, ensuring every modification is fully integrated.<|im_end|>
<|im_start|>user
Merge all changes from the <update> snippet into the <code> below.
- Preserve the code's structure, order, comments, and indentation exactly.
- Output only the updated code, enclosed within <updated-code> and </updated-code> tags.
- Do not include any additional text, explanations, placeholders, ellipses, or code fences.

<code>{original_code}</code>

<update>{update_snippet}</update>

Provide the complete updated code.<|im_end|>
<|im_start|>assistant
"""

# 从文件读取原始代码和更新片段
original_code = read_file_content("/tmp/original_code.txt")
update_snippet = read_file_content("/tmp/update_snippet.txt")


input_text = input_text.format(
    original_code=original_code,
    update_snippet=update_snippet,
).strip()

# Generate the response
input_ids = tokenizer.encode(input_text, return_tensors="pt")
input_ids = input_ids.to('mps')  # 确保input_ids与模型在同一设备
output = model.generate(input_ids, max_length=8192,)

try:
    output = model.generate(input_ids, max_length=8192)
    response = tokenizer.decode(output[0][len(input_ids[0]):])
    print(response)

    # Extract the updated code from the response
    updated_code = response.split("<updated-code>")[1].split("</updated-code>")[0]
except Exception as e:
    print(f"Error during generation: {str(e)}")
    exit(1)