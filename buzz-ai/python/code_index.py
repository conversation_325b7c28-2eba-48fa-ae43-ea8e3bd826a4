import os
import javalang
import openai
from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance

openai.api_key = "YOUR_OPENAI_API_KEY"

# 向量模型（bge-m3、e5-multilingual 或 text-embedding-3-small）
embedding_model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')  # 替换为更强的模型

# 初始化 Qdrant 向量库
qdrant = QdrantClient(":memory:")  # 用内存 demo，也可以指定 localhost:6333
qdrant.recreate_collection(
    collection_name="java_methods",
    vectors_config=VectorParams(size=embedding_model.get_sentence_embedding_dimension(), distance=Distance.COSINE)
)

# 📍 获取目录下所有 .java 文件
def find_java_files(root_dir):
    for root, _, files in os.walk(root_dir):
        for file in files:
            if file.endswith(".java"):
                yield os.path.join(root, file)

# 📍 用 LLM 生成中英文摘要
def generate_summary(class_name, method_name, method_code):
    prompt = f"""
你是一个 Java 工程分析助手。请用中文和英文为以下方法生成简洁摘要：

类名: {class_name}
方法名: {method_name}
方法代码:
{method_code}

返回格式：
summary_cn: ...
summary_en: ...
"""
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.3
    )
    return response.choices[0].message.content.strip()

# 📍 处理单个 Java 文件
def process_java_file(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()

    try:
        tree = javalang.parse.parse(content)
    except:
        print(f"⚠️ Failed to parse: {filepath}")
        return

    for path, node in tree.filter(javalang.tree.ClassDeclaration):
        class_name = node.name
        for method in node.methods:
            if not method.modifiers or "public" not in method.modifiers:
                continue  # 只处理 public 方法

            method_name = method.name
            signature = f"{' '.join(method.modifiers)} {method.return_type.name if method.return_type else 'void'} {method_name}({', '.join([p.type.name for p in method.parameters])})"

            # 提取代码（简单处理）
            method_code = content[method.position.line - 1 : method.position.line + 15]  # 取若干行上下文

            # 调用 GPT 生成摘要
            print(f"🧠 Summarizing {class_name}.{method_name}...")
            try:
                summary_output = generate_summary(class_name, method_name, method_code)
                lines = summary_output.splitlines()
                summary_cn = next((line.replace("summary_cn:", "").strip() for line in lines if "summary_cn" in line), "")
                summary_en = next((line.replace("summary_en:", "").strip() for line in lines if "summary_en" in line), "")
            except Exception as e:
                print(f"❌ LLM failed: {e}")
                continue

            # 构造索引结构
            item = {
                "className": class_name,
                "methodName": method_name,
                "signature": signature,
                "summary_cn": summary_cn,
                "summary_en": summary_en,
                "code": method_code,
                "filePath": filepath
            }

            # 生成 embedding
            embedding_input = f"{summary_cn} {summary_en} {signature} {class_name}"
            vector = embedding_model.encode(embedding_input)

            qdrant.upsert(
                collection_name="java_methods",
                points=[PointStruct(id=hash(signature), vector=vector.tolist(), payload=item)]
            )

# 📍 启动入口
if __name__ == "__main__":
    source_dir = "./java_project"  # 替换为你的 Java 项目路径
    for java_file in find_java_files(source_dir):
        process_java_file(java_file)

    print("✅ 索引构建完成！你可以开始查询了。")
