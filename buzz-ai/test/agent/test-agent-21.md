2025-07-02 17:52

<thinking>
1. 目标是为 HermesMessage 创建单元测试，使用 JUnit4
2. 需要先确定 HermesMessage 类的路径
3. 查看项目结构，hermes-core 目录可能包含核心消息类
4. 需要先搜索 HermesMessage 类的位置
5. 使用 search_files 工具查找 HermesMessage 类定义
</thinking>

<search_files>
<keyword>HermesMessage</keyword>
</search_files><thinking>
1. 找到了 HermesMessage 类位于 hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
2. 需要创建一个对应的单元测试类
3. 测试类应该放在 hermes-api/src/test/java/com/wacai/hermes/message/ 目录下
4. 测试类名应为 HermesMessageTest
5. 创建一个简单的测试用例，测试消息的基本功能
</thinking>

<write_to_file>
<path>hermes-api/src/test/java/com/wacai/hermes/message/HermesMessageTest.java</path>
<content>
package com.wacai.hermes.message;

import org.junit.Test;
import static org.junit.Assert.*;

public class HermesMessageTest {

    @Test
    public void testMessageCreation() {
        String topic = "testTopic";
        String key = "testKey";
        String value = "testValue";
        
        HermesMessage message = new HermesMessage(topic, key, value);
        
        assertEquals(topic, message.getTopic());
        assertEquals(key, message.getKey());
        assertEquals(value, message.getValue());
    }
}
</content>
<line_count>16</line_count>
</write_to_file>