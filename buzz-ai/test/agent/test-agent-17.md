2025-07-02 17:22

<thinking>
1. 分析任务：需要优化 `ProducerController`，为发送消息增加 header 并设置到 Kafka
2. 查看环境信息：当前工作目录是 `/System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4`，目标文件路径是 `hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`
3. 需要先查看当前 `ProducerController` 的实现，了解消息发送逻辑
4. 然后修改代码，添加 header 支持并确保能正确设置到 Kafka 消息中
5. 首先使用 `read_file` 工具读取 `ProducerController.java` 文件内容
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
1. 分析当前代码：`ProducerController` 有三个主要方法处理消息发送：
   - `sendSingle`: 发送单条消息
   - `sendBatch`: 批量发送消息
   - `publish`: 兼容老版本的消息发送

2. 需要修改的点：
   - 从请求中获取 header 信息
   - 将 header 设置到 `HermesMessage` 中
   - 需要检查 `HermesMessage` 类是否支持 header 字段

3. 修改方案：
   - 在 `sendSingle` 和 `sendBatch` 方法中，从 `RestRequest` 获取 headers
   - 将 headers 设置到 `HermesMessage` 对象中
   - 需要确保 `HermesMessage` 类有设置 headers 的方法

4. 首先需要确认 `HermesMessage` 类的结构，查看是否支持 headers
</thinking>

<search_files>
<keyword>HermesMessage</keyword>
</search_files>