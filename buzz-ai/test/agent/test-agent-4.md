2025-06-16 16:54

<thinking>
1. 需要优化 `ProducerController` 为消息发送增加 header 功能
2. 根据环境信息，文件路径是 `hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`
3. 需要先查看当前 `ProducerController` 的实现，特别是消息发送部分
4. Kafka 消息头可以通过 `ProducerRecord` 的 `headers()` 方法设置
5. 修改后需要确保不影响现有功能

下一步行动：先查看当前 `ProducerController` 的代码实现
</thinking>

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
</devin><thinking>
1. 需要为 `ProducerController` 增加消息头功能，主要修改点：
   - 在 `sendSingle` 方法中从请求获取 headers 并设置到消息
   - 在 `sendBatch` 方法中为批量消息设置 headers
   - 在 `publish` 方法中保持兼容性

2. 修改方案：
   - 在 `HermesMessage` 类中增加 headers 字段（假设已支持）
   - 从 `RestRequest` 中获取 headers 参数
   - 在发送前将 headers 设置到消息中

3. 需要检查 `Client` 类的 `send` 和 `sendBatch` 方法是否支持带 headers 的消息发送

4. 修改步骤：
   - 首先修改 `sendSingle` 方法
   - 然后修改 `sendBatch` 方法
   - 最后保持 `publish` 方法不变以保持兼容

5. 需要确保：
   - 不影响现有功能
   - 保持向后兼容
   - 添加适当的日志
</thinking>

```java hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
// ... rest of code here ...

@RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
    String topic = restRequest.param("topic");
    String ack = restRequest.param("ack", "1");
    long timeout = restRequest.paramAsLong("timeout", 5000l);
    HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
    
    // Add headers from request
    Map<String, String> headers = restRequest.paramAsMap("headers");
    if (headers != null && !headers.isEmpty()) {
        hermesMessage.setHeaders(headers);
    }
    
    validateSingleMessage(hermesMessage, topic);
    StopWatch watch = StopWatch.create().start();
    ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
        log.info("sendSingle, topic {} client {} offset {} cost(ms) {} headers {}",
                topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed(), headers);
    });
    client.send(ack, hermesMessage, timeout, actionListener);
}

@RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
    String topic = restRequest.param("topic");
    String ack = restRequest.param("ack", "1");
    long timeout = restRequest.paramAsLong("timeout", 5000l);
    String trace = restRequest.param("trace", "off");
    List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);

    // Add headers from request to each message
    Map<String, String> headers = restRequest.paramAsMap("headers");
    if (headers != null && !headers.isEmpty()) {
        messages.forEach(msg -> msg.setHeaders(headers));
    }

    validateBatchMessages(messages, topic);
    StopWatch watch = StopWatch.create().start();
    ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
        log.info("sendBatch topic {} client {} offset {} cost(ms) {} headers {}",
                topic, restChannel.getClientIp(), r.getMessageMetaList().get(0).getOffset(), watch.stop().elapsed(), headers);
    });
    if (StringUtils.equalsIgnoreCase("on", trace)) {
        client.sendBatch(ack, messages, timeout, actionListener);
    } else {
        client.sendBatchByOneFlush(ack, messages, timeout, actionListener);
    }
}

// ... rest of code here ...
```

<thinking>
修改说明：
1. 在 `sendSingle` 方法中：
   - 从请求参数获取 headers (Map<String, String>)
   - 将 headers 设置到 HermesMessage
   - 在日志中添加 headers 信息

2. 在 `sendBatch` 方法中：
   - 同样从请求参数获取 headers
   - 为批量中的每条消息设置相同的 headers
   - 在日志中添加 headers 信息

3. 保持 `publish` 方法不变以保持老版本兼容性

4. 需要确认：
   - `HermesMessage` 类是否已有 `setHeaders` 方法
   - `RestRequest` 的 `paramAsMap` 方法是否能正确解析 headers 参数
   - `Client` 的发送方法是否支持带 headers 的消息

下一步：如果需要确认上述假设，可以检查相关类的代码
</thinking>

需要我检查 `HermesMessage` 类或 `Client` 类的实现吗？