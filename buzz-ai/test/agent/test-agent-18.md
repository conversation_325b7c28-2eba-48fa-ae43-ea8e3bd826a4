2025-07-02 17:24

<thinking>
1. 首先需要查看当前的 ProducerController.java 文件内容，了解现有的消息发送逻辑
2. 需要确定如何修改代码来支持 header 的设置
3. 需要了解 Kafka 消息头(headers)的 API 使用方式
4. 修改后需要确保不影响现有功能

根据环境信息，文件路径是: hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java

首先使用 read_file 工具查看当前文件内容
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
1. 需要修改 HermesMessage 类来支持 headers，但首先需要确认这个类是否存在 headers 字段
2. 需要查看 Client 类的 send 和 sendBatch 方法签名，确认它们是否支持 headers
3. 修改 ProducerController 的 sendSingle 和 sendBatch 方法，从请求中获取 headers 并设置到消息中

首先需要检查 HermesMessage 类的定义，看看是否已经有 headers 字段
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>
</read_file><thinking>
既然 HermesMessage 类不在当前路径，我们需要查看 Client 类的实现，了解消息发送接口是否支持 headers。

接下来查看 Client 接口的定义，确认 send 方法是否支持 headers 参数
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/Client.java</path>
</read_file><thinking>
1. Client 接口的 send 方法使用 HermesMessage 作为参数，但没有直接支持 headers
2. 需要查看 HermesMessage 的实现类，可能在 hermes-core 模块中
3. 需要先找到 HermesMessage 的实现类路径

使用 search_files 工具搜索 HermesMessage 的实现类
</thinking>

<search_files>
<keyword>HermesMessage</keyword>
</search_files><thinking>
找到了 HermesMessage 的路径: hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java

现在需要查看这个类的定义，确认是否支持 headers 字段
</thinking>

<read_file>
<path>hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>
</read_file><thinking>
1. HermesMessage 类目前没有 headers 字段，但 Builder 内部类有 headers 字段
2. 需要修改 HermesMessage 类，添加 headers 字段和相应的方法
3. 需要修改 build() 方法，将 headers 设置到 HermesMessage 实例中
4. 然后修改 ProducerController 的 sendSingle 和 sendBatch 方法，从请求中获取 headers 并设置到消息中

首先需要修改 HermesMessage 类，添加 headers 支持
</thinking>

<apply_diff>
<path>hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>
<diff>
<<<<<<< SEARCH
    private UUID messageId = UUID.randomUUID();

    public HermesMessage() {
    }

    public HermesMessage(byte[] key, byte[] data) {
        this.key = key;
        this.data = data;
    }

    public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
    }
=======
    private UUID messageId = UUID.randomUUID();
    private Map<String, byte[]> headers = new HashMap<>();

    public HermesMessage() {
    }

    public HermesMessage(byte[] key, byte[] data) {
        this.key = key;
        this.data = data;
    }

    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
    }

    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp, Map<String, byte[]> headers) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
        this.headers = headers;
    }
>>>>>>> REPLACE

<<<<<<< SEARCH
        public HermesMessage build() {
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
        }
=======
        public HermesMessage build() {
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp, this.headers);
        }
>>>>>>> REPLACE
</diff>
</apply_diff>