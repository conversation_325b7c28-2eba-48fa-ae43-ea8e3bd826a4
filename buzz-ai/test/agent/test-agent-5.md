2025-07-01 15:57

我将帮助你优化 ProducerController，为发送消息增加 header 并设置到 Kafka。首先，我需要查看当前的 ProducerController.java 文件内容，了解现有的实现方式。

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
</devin>

查看完文件内容后，我会根据现有代码结构为你添加 header 支持功能。我将为 ProducerController 添加 header 支持功能。根据代码分析，我们需要修改以下几个地方：

1. 首先需要修改 HermesMessage 类（虽然这里看不到定义）来支持 headers，但假设它已经有这个功能
2. 修改 sendSingle 和 sendBatch 方法从请求中提取 headers
3. 修改 publish 方法也支持 headers

以下是修改后的代码：

```java
// 在 sendSingle 方法中添加 headers 支持
@RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
    String topic = restRequest.param("topic");
    String ack = restRequest.param("ack", "1");
    long timeout = restRequest.paramAsLong("timeout", 5000l);
    HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
    
    // 从请求头中提取 headers 并设置到消息中
    Map<String, String> headers = extractHeadersFromRequest(restRequest);
    if (headers != null && !headers.isEmpty()) {
        hermesMessage.setHeaders(headers);
    }
    
    validateSingleMessage(hermesMessage, topic);
    StopWatch watch = StopWatch.create().start();
    ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
        log.info("sendSingle, topic {} client {} offset {} cost(ms) {}",
                topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed());
    });
    client.send(ack, hermesMessage, timeout, actionListener);
}

// 在 sendBatch 方法中添加 headers 支持
@RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
    String topic = restRequest.param("topic");
    String ack = restRequest.param("ack", "1");
    long timeout = restRequest.paramAsLong("timeout", 5000l);
    String trace = restRequest.param("trace", "off");
    List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);

    // 从请求头中提取 headers 并设置到每条消息中
    Map<String, String> headers = extractHeadersFromRequest(restRequest);
    if (headers != null && !headers.isEmpty()) {
        for (HermesMessage message : messages) {
            message.setHeaders(headers);
        }
    }
    
    validateBatchMessages(messages, topic);
    StopWatch watch = StopWatch.create().start();
    ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
        log.info("sendBatch topic {} client {} offset {} cost(ms) {}",
                topic, restChannel.getClientIp(), r.getMessageMetaList().get(0).getOffset(), watch.stop().elapsed());
    });
    if (StringUtils.equalsIgnoreCase("on", trace)) {
        client.sendBatch(ack, messages, timeout, actionListener);
    } else {
        client.sendBatchByOneFlush(ack, messages, timeout, actionListener);
    }
}

// 在 publish 方法中添加 headers 支持
@RequestMapping(value = {"/kafka/publish", "/kafka-proxy/kafka/publish"}, method = POST)
public void publish(RestRequest restRequest, RestChannel restChannel) throws UnsupportedEncodingException {
    String topic = parseTopic(restRequest);
    final String msgKey = restRequest.param("message_key");
    final String msgVal = restRequest.param("message");
    Assert.isTrue(StringUtils.isNotEmpty(msgVal), Errors.ILLEGAL_PARAMETER, "message cannot be empty");
    HermesMessage hermesMessage = new HermesMessage();
    hermesMessage.setTopic(topic);
    if (StringUtils.isNotBlank(msgKey)) {
        hermesMessage.setKey(msgKey.getBytes("UTF-8"));
    }
    hermesMessage.setData(msgVal.getBytes("UTF-8"));
    
    // 从请求头中提取 headers 并设置到消息中
    Map<String, String> headers = extractHeadersFromRequest(restRequest);
    if (headers != null && !headers.isEmpty()) {
        hermesMessage.setHeaders(headers);
    }
    
    StopWatch watch = StopWatch.create().start();
    ActionListener<MessageMeta> actionListener = ActionListener.wrap((ret, e) -> {
        if (ret != null) {
            log.info("publish, topic {} client {} offset {} cost(ms) {}",
                    topic, restChannel.getClientIp(), ret.getOffset(), watch.stop().elapsed());
        }else{
            log.error("publish error",e);
        }
        restChannel.sendResponse(RestResponse.text("ok"));
    });
    client.send("1", hermesMessage, 20000, actionListener);
}

// 新增方法：从请求中提取 headers
private Map<String, String> extractHeadersFromRequest(RestRequest restRequest) {
    Map<String, String> headers = new HashMap<>();
    // 提取所有以 "X-Kafka-Header-" 开头的请求头
    for (String headerName : restRequest.getHeaders().keySet()) {
        if (headerName.startsWith("X-Kafka-Header-")) {
            String headerKey = headerName.substring("X-Kafka-Header-".length());
            headers.put(headerKey, restRequest.header(headerName));
        }
    }
    return headers.isEmpty() ? null : headers;
}
```

主要变更点：

1. 新增了 `extractHeadersFromRequest` 方法，用于从请求头中提取 Kafka headers（约定以 "X-Kafka-Header-" 开头）
2. 在三个发送方法中都添加了 headers 提取和设置的逻辑
3. 对于批量发送，将相同的 headers 设置到每条消息中

注意事项：

1. 需要确保 HermesMessage 类有 `setHeaders` 方法
2. 客户端发送请求时需要将 Kafka headers 以 "X-Kafka-Header-YourHeaderName" 的形式放在请求头中
3. 批量发送时所有消息会共享相同的 headers

需要我继续查看 HermesMessage 类的定义来确认 headers 支持情况吗？