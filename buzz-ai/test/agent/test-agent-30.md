2025-07-11 18:51

<thinking>
1. 任务分析：需要优化 ProducerController，为发送消息增加 header 并设置到 Kafka
2. 从环境信息中可以看到文件路径：hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
3. 首先需要查看当前 ProducerController 的实现，了解如何发送消息
4. 然后需要了解如何为 Kafka 消息添加 headers
5. 最后修改代码实现 header 功能

需要使用的工具：
1. 首先使用 read_file 读取 ProducerController.java 文件内容
2. 根据现有代码结构决定如何修改
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
1. 分析当前代码：
   - 消息发送主要通过 `sendSingle` 和 `sendBatch` 方法
   - 消息体使用 `HermesMessage` 类封装
   - 通过 `client.send()` 和 `client.sendBatch()` 发送消息

2. 需要修改的内容：
   - 需要从请求中获取 headers 参数
   - 需要将 headers 设置到 `HermesMessage` 中
   - 需要确保 `HermesMessage` 类支持 headers 字段

3. 修改方案：
   - 首先检查 `HermesMessage` 类是否已有 headers 字段
   - 如果没有，需要先修改 `HermesMessage` 类
   - 然后修改 `ProducerController` 从请求中获取 headers 并设置到消息中

4. 下一步行动：
   - 先检查 `HermesMessage` 类的定义
</thinking>

<codebase_search>
<query>class HermesMessage</query>
<path>hermes-core/src/main/java/com/wacai/hermes/message</path>
</codebase_search>