2025-06-16 15:55

根据代码分析，我需要优化 ProducerController 和 KafkaProducerWrapper 来支持 Kafka 消息头的设置。以下是修改方案：

1. 首先修改 KafkaProducerWrapper 的 buildFromHermesMessage 方法，使其支持设置 Kafka 消息头：

```java hermes-core/src/main/java/com/wacai/hermes/core/message/KafkaProducerWrapper.java
protected ProducerRecord buildFromHermesMessage(HermesMessage message) {
    ProducerRecord<byte[], byte[]> record =
            new ProducerRecord(message.getTopic(), message.getPartition(), null, message.getKey(), message.getData());
    
    // Add headers support
    if (message.getHeaders() != null && !message.getHeaders().isEmpty()) {
        for (Map.Entry<String, byte[]> entry : message.getHeaders().entrySet()) {
            record.headers().add(entry.getKey(), entry.getValue());
        }
    }
    
    return record;
}
```

2. 检查 ProducerController 的代码，发现已经实现了从请求头中提取 header 并设置到 HermesMessage 中，这部分代码已经存在：

```java hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
// 在 sendSingle 方法中已有：
Map<String, String> headers = restRequest.headers();
if (headers != null && !headers.isEmpty()) {
    Map<String, byte[]> headerBytes = new HashMap<>();
    headers.forEach((k, v) -> {
        if (v != null) {
            headerBytes.put(k, v.getBytes());
        }
    });
    hermesMessage.setHeaders(headerBytes);
}

// 在 sendBatch 方法中已有：
Map<String, String> headers = restRequest.headers();
if (headers != null && !headers.isEmpty()) {
    Map<String, byte[]> headerBytes = new HashMap<>();
    headers.forEach((k, v) -> {
        if (v != null) {
            headerBytes.put(k, v.getBytes());
        }
    });
    messages.forEach(msg -> msg.setHeaders(headerBytes));
}

// 在 publish 方法中已有：
Map<String, String> headers = restRequest.headers();
if (headers != null && !headers.isEmpty()) {
    Map<String, byte[]> headerBytes = new HashMap<>();
    headers.forEach((k, v) -> {
        if (v != null) {
            headerBytes.put(k, v.getBytes("UTF-8"));
        }
    });
    hermesMessage.setHeaders(headerBytes);
}
```

3. 修改已经完成，现在 Kafka 消息头功能已经实现。当客户端发送请求时，可以通过 HTTP 请求头传递自定义的 header，这些 header 会被自动设置到 Kafka 消息中。

主要变更点：
1. 在 KafkaProducerWrapper 中添加了对 Kafka 消息头的支持
2. ProducerController 已经实现了从 HTTP 请求头提取 header 并设置到 HermesMessage 中

这些修改保持了向后兼容性，不会影响现有功能。