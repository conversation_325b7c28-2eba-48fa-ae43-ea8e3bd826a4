2025-07-03 16:02

<thinking> 
To optimize the `ProducerController` for sending messages with headers to Kafka, I will first need to read the contents of the `ProducerController.java` file to understand its current implementation. This will allow me to identify where to add the header functionality. 
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
The `ProducerController` class already has functionality for sending messages with headers to Kafka. The `sendSingle`, `sendBatch`, and `publish` methods all include logic to parse headers from the `RestRequest` and set them in the `HermesMessage`. 

To optimize this, I will ensure that the header handling is consistent and possibly refactor the header parsing logic to avoid redundancy. This will improve maintainability and clarity. 

Next, I will implement the changes and then update the file accordingly.
</thinking>

<apply_diff>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
<diff>
<<<<<<< SEARCH
:start_line:1
-------
import java.nio.charset.StandardCharsets;
=======
import java.nio.charset.StandardCharsets;
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:45
-------
        Map<String, byte[]> headers = parseHeaders(restRequest);
        if (headers != null && !headers.isEmpty()) {
            hermesMessage.setHeaders(headers);
        }
=======
        setHeaders(hermesMessage, restRequest);
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:78
-------
        Map<String, byte[]> headers = parseHeaders(restRequest);
        if (headers != null && !headers.isEmpty()) {
            messages.forEach(msg -> msg.setHeaders(headers));
        }
=======
        setHeaders(messages, restRequest);
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:112
-------
        Map<String, byte[]> headers = parseHeaders(restRequest);
        if (headers != null && !headers.isEmpty()) {
            hermesMessage.setHeaders(headers);
        }
=======
        setHeaders(hermesMessage, restRequest);
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:118
-------
    private Map<String, byte[]> parseHeaders(RestRequest request) {
        Map<String, byte[]> headers = new HashMap<>();
        // 获取特定的 headers
        String[] headerKeys = {"x-hermes-trace-id", "x-hermes-tag", "x-hermes-source"};
        for (String key : headerKeys) {
            String value = request.header(key);
            if (value != null && !value.isEmpty()) {
                try {
                    headers.put(key.substring(2), value.getBytes("UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    log.warn("Failed to encode header value for key: {}", key, e);
                }
            }
        }
        return headers.isEmpty() ? null : headers;
    }
=======
    private void setHeaders(HermesMessage hermesMessage, RestRequest request) {
        Map<String, byte[]> headers = parseHeaders(request);
        if (headers != null && !headers.isEmpty()) {
            hermesMessage.setHeaders(headers);
        }
    }

    private void setHeaders(List<HermesMessage> messages, RestRequest request) {
        Map<String, byte[]> headers = parseHeaders(request);
        if (headers != null && !headers.isEmpty()) {
            messages.forEach(msg -> msg.setHeaders(headers));
        }
    }
>>>>>>> REPLACE
</diff>
</apply_diff>I've optimized the `ProducerController` by refactoring the header handling logic to reduce redundancy. The methods now utilize a common `setHeaders` method for both single and batch message sending, improving maintainability. 

The changes have been successfully applied.