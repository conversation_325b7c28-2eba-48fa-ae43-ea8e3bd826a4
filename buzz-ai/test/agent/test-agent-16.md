2025-07-02 17:22

<thinking>
1. 首先需要查看当前的 `ProducerController.java` 文件内容，了解现有的消息发送逻辑
2. 根据任务要求，需要修改代码以支持在发送消息时添加 header 并设置到 Kafka
3. 需要确认 Kafka 生产者相关的代码位置和实现方式
4. 需要确保修改后的代码与现有架构兼容

第一步应该是读取 `ProducerController.java` 文件内容
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
1. 当前代码分析：
   - 消息发送主要通过 `sendSingle` 和 `sendBatch` 方法
   - 消息体使用 `HermesMessage` 类封装
   - 目前没有处理 header 的逻辑

2. 修改方案：
   - 需要修改 `HermesMessage` 类添加 header 字段（需要先查看这个类）
   - 修改 `ProducerController` 从请求中获取 header 并设置到消息中
   - 确保 `Client` 类能正确处理带 header 的消息

3. 下一步：
   - 首先需要查看 `HermesMessage` 类的定义
   - 路径可能在 `hermes-core` 或 `hermes-message` 模块中
</thinking>

<search_files>
<keyword>HermesMessage</keyword>
</search_files>