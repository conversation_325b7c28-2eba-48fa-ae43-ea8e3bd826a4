2025-07-02 17:06

<thinking>
1. 目标是为 ProducerController 增加发送消息时设置 header 的功能
2. 从环境信息中看到文件路径是: hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
3. 需要先读取当前 ProducerController 的代码，了解其结构和发送消息的实现方式
4. 然后分析如何修改以支持 header 设置
5. 最后应用修改并确保与 Kafka 集成正确
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
1. 需要为 HermesMessage 增加 headers 支持，但目前代码中 HermesMessage 类没有 headers 字段
2. 需要先查看 HermesMessage 类的定义，确认是否可以添加 headers
3. 从文件结构看，HermesMessage 应该在 hermes-core 模块中
4. 需要先读取 HermesMessage 类的代码，确认其结构
</thinking>

<read_file>
<path>hermes-core/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>
</read_file><thinking>
1. 由于无法找到 HermesMessage 类，需要采用另一种方案
2. 可以在 ProducerController 中创建一个新的 MessageWithHeaders 类来包装 HermesMessage 和 headers
3. 修改发送方法，从请求中提取 headers 并设置到 Kafka 消息中
4. 需要确保 Client 类支持 headers 设置
5. 先修改 ProducerController 代码，添加 headers 支持
</thinking>

<apply_diff>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
<diff>
<<<<<<< SEARCH
import com.wacai.hermes.message.HermesMessage;
=======
import com.wacai.hermes.message.HermesMessage;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
>>>>>>> REPLACE

<<<<<<< SEARCH
    @RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
    public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
=======
    @RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
    public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
        
        // Add headers from request
        Map<String, String> headers = restRequest.headers();
        if (headers != null && !headers.isEmpty()) {
            List<Header> kafkaHeaders = new ArrayList<>();
            headers.forEach((k, v) -> {
                if (v != null) {
                    kafkaHeaders.add(new RecordHeader(k, v.getBytes(StandardCharsets.UTF_8)));
                }
            });
            hermesMessage.setHeaders(kafkaHeaders);
        }
>>>>>>> REPLACE

<<<<<<< SEARCH
    @RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
    public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        String trace = restRequest.param("trace", "off");
        List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);
=======
    @RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
    public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        String trace = restRequest.param("trace", "off");
        List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);
        
        // Add headers from request to each message
        Map<String, String> headers = restRequest.headers();
        if (headers != null && !headers.isEmpty()) {
            List<Header> kafkaHeaders = new ArrayList<>();
            headers.forEach((k, v) -> {
                if (v != null) {
                    kafkaHeaders.add(new RecordHeader(k, v.getBytes(StandardCharsets.UTF_8)));
                }
            });
            messages.forEach(msg -> msg.setHeaders(kafkaHeaders));
        }
>>>>>>> REPLACE
</diff>
</apply_diff>