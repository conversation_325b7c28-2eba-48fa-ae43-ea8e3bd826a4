2025-07-01 16:11

我将帮助优化 `ProducerController` 为发送消息增加 header 功能。首先我需要查看当前 `ProducerController` 的代码结构。

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
</devin>我将为 `ProducerController` 增加 header 功能。需要修改 `HermesMessage` 类来支持 header，并更新发送逻辑。首先我需要确认 `HermesMessage` 的结构。

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin>我先查看项目目录结构，找到 `HermesMessage` 类的位置。

<devin>
/dir:.
</devin>看来 `HermesMessage` 不在 `hermes-proxy` 模块中，可能在 `hermes-common` 或 `hermes-core` 模块中。我先检查 `hermes-core` 模块。

<devin>
/dir:hermes-core/src/main/java/com/wacai/hermes/message
</devin>现在我将查看 `HermesMessage` 类的具体实现，以便了解如何为其添加 header 支持。

<devin>
/file:hermes-core/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin>看来 `HermesMessage` 不在 `hermes-core` 模块中，可能在 `hermes-common` 模块中。我继续查找。

<devin>
/dir:hermes-common/src/main/java/com/wacai/hermes/message
</devin>现在我将查看 `HermesMessage` 类的具体实现。

<devin>
/file:hermes-common/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin>看来 `HermesMessage` 不在 `hermes-common` 模块中，可能在 `hermes-api` 模块中。我继续查找。

<devin>
/dir:hermes-api/src/main/java/com/wacai/hermes/message
</devin>现在我将查看 `HermesMessage` 类的具体实现。

<devin>
/file:hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin>根据 `HermesMessage` 的实现，我发现虽然 Builder 中有 headers 字段，但实际构建时并没有包含 headers。我需要修改 `HermesMessage` 类来支持 headers，然后更新 `ProducerController`。

首先，我将修改 `HermesMessage` 类：

1. 添加 headers 字段
2. 更新构造函数和 builder 以支持 headers
3. 更新 equals/hashCode/toString 方法

然后，我将更新 `ProducerController` 的发送方法，从请求中提取 headers 并设置到消息中。

请确认是否先修改 `HermesMessage` 类？或者您希望我直接提供完整的修改方案？