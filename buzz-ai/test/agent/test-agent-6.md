2025-07-01 16:01

我将帮助优化 [`ProducerController.java`](hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java) 来为 Kafka 消息添加 header。首先我需要查看当前控制器的实现情况。

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
</devin>我将为 `ProducerController` 添加 Kafka header 支持。以下是优化方案：

1. 首先需要修改 [`HermesMessage`](hermes-proxy/src/main/java/com/wacai/hermes/message/HermesMessage.java) 类来支持 headers（如果尚未支持）
2. 然后在 `ProducerController` 中添加 header 处理逻辑

以下是具体的代码修改建议：

```java
// 在 sendSingle 方法中添加 header 支持
@RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
    String topic = restRequest.param("topic");
    String ack = restRequest.param("ack", "1");
    long timeout = restRequest.paramAsLong("timeout", 5000l);
    HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
    
    // 添加请求头到消息
    Map<String, String> headers = extractHeaders(restRequest);
    if (headers != null && !headers.isEmpty()) {
        hermesMessage.setHeaders(headers);
    }
    
    validateSingleMessage(hermesMessage, topic);
    StopWatch watch = StopWatch.create().start();
    ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
        log.info("sendSingle, topic {} client {} offset {} cost(ms) {}",
                topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed());
    });
    client.send(ack, hermesMessage, timeout, actionListener);
}

// 在 sendBatch 方法中添加 header 支持
@RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
    String topic = restRequest.param("topic");
    String ack = restRequest.param("ack", "1");
    long timeout = restRequest.paramAsLong("timeout", 5000l);
    String trace = restRequest.param("trace", "off");
    List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);

    // 为批量消息添加相同的请求头
    Map<String, String> headers = extractHeaders(restRequest);
    if (headers != null && !headers.isEmpty()) {
        messages.forEach(msg -> msg.setHeaders(headers));
    }

    validateBatchMessages(messages, topic);
    StopWatch watch = StopWatch.create().start();
    ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
        log.info("sendBatch topic {} client {} offset {} cost(ms) {}",
                topic, restChannel.getClientIp(), r.getMessageMetaList().get(0).getOffset(), watch.stop().elapsed());
    });
    if (StringUtils.equalsIgnoreCase("on", trace)) {
        client.sendBatch(ack, messages, timeout, actionListener);
    } else {
        client.sendBatchByOneFlush(ack, messages, timeout, actionListener);
    }
}

// 新增方法：从请求中提取需要作为 Kafka header 的字段
private Map<String, String> extractHeaders(RestRequest restRequest) {
    Map<String, String> headers = new HashMap<>();
    
    // 添加常用请求头
    addHeaderIfPresent(headers, restRequest, "X-Request-ID");
    addHeaderIfPresent(headers, restRequest, "X-Trace-ID");
    addHeaderIfPresent(headers, restRequest, "X-Span-ID");
    addHeaderIfPresent(headers, restRequest, "Client-Id");
    addHeaderIfPresent(headers, restRequest, "User-Agent");
    
    return headers;
}

// 辅助方法：添加请求头到 headers map
private void addHeaderIfPresent(Map<String, String> headers, RestRequest restRequest, String headerName) {
    String value = restRequest.header(headerName);
    if (StringUtils.isNotBlank(value)) {
        headers.put(headerName, value);
    }
}
```

需要确认 [`HermesMessage`](hermes-proxy/src/main/java/com/wacai/hermes/message/HermesMessage.java) 是否已经支持 headers 属性。如果没有，需要先修改该类。

您需要我检查 [`HermesMessage`](hermes-proxy/src/main/java/com/wacai/hermes/message/HermesMessage.java) 的实现吗？或者您希望我以其他方式实现 header 支持？