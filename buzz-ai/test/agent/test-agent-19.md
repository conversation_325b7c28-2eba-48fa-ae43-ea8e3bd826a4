2025-07-02 17:33

<thinking>
1. 首先需要查看当前的 ProducerController.java 文件内容，了解现有的消息发送逻辑
2. 需要确定如何修改代码来支持 header 的添加
3. 需要了解 Kafka 消息头(headers)的设置方式
4. 修改后需要确保不破坏现有功能

根据环境信息，文件路径是: hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java

第一步应该使用 read_file 工具读取这个文件内容
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
1. 需要修改 HermesMessage 类来支持 headers，但首先需要确认这个类的定义
2. 需要修改 ProducerController 中的 sendSingle 和 sendBatch 方法来处理 headers
3. Kafka Producer 的 headers 通常使用 org.apache.kafka.common.header.Header 接口
4. 需要从 RestRequest 中获取 headers 参数并设置到 HermesMessage 中

首先需要查看 HermesMessage 类的定义，确认是否可以添加 headers 字段
</thinking>

<search_files>
<keyword>HermesMessage</keyword>
</search_files><thinking>
从搜索结果看，HermesMessage 类位于 hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
需要先查看这个类的定义，确认是否可以添加 headers 字段
</thinking>

<read_file>
<path>hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>
</read_file><thinking>
1. HermesMessage 类已经有 headers 支持，通过 Builder 模式可以设置 headers
2. 需要修改 ProducerController 的 sendSingle 和 sendBatch 方法，从请求中获取 headers 并设置到 HermesMessage 中
3. 需要确保 headers 能够正确传递到 Kafka 消息中
4. 修改点包括：
   - 在 sendSingle 方法中添加 headers 处理
   - 在 sendBatch 方法中添加 headers 处理
   - 可能需要修改 Client 接口和实现类来支持 headers

首先需要修改 ProducerController.java 文件
</thinking>

<apply_diff>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
<diff>
<<<<<<< SEARCH
:start_line:48
-------
    @RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
    public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
        validateSingleMessage(hermesMessage, topic);
=======
    @RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
    public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
        Map<String, String> headers = restRequest.headers();
        if (headers != null && !headers.isEmpty()) {
            hermesMessage = HermesMessage.builder()
                    .setTopic(hermesMessage.getTopic())
                    .setPartition(hermesMessage.getPartition())
                    .setKey(hermesMessage.getKey())
                    .setData(hermesMessage.getData())
                    .setTimestamp(hermesMessage.getTimestamp())
                    .setHeaders(convertHeaders(headers))
                    .build();
        }
        validateSingleMessage(hermesMessage, topic);
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:76
-------
    @RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
    public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        String trace = restRequest.param("trace", "off");
        List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);
=======
    @RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
    public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
        String topic = restRequest.param("topic");
        String ack = restRequest.param("ack", "1");
        long timeout = restRequest.paramAsLong("timeout", 5000l);
        String trace = restRequest.param("trace", "off");
        List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);
        Map<String, String> headers = restRequest.headers();
        if (headers != null && !headers.isEmpty()) {
            Map<String, byte[]> headerBytes = convertHeaders(headers);
            messages = messages.stream().map(msg -> 
                HermesMessage.builder()
                    .setTopic(msg.getTopic())
                    .setPartition(msg.getPartition())
                    .setKey(msg.getKey())
                    .setData(msg.getData())
                    .setTimestamp(msg.getTimestamp())
                    .setHeaders(headerBytes)
                    .build()
            ).collect(Collectors.toList());
        }
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:150
-------
    private String parseTopic(RestRequest request) {
=======
    private Map<String, byte[]> convertHeaders(Map<String, String> headers) {
        Map<String, byte[]> result = new HashMap<>();
        headers.forEach((k, v) -> {
            if (v != null) {
                result.put(k, v.getBytes(StandardCharsets.UTF_8));
            }
        });
        return result;
    }

    private String parseTopic(RestRequest request) {
>>>>>>> REPLACE
</diff>
</apply_diff>