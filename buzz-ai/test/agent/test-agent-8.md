2025-07-01 16:15

<thinking>
为了优化 `ProducerController` 为发送消息增加 header 并设置到 Kafka，我需要：

1. 首先查看当前的 `ProducerController.java` 文件内容，了解现有的消息发送逻辑
2. 确定 Kafka 消息头(header)的设置方式
3. 修改代码以支持 header 的添加

首先需要获取当前 `ProducerController.java` 的内容进行分析。
</thinking>

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
</devin><thinking>
分析当前代码后，我需要为 Kafka 消息添加 header 支持。以下是实现步骤：

1. 首先需要修改 `HermesMessage` 类以支持 headers（需要先查看这个类的定义）
2. 然后在 `ProducerController` 中：
   - 为 `sendSingle` 方法添加 header 参数处理
   - 为 `sendBatch` 方法添加 header 参数处理
   - 为 `publish` 方法添加 header 参数处理

首先需要查看 `HermesMessage` 类的定义，确认是否已经支持 headers。
</thinking>

<devin>
/file:hermes-core/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin><thinking>
由于 `HermesMessage.java` 文件不在预期的路径中，我需要先找到这个文件的确切位置。我将列出项目目录结构来定位这个文件。
</thinking>

<devin>
/dir:.
</devin><thinking>
从目录结构看，`HermesMessage` 类可能在 `hermes-common` 或 `hermes-core` 模块中。由于之前 `hermes-core` 路径不存在，我将在 `hermes-common` 中查找这个类。
</thinking>

<devin>
/file:hermes-common/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin><thinking>
看来 `HermesMessage` 类不在 `hermes-common` 模块中。我将尝试在 `hermes-api` 模块中查找这个类，因为消息定义通常放在 API 模块中。
</thinking>

<devin>
/file:hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin><thinking>
分析 `HermesMessage` 类后，我发现：

1. 虽然 `Builder` 类中有 `headers` 字段和相关方法，但 `HermesMessage` 主类中并没有暴露 headers 字段
2. 需要修改 `HermesMessage` 类以支持 headers 的存储和访问
3. 然后才能修改 `ProducerController` 来支持 headers 的设置

首先需要修改 `HermesMessage` 类添加 headers 支持。以下是修改方案：

1. 在 `HermesMessage` 类中添加 `headers` 字段
2. 添加 headers 的 getter/setter 方法
3. 修改 `Builder` 类使其正确构建带 headers 的消息
4. 修改 `equals` 和 `hashCode` 方法以包含 headers

修改完成后，才能继续修改 `ProducerController`。
</thinking>

<devin>
/file:hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin><thinking>
我将修改 `HermesMessage` 类以支持 headers。以下是具体修改方案：

1. 在 `HermesMessage` 类中添加 `headers` 字段
2. 添加 headers 的 getter/setter 方法
3. 修改构造函数和 Builder 类以支持 headers
4. 更新 `equals` 和 `hashCode` 方法
5. 更新 `toString` 方法

修改后的代码将支持从 `ProducerController` 设置消息头。
</thinking>

以下是修改后的 `HermesMessage.java` 代码：

```java
package com.wacai.hermes.message;

import com.wacai.hermes.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.wacai.hermes.constants.HermesHeader.TAG;

@Getter
@Setter
public class HermesMessage {

    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    private long timestamp;
    private UUID messageId = UUID.randomUUID();
    private Map<String, byte[]> headers = new HashMap<>();

    public HermesMessage() {
    }

    public HermesMessage(byte[] key, byte[] data) {
        this.key = key;
        this.data = data;
    }

    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this(topic, partition, key, data, timestamp, new HashMap<>());
    }

    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp, Map<String, byte[]> headers) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
        if (headers != null) {
            this.headers.putAll(headers);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    public long getPayLoad() {
        long size = 0;
        if (key != null) {
            size += key.length;
        }
        if (data != null) {
            size += data.length;
        }
        // Add headers size if needed
        return size;
    }

    public String getTraceId() {
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HermesMessage message = (HermesMessage)o;
        return timestamp == message.timestamp &&
                Objects.equals(partition, message.partition) &&
                Objects.equals(topic, message.topic) &&
                Arrays.equals(key, message.key) &&
                Arrays.equals(data, message.data) &&
                Objects.equals(messageId, message.messageId) &&
                Objects.equals(headers, message.headers);
    }

    @Override
    public int hashCode() {
        int result = Objects.hash(topic, partition, timestamp, messageId, headers);
        result = 31 * result + Arrays.hashCode(key);
        result = 31 * result + Arrays.hashCode(data);
        return result;
    }

    @Override
    public String toString() {
        return "HermesMessage:{" + messageId.toString() + 
               (key == null ? "" : ",key=" + new String(key)) + 
               (headers.isEmpty() ? "" : ",headers=" + headers) + "}";
    }

    public static class Builder {
        private String topic;
        private Integer partition;
        private byte[] key;
        private byte[] data;
        private Map<String, byte[]> headers = new HashMap<>();
        private long timestamp;

        public Builder setHeaders(Map<String, byte[]> headers) {
            if (headers != null) {
                this.headers.putAll(headers);
            }
            return this;
        }

        public Builder addHeader(String key, byte[] value) {
            if (key != null && value != null) {
                this.headers.put(key, value);
            }
            return this;
        }

        public Builder setTopic(String topic) {
            this.topic = topic;
            return this;
        }

        public Builder setPartition(Integer partition) {
            this.partition = partition;
            return this;
        }

        public Builder setKey(byte[] key) {
            this.key = key;
            return this;
        }

        public Builder setData(byte[] data) {
            this.data = data;
            return this;
        }

        public Builder setTimestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder setTag(String tag) {
            if (null != tag && !"".equals(tag = tag.trim())) {
                this.headers.put(TAG, tag.getBytes());
            }
            return this;
        }

        public HermesMessage build() {
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp, this.headers);
        }
    }
}
```

主要修改点：

1. 添加了 `headers` 字段和对应的 getter/setter
2. 新增了带 headers 参数的构造函数
3. 修改了 `Builder` 类的 `build()` 方法以包含 headers
4. 更新了 `equals()` 和 `hashCode()` 方法以包含 headers
5. 更新了 `toString()` 方法显示 headers
6. 在 `Builder` 类中添加了 `addHeader()` 方法方便添加单个 header

接下来，我将修改 `ProducerController` 来支持 headers 的设置。请确认上述修改是否可以提交。