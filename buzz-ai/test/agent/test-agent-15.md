2025-07-02 17:12

<thinking>
1. 需要修改的文件是 [`hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`](hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java)
2. 任务要求：
   - 优化 ProducerController
   - 为发送消息增加 header
   - 将 header 设置到 Kafka
3. 首先需要查看当前 ProducerController 的实现，了解如何发送消息
4. 然后确定如何添加和传递 header 信息
5. 最后修改代码实现 header 功能
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
</read_file><thinking>
1. 需要为发送消息增加 header 功能，需要修改以下方法：
   - `sendSingle()`
   - `sendBatch()`
   - `publish()`
2. 需要从 RestRequest 中获取 header 信息并设置到 HermesMessage 中
3. HermesMessage 类可能需要扩展以支持 header 功能
4. 修改方案：
   - 在 HermesMessage 中添加 headers Map 字段
   - 在发送消息前从 RestRequest 中获取 headers 并设置到 HermesMessage
   - 确保 Client 类能将 headers 传递到 Kafka
5. 需要先确认 HermesMessage 类是否已有 headers 字段
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>
</read_file><thinking>
1. 由于无法找到 [`HermesMessage.java`](hermes-proxy/src/main/java/com/wacai/hermes/message/HermesMessage.java) 文件，需要调整方案
2. 替代方案：
   - 在 ProducerController 中创建一个新的 MessageWithHeaders 类来包装 HermesMessage 和 headers
   - 或者修改 Client 接口以支持 headers 参数
3. 更合理的方案是检查 Client 接口是否已经支持 headers
4. 需要先查看 Client 接口的定义
</thinking>

<read_file>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/Client.java</path>
</read_file><thinking>
1. Client 接口目前不支持 headers 参数
2. 需要修改 Client 接口以支持 headers
3. 修改方案：
   - 在 Client 接口中添加新的方法支持 headers
   - 或者修改现有方法签名以包含 headers 参数
4. 更合理的方案是创建新的方法，保持向后兼容
5. 需要修改 ProducerController 以使用新的方法
6. 需要从 RestRequest 中提取 headers 并传递给 Client
</thinking>

<apply_diff>
<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/Client.java</path>
<diff>
<<<<<<< SEARCH
:start_line:1
-------
package com.wacai.hermes.proxy.client;

import com.wacai.hermes.admin.OffsetResp;
import com.wacai.hermes.fetch.*;
import com.wacai.hermes.admin.ConsumerOffset;
import com.wacai.hermes.admin.OffsetRange;
import com.wacai.hermes.async.ActionListener;
import com.wacai.hermes.message.BatchMessage;
import com.wacai.hermes.message.BatchMessageMeta;
import com.wacai.hermes.message.HermesMessage;
import com.wacai.hermes.message.MessageMeta;
import com.wacai.hermes.message.TopicPartition;

import java.util.List;
import java.util.Map;

/**
 * 封装了Hermes所有操作，注意所有操作必须异步
 *
 * <AUTHOR>
 * @description
 **/
public interface Client {
=======
package com.wacai.hermes.proxy.client;

import com.wacai.hermes.admin.OffsetResp;
import com.wacai.hermes.fetch.*;
import com.wacai.hermes.admin.ConsumerOffset;
import com.wacai.hermes.admin.OffsetRange;
import com.wacai.hermes.async.ActionListener;
import com.wacai.hermes.message.BatchMessage;
import com.wacai.hermes.message.BatchMessageMeta;
import com.wacai.hermes.message.HermesMessage;
import com.wacai.hermes.message.MessageMeta;
import com.wacai.hermes.message.TopicPartition;

import java.util.List;
import java.util.Map;

/**
 * 封装了Hermes所有操作，注意所有操作必须异步
 *
 * <AUTHOR>
 * @description
 **/
public interface Client {

    /**
     * 发送带header的消息
     *
     * @param ack
     * @param message
     * @param headers
     * @param timeout
     * @param actionListener
     */
    void sendWithHeaders(String ack, HermesMessage message, Map<String, String> headers, long timeout, ActionListener<MessageMeta> actionListener);

    /**
     * 批量发送带header的消息（一条条发送）
     *
     * @param ack
     * @param messages
     * @param headers
     * @param timeout
     * @param actionListener
     */
    void sendBatchWithHeaders(String ack, List<HermesMessage> messages, Map<String, String> headers, long timeout, ActionListener<BatchMessageMeta> actionListener);

    /**
     * 批量发送带header的消息(一次性flush)
     *
     * @param ack
     * @param messages
     * @param headers
     * @param timeout
     * @param actionListener
     */
    void sendBatchByOneFlushWithHeaders(String ack, List<HermesMessage> messages, Map<String, String> headers, long timeout, ActionListener<BatchMessageMeta> actionListener);
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:19
-------
    /**
     * 发送消息
     *
     * @param ack
     * @param message
     * @param timeout
     * @return
     */
    void send(String ack, HermesMessage message, long timeout, ActionListener<MessageMeta> actionListener);

    /**
     * 批量发送（一条条发送）
     *
     * @param ack
     * @param messages
     * @param timeout
     * @return
     */
    void sendBatch(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener);


    /**
     * 批量发送(一次性flush)
     *
     * @param ack
     * @param messages
     * @param timeout
     * @param actionListener
     */
    void sendBatchByOneFlush(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener);
=======
    /**
     * 发送消息
     *
     * @param ack
     * @param message
     * @param timeout
     * @return
     */
    void send(String ack, HermesMessage message, long timeout, ActionListener<MessageMeta> actionListener);

    /**
     * 批量发送（一条条发送）
     *
     * @param ack
     * @param messages
     * @param timeout
     * @return
     */
    void sendBatch(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener);


    /**
     * 批量发送(一次性flush)
     *
     * @param ack
     * @param messages
     * @param timeout
     * @param actionListener
     */
    void sendBatchByOneFlush(String ack, List<HermesMessage> messages, long timeout, ActionListener<BatchMessageMeta> actionListener);
>>>>>>> REPLACE
</diff>
</apply_diff>