package com.wacai.hermes.message;

import com.wacai.hermes.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.wacai.hermes.constants.HermesHeader.TAG;

@Getter
@Setter
public class HermesMessage {

    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    //时间戳
    private long timestamp;
    private UUID messageId = UUID.randomUUID();

    public HermesMessage() {
    }

    public HermesMessage(byte[] key, byte[] data) {
        this.key = key;
        this.data = data;
    }

    public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
    }

    public static Builder builder() {
        return new Builder();
    }

    public long getPayLoad() {
        long size = 0;
        if (key != null) {
            size += key.length;
        }
        if (data != null) {
            size += data.length;
        }
        return size;
    }

    public String getTraceId() {
        return null;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HermesMessage message = (HermesMessage)o;
        return Objects.equals(partition, message.partition) && timestamp == message.timestamp
                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
                messageId, message.messageId);
    }

    @Override
    public int hashCode() {

        int result = Objects.hash( topic, partition, timestamp, messageId);
        result = 31 * result + Arrays.hashCode(key);
        result = 31 * result + Arrays.hashCode(data);
        return result;
    }

    @Override
    public String toString() {
        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
    }

    public static class Builder {
        private String topic;
        private Integer partition;
        private byte[] key;
        private byte[] data;
        private Map<String, byte[]> headers = new HashMap<>();
        private long timestamp;

        public Builder setHeaders(Map<String, byte[]> headers) {
            // headers.entrySet().removeIf(entry -> entry.getKey().startsWith("hermes."));
            this.headers.putAll(headers);
            return this;
        }

        public Builder setTopic(String topic) {
            this.topic = topic;
            return this;
        }

        public Builder setPartition(Integer partition) {
            this.partition = partition;
            return this;
        }

        public Builder setKey(byte[] key) {
            this.key = key;
            return this;
        }

        public Builder setData(byte[] data) {
            this.data = data;
            return this;
        }

        public Builder setTimestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder setTag(String tag) {
            if (null != tag && !"".equals(tag = tag.trim())) {
                this.headers.put(TAG, tag.getBytes());
            }
            return this;
        }

        public HermesMessage build() {
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
        }
    }
}