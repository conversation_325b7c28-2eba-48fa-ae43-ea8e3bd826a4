class KafkaProducerWrapper{
    protected ProducerRecord buildFromHermesMessage(HermesMessage message) {
        ProducerRecord<byte[], byte[]> record =
                new ProducerRecord(message.getTopic(), message.getPartition(), null, message.getKey(), message.getData());

        if (message.getHeaders() != null) {
            for (Map.Entry<String, byte[]> entry : message.getHeaders().entrySet()) {
                record.headers().add(entry.getKey(), entry.getValue());
            }
        }
        return record;
    }
}