@Getter
@Setter
public class HermesMessage {
    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    private long timestamp;
    private UUID messageId = UUID.randomUUID();
    private Map<String, byte[]> headers = new HashMap<>();

    // ... existing code ...

    public static class Builder {
        private String topic;
        private Integer partition;
        private byte[] key;
        private byte[] data;
        private Map<String, byte[]> headers = new HashMap<>();
        private long timestamp;

        // ... existing code ...

        public Builder addHeader(String key, byte[] value) {
            if (key != null && value != null) {
                this.headers.put(key, value);
            }
            return this;
        }

        public HermesMessage build() {
            HermesMessage message = new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
            message.setHeaders(this.headers);
            return message;
        }
    }
}