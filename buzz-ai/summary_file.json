{"AbstractRunnable": "Summary: Abstract class implementing Runnable interface.\nClass: AbstractRunnable\nFunction: run, doRun, onFailure\nVariable: \nImport:", "NettyHttpChannel": "Summary: Implements Netty HTTP channel for REST communication.\nClass: NettyHttpChannel, LogChannelFuture\nFunction: getClientIp, getRemoteIp, sendResponse, close, addCloseListener, isOpen, getRestRequest, getHttpResponseStatus, operationComplete\nVariable: channel, httpRequest, closeContext, path\nImport: import com.wacai.hermes.async.ActionListener^^import com.wacai.hermes.async.CompletableContext^^import com.wacai.hermes.core.rest.network.NettyHttpServerTransport^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse^^import com.wacai.hermes.rest.RestStatus^^import io.netty.buffer.ByteBuf^^import io.netty.buffer.Unpooled^^import io.netty.channel.Channel^^import io.netty.channel.ChannelFuture^^import io.netty.channel.ChannelFutureListener^^import io.netty.handler.codec.http.DefaultFullHttpResponse^^import io.netty.handler.codec.http.FullHttpResponse^^import io.netty.handler.codec.http.HttpHeaderNames^^import io.netty.handler.codec.http.HttpHeaderValues^^import io.netty.handler.codec.http.HttpResponseStatus^^import io.netty.handler.codec.http.HttpVersion^^import lombok.extern.slf4j.Slf4j^^import java.net.InetSocketAddress^^import java.net.SocketAddress", "EnvUtil": "Summary:  \nClass: EnvUtil  \nVariable: ENV  \nFunction: getEnv, doGetEnv, getSystemProperty  \nImport:", "Ordered": "Summary: Defines an interface for ordered elements with priority levels.\nClass: Ordered, OrderComparator\nVariable: INSTANCE\nFunction: order, compare, getValue\nImport: import java.util.Comparator", "HermesRuntimeUtil": "Summary:  \nClass: HermesRuntimeUtil  \nFunction: get, set  \nVariable: hermesRuntime  \nImport: com.wacai.hermes.runtime.HermesRuntime", "OffsetStorage": "Summary: Interface for offset storage operations.\nFunction: getOffset, storeOffset, resetOffset\nClass: OffsetStorage\nVariable: \nImport: import com.wacai.hermes.fetch.ConsumerVo", "QueryTopicInfoCommand": "Summary: Query topic configuration information.\nClass: QueryTopicInfoCommand\nFunction: buildUrl, parseData\nVariable: \nImport: com.alibaba.fastjson.JSON^^com.alibaba.fastjson.JSONObject^^com.wacai.hermes.command.resp.TopicInfo^^java.util.List", "ExecutorBuilder": "Summary: Builds and manages thread pool executors.\nClass: Executor<PERSON><PERSON><PERSON>, FixExecutorBuilder, ScalingExecutorBuilder, ScalingRejectPolicy, ExecutorScalingQueue, ScheduledExecutorBuilder\nVariable: name, coreSize, maxSize, queueSize, info, keepAliveTime, unit, executor\nImport: import com.wacai.hermes.async.ThreadUtil^^import com.wacai.hermes.core.async.ThreadPool.ExecutorHolder^^import com.wacai.hermes.runtime.ISettings^^import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.ArrayBlockingQueue^^import java.util.concurrent.ExecutorService^^import java.util.concurrent.RejectedExecutionException^^import java.util.concurrent.RejectedExecutionHandler^^import java.util.concurrent.ScheduledThreadPoolExecutor^^import java.util.concurrent.ThreadFactory^^import java.util.concurrent.ThreadPoolExecutor^^import java.util.concurrent.TimeUnit", "MethodHandler": "Summary: \nClass: MethodHandler\nVariable: method, path, restHandler\nImport: import com.wacai.hermes.rest.RestHandler^^import com.wacai.hermes.rest.RestRequest^^import lombok.AllArgsConstructor^^import lombok.Data", "ProxyConfigMode": "Summary: Defines an enumeration for proxy configuration modes.  \nClass: ProxyConfigMode  \nFunction:  \nVariable:  \nImport:", "OffsetResetPolicy": "Summary: Mainly used to handle offset reset policies.\nClass: OffsetResetPolicy\nFunction: resetOffset\nVariable: startNextOffset, tp\nImport: import com.wacai.hermes.admin.OffsetStorage^^import com.wacai.hermes.fetch.FetchRequest^^import com.wacai.hermes.message.ConsumerWrapper^^import com.wacai.hermes.message.TopicPartition^^import lombok.extern.slf4j.Slf4j", "Assert": "Summary: Mainly used for assertion utilities in Hermes.\nClass: Assert\nFunction: notNull, isNotBlank, isTrue\nVariable: \nImport: import com.wacai.hermes.errors.Errors", "AsyncAlert": "Summary: Mainly used for asynchronous alert handling.\nClass: AsyncAlert\nFunction: send, run, batchSend\nVariable: queue, report, thread, lastSendTime, parcel, msg, now, e\nImport: import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.alert.Alert^^import lombok.extern.slf4j.Slf4j^^import java.util.Collection^^import java.util.HashSet^^import java.util.Set^^import java.util.concurrent.ArrayBlockingQueue^^import java.util.concurrent.TimeUnit", "HermesHeader": "Summary: Defines constants for <PERSON><PERSON> headers.\nClass: HermesHeader\nVariable: TRACE_ID, SEND_TIME, IP, APP_NAME, TAG, ENCODE, CREATOR, ORIGINAL_TOPIC, ORIGINAL_PARTITION, ORIGINAL_OFFSET\nFunction:\nImport:", "BaseBootstrap": "Summary: Mainly used to provide automatic implementation of function templates.\nClass: BaseBootstrap\nFunction: registerStartListener, registerStoppable, install, start, doStart, getSettings, getStage, stop, prepareStop, doStop\nVariable: keepAliveThread, keepAliveLatch, startListenerList, stoppableList, moduleList, settings, hermesRuntime\nImport: import com.google.inject.Guice^^import com.google.inject.Module^^import com.google.inject.Stage^^import com.wacai.hermes.bootstrap.Bootstrap^^import com.wacai.hermes.bootstrap.BootstrapAware^^import com.wacai.hermes.bootstrap.Ordered^^import com.wacai.hermes.bootstrap.StartListener^^import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.core.runtime.DefaultHermesRuntime^^import com.wacai.hermes.core.runtime.HermesRuntimeUtil^^import com.wacai.hermes.core.runtime.Settings^^import com.wacai.hermes.core.util.StopWatch^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.runtime.ISettings.Loader^^import com.wacai.hermes.util.EnvUtil^^import com.wacai.hermes.util.EnvUtil.Env^^import lombok.extern.slf4j.Slf4j^^import java.util.ArrayList^^import java.util.Collections^^import java.util.List^^import java.util.concurrent.CountDownLatch", "RestUtils": "Summary: Mainly used for URL decoding and query string processing.\nClass: RestUtils\nFunction: decodeURL, decodeQueryString\nVariable: \nImport: import lombok.extern.slf4j.Slf4j^^import java.io.UnsupportedEncodingException^^import java.net.URLDecoder^^import java.util.Map", "ProducerController": "Summary: Mainly used to handle message sending operations in a proxy controller.\nClass: ProducerController\nFunction: sendSingle^^sendBatch^^validateSingleMessage^^validateBatchMessages^^publish^^parseTopic\nVariable: messageMaxSize^^client\nImport: import com.google.inject.Inject^^import com.wacai.hermes.async.ActionListener^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.core.rest.handler.BaseRestHandler^^import com.wacai.hermes.core.util.StopWatch^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.message.BatchMessageMeta^^import com.wacai.hermes.message.HermesMessage^^import com.wacai.hermes.message.MessageMeta^^import com.wacai.hermes.proxy.client.Client^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.Assert^^import lombok.extern.slf4j.Slf4j^^import org.apache.commons.lang3.StringUtils^^import java.io.UnsupportedEncodingException^^import java.util.HashMap^^import java.util.List^^import java.util.Map^^import static com.wacai.hermes.rest.RestRequest.Method.POST", "FetchRequest": "Summary: \nClass: FetchRequest\nVariable: requestId, fetcherId, fetchCnt, reset, fetchLevel, fetchMax, pollMs, timestamp, manualOffset, filterExpress, tag, trace, rewrite, noProducer\nFunction: isManual, isResetBegin\nImport: import com.wacai.hermes.util.StringUtils^^import lombok.Getter^^import lombok.Setter", "Fetcher": "Summary: Interface for fetching and managing messages.\nClass: Fetcher\nFunction: isNoProducer, fetch, ack, renew, resetOffset\nVariable: \nImport: import com.wacai.hermes.message.BatchMessage", "LockRequest": "Summary: \nClass: LockRequest\nVariable: lockMs\nImport: import lombok.Getter^^import lombok.Setter", "Settings": "Summary: Mainly used to provide settings and configuration management.\nClass: Settings\nFunction: Settings putAll set getString containsKey get getAllKeys getLong getInt getBoolean getEnum remove load prepareEnvironment configureLogging\nVariable: PROPERTIES_FILE_NAME properties\nImport: import ch.qos.logback.classic.LoggerContext^^import ch.qos.logback.classic.joran.JoranConfigurator^^import com.wacai.hermes.core.runtime.loader.ClassPathLoader^^import com.wacai.hermes.core.runtime.loader.FilePathLoader^^import com.wacai.hermes.core.runtime.loader.FilterLoader^^import com.wacai.hermes.runtime.ISettings^^import org.slf4j.LoggerFactory^^import java.io.FileInputStream^^import java.io.InputStream^^import java.net.URL^^import java.util.*^^import java.util.stream.Collectors^^import java.util.stream.Stream", "NettyHttpServerTransport": "Summary: Mainly used to provide network transport functionality.\nClass: NettyHttpServerTransport, HttpChannelHandler\nVariable: SERVER_PORT, SERVER_WORKER_COUNT, SERVER_READ_TIMEOUT, settings, serverBootstrap, dispatcher, workerCount, readTimeout<PERSON>illis, HTTP_CHANNEL_KEY\nFunction: NettyHttpServerTransport, doStart, configureServerChannelHandler, bindServer, dispatchRequest, exceptionCaught, initChannel, doStop, addListener\nImport: import com.google.inject.Inject^^import com.wacai.hermes.async.CompletableContext^^import com.wacai.hermes.component.AbstractLifecycleComponent^^import com.wacai.hermes.core.rest.NettyHttpChannel^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.network.HttpServerTransport^^import com.wacai.hermes.runtime.ISettings^^import io.netty.bootstrap.ServerBootstrap^^import io.netty.channel.Channel^^import io.netty.channel.ChannelFuture^^import io.netty.channel.ChannelHandler^^import io.netty.channel.ChannelHandlerContext^^import io.netty.channel.ChannelInitializer^^import io.netty.channel.ChannelOption^^import io.netty.channel.nio.NioEventLoopGroup^^import io.netty.channel.socket.nio.NioServerSocketChannel^^import io.netty.handler.codec.http.HttpObjectAggregator^^import io.netty.handler.codec.http.HttpServerCodec^^import io.netty.handler.timeout.ReadTimeoutException^^import io.netty.handler.timeout.ReadTimeoutHandler^^import io.netty.util.AttributeKey^^import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.TimeUnit^^import static com.wacai.hermes.async.ThreadUtil.daemonThreadFactory^^import static com.wacai.hermes.async.ThreadUtil.numberOfProcessors", "ThreadDumpUtil": "Summary: Utility for thread dump handling.\nClass: ThreadDumpUtil\nVariable: event_queue, output\nFunction: dump, handleEvent, handleThreadDump\nImport: import com.wacai.common.middleware.util.IOUtils^^import com.wacai.common.middleware.util.JVMToolkit^^import lombok.extern.slf4j.Slf4j^^import java.io.File^^import java.io.IOException^^import java.util.concurrent.ArrayBlockingQueue", "FetcherScheduler": "Summary: Mainly used to schedule and manage fetch tasks.\nClass: FetcherScheduler\nVariable: TASK_TYPE, TASK_TIMEOUT, fetcher, logEnable\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.async.Task^^import com.wacai.hermes.core.async.AbstractScheduler^^import com.wacai.hermes.core.async.ThreadPool.Names^^import com.wacai.hermes.errors.FetchLockException^^import com.wacai.hermes.fetch.FetchRequest^^import com.wacai.hermes.fetch.Fetcher^^import com.wacai.hermes.message.BatchMessage^^import com.wacai.hermes.runtime.HermesRuntime^^import lombok.extern.slf4j.Slf4j", "ZkException": "Summary: Represents exceptions related to ZooKeeper operations.\nClass: ZkException\nFunction: ZkException^^ZkException(String message)^^ZkException(Throwable cause)^^ZkException(String message, Throwable cause)\nVariable: serialVersionUID\nImport:", "Harmony": "Summary: Compatibility with historical logic for Harmony registration.\nClass: Harmony, RegisterListener, UnRegisterListener\nFunction: register, unregister, getReporterDTO\nVariable: stopped\nImport: import com.wacai.harmony.client.Harmony.Reporter^^import com.wacai.harmony.common.constants.ReportTypeEnum^^import com.wacai.harmony.common.dto.ReporterDTO^^import com.wacai.harmony.common.util.EnvUtils^^import com.wacai.hermes.bootstrap.StartListener^^import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.IPUtil^^import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.atomic.AtomicBoolean", "HttpUtil": "Summary: Utility class for HTTP operations.\nClass: HttpUtil, Builder\nFunction: httpPost<PERSON>son, httpPost, setHeaders, builder, connectTimeout, readTimeout, build\nVariable: connectTimeout, readTimeout\nImport: import com.alibaba.fastjson.JSON^^import com.alibaba.fastjson.JSONObject^^import com.wacai.common.middleware.util.IOUtils^^import com.wacai.hermes.errors.Errors.HttpStatusException^^import com.wacai.hermes.errors.Errors.NetErrorException^^import java.io.InputStream^^import java.net.HttpURLConnection^^import java.net.URL^^import java.util.HashMap^^import java.util.Iterator^^import java.util.Map", "AckRequest": "Summary: \nClass: AckRequest\nImport:", "ClientImpl": "Summary: Mainly used to implement client functionality for Hermes proxy.\nClass: ClientImpl\nFunction: runAsync, send, sendBatch, sendBatchByOneFlush, fetch, longFetch, ack, renew, getConsumerOffset, getTopicOffsetRange, resetOffset, getOffset\nVariable: threadPool, fetchTaskManager, fetcher, produceService, metadataManager, alert\nImport: com.google.inject.Inject^^com.google.inject.Singleton^^com.wacai.hermes.admin.ConsumerOffset^^com.wacai.hermes.admin.MetadataManager^^com.wacai.hermes.admin.OffsetRange^^com.wacai.hermes.admin.OffsetResp^^com.wacai.hermes.alert.AlarmMsg^^com.wacai.hermes.alert.Alert^^com.wacai.hermes.async.AbstractRunnable^^com.wacai.hermes.async.ActionListener^^com.wacai.hermes.async.Task^^com.wacai.hermes.core.async.ThreadPool^^com.wacai.hermes.core.async.ThreadPool.Names^^com.wacai.hermes.core.util.StopWatch^^com.wacai.hermes.errors.ActionTimeoutException^^com.wacai.hermes.fetch.*^^com.wacai.hermes.message.*^^com.wacai.hermes.proxy.fetch.impl.FetchTaskManager^^com.wacai.hermes.proxy.fetch.impl.FetcherScheduler^^com.wacai.hermes.proxy.fetch.impl.LongFetcherScheduler^^com.wacai.hermes.proxy.produce.ProduceService^^lombok.extern.slf4j.Slf4j^^java.util.List^^java.util.concurrent.RejectedExecutionException^^java.util.function.Supplier", "RestChannel": "Summary: Interface for REST channel operations.\nClass: RestChannel\nImport: import com.wacai.hermes.async.ActionListener^^import java.io.Closeable", "FetchLockException": "Summary: Mainly used to define a custom exception for fetch lock errors.\nClass: FetchLockException\nVariable: serialVersionUID\nFunction: FetchLockException\nImport:", "RestModule": "Summary: Mainly used to configure REST module components.\nClass: RestModule\nFunction: doConfigure\nVariable: restController, transport\nImport: com.wacai.hermes.core.bootstrap.BaseModule^^com.wacai.hermes.core.rest.RestController^^com.wacai.hermes.core.rest.handler.FaviconHandler^^com.wacai.hermes.core.rest.handler.HealthHandler^^com.wacai.hermes.core.rest.handler.PrometheusHandler^^com.wacai.hermes.core.rest.network.NettyHttpServerTransport^^com.wacai.hermes.rest.network.HttpServerTransport", "ZkClientImpl": "Summary: Mainly used to provide Zookeeper client implementation.\nClass: ZkClientImpl\nFunction: getData, create, setData, casUpdate, close, stop, order\nVariable: client\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.common.middleware.util.StringUtils^^import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.errors.OffsetIgnoreException^^import com.wacai.hermes.errors.ZkException^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.zk.ZkClient^^import lombok.extern.slf4j.Slf4j^^import org.apache.curator.framework.CuratorFramework^^import org.apache.curator.framework.CuratorFrameworkFactory^^import org.apache.curator.retry.ExponentialBackoffRetry^^import org.apache.zookeeper.CreateMode^^import org.apache.zookeeper.KeeperException^^import org.apache.zookeeper.data.Stat^^import java.io.IOException", "KafkaModule": "Summary: Mainly used to configure Kafka modules.\nClass: KafkaModule\nFunction: doConfigure configKafkaAdmin configKafkaConsumer configKafkaProducer getProducerPool\nVariable: brokerList settings kafkaAdminClient producerOnePool producerTwoPool producerThreePool list\nImport: import com.google.inject.TypeLiteral^^import com.google.inject.name.Names^^import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.core.bootstrap.BaseModule^^import com.wacai.hermes.core.message.KafkaClientBuilder^^import com.wacai.hermes.core.message.KafkaConsumerWrapper^^import com.wacai.hermes.core.message.KafkaProducerWrapper^^import com.wacai.hermes.message.ConsumerThreadLocal^^import com.wacai.hermes.message.ProducerPool^^import com.wacai.hermes.message.ProducerWrapper^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.runtime.ISettings^^import lombok.extern.slf4j.Slf4j^^import org.apache.kafka.clients.admin.KafkaAdminClient^^import java.util.ArrayList^^import java.util.List^^import static com.wacai.hermes.runtime.ISettings.*", "RestHandler": "Summary: Handler for REST requests  \nClass: RestHandler  \nFunction: handRequest  \nVariable:  \nImport:", "BaseRestHandler": "Summary: \nClass: BaseRestHandler\nFunction: handRequest, getActionListener\nVariable: alert\nImport: import com.google.inject.Inject^^import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.alert.Alert^^import com.wacai.hermes.async.ActionListener^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.errors.FetchLockException^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestHandler^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse^^import lombok.extern.slf4j.Slf4j", "RestStatus": "Summary: Defines REST status codes and their mappings.\nClass: RestStatus\nVariable: status, CODE_TO_STATUS\nFunction: getStatus, fromCode\nImport: import java.util.HashMap^^import java.util.Map^^import static java.util.Collections.unmodifiableMap", "HermesMessage": "Summary: Mainly used to define HermesMessage class and its builder.\nClass: HermesMessage, Builder\nFunction: HermesMessage, getPayLoad, getTraceId, equals, hashCode, toString, setHeaders, setTopic, setPartition, setKey, setData, setTimestamp, setTag, build\nVariable: topic, partition, key, data, timestamp, messageId, headers\nImport: import com.wacai.hermes.util.StringUtils^^import lombok.Getter^^import lombok.Setter^^import java.util.Arrays^^import java.util.HashMap^^import java.util.Map^^import java.util.Objects^^import java.util.UUID", "TopicPartition": "Summary: Mainly used to define a topic partition structure.\nClass: TopicPartition\nVariable: topic, partition\nFunction: \nImport: import lombok.AllArgsConstructor^^import lombok.Data^^import lombok.NoArgsConstructor", "BaseModule": "Summary: Abstract base module for G<PERSON>ce configuration.\nClass: BaseModule\nFunction: setBootstrap, bindSPI, configure, doConfigure\nVariable: bootstrap, settings\nImport: com.google.inject.AbstractModule^^com.google.inject.multibindings.Multibinder^^com.wacai.hermes.bootstrap.Bootstrap^^com.wacai.hermes.bootstrap.BootstrapAware^^com.wacai.hermes.runtime.ISettings", "AbstractLifecycleComponent": "Summary: Abstract class for lifecycle management.\nClass: AbstractLifecycleComponent\nFunction: start, doStart, stop, doStop, addLifecycleListener, removeLifecycleListener\nVariable: listeners, stopped\nImport: import com.wacai.hermes.bootstrap.StartListener^^import java.util.List^^import java.util.concurrent.CopyOnWriteArrayList^^import java.util.concurrent.atomic.AtomicBoolean^^import java.util.stream.Collectors", "PrometheusHandler": "Summary:  \nClass: PrometheusHandler  \nFunction: PrometheusHandler, checkReadiness  \nVariable:  \nImport: import com.google.inject.Inject^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse", "BatchMessageMeta": "Summary: Mainly used to define batch message metadata.\nClass: BatchMessageMeta\nVariable: messageMetaList, proxyCostUs\nImport: import lombok.Data^^import java.util.ArrayList^^import java.util.List", "AbstractFetcher": "Summary: Abstract class for fetching messages with lock management.\nClass: AbstractFetcher\nFunction: rewriteFetchRequest, getOffsetStorage, tryLock0, releaseLock0, tryLock, releaseLock, validate, fetch, ack, getNextOffset, buildPullParam\nVariable: metadataManager, monitorTimeoutMs, lockCount\nImport: com.google.inject.Inject^^com.wacai.hermes.admin.MetadataManager^^com.wacai.hermes.admin.OffsetStorage^^com.wacai.hermes.errors.Errors^^com.wacai.hermes.errors.FetchLockException^^com.wacai.hermes.errors.OffsetOutOfRangeException^^com.wacai.hermes.errors.PartitionNotAssignException^^com.wacai.hermes.fetch.AckRequest^^com.wacai.hermes.fetch.AckResult^^com.wacai.hermes.fetch.ConsumerVo^^com.wacai.hermes.fetch.FetchRequest^^com.wacai.hermes.message.BatchMessage^^com.wacai.hermes.message.ConsumerThreadLocal^^com.wacai.hermes.message.ConsumerWrapper^^com.wacai.hermes.message.PullParam^^com.wacai.hermes.message.TopicPartition^^com.wacai.hermes.runtime.ISettings^^com.wacai.hermes.util.Assert^^lombok.Getter^^lombok.extern.slf4j.Slf4j^^java.util.concurrent.atomic.AtomicLong^^static com.wacai.hermes.core.admin.OffsetResetPolicy.resetOffset", "Scheduler": "Summary: Interface for scheduling tasks.\nClass: Scheduler\nFunction: isRunning, submit\nVariable:\nImport:", "TopicNotFoundException": "Summary: Represents a topic not found exception.\nClass: TopicNotFoundException\nVariable: serialVersionUID\nFunction: TopicNotFoundException\nImport:", "ActionTimeoutException": "Summary: \nClass: ActionTimeoutException\nVariable: serialVersionUID\nFunction: ActionTimeoutException\nImport:", "ThreadPool": "Summary: Manages thread pools for asynchronous tasks.\nClass: ThreadPool, Names, ExecutorHolder, Info\nFunction: submit, executor, executor, getName, getAllExecutor\nVariable: executors, alert\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.alert.Alert^^import com.wacai.hermes.async.AbstractRunnable^^import com.wacai.hermes.core.async.ExecutorBuilder.ScalingExecutorBuilder^^import com.wacai.hermes.core.async.ExecutorBuilder.ScheduledExecutorBuilder^^import com.wacai.hermes.runtime.ISettings^^import lombok.Data^^import lombok.extern.slf4j.Slf4j^^import java.util.ArrayList^^import java.util.HashMap^^import java.util.List^^import java.util.Map^^import java.util.concurrent.ExecutorService^^import java.util.concurrent.RejectedExecutionException^^import java.util.concurrent.ThreadPoolExecutor^^import java.util.concurrent.TimeUnit^^import static java.util.Collections.unmodifiableMap", "AuthInterceptor": "Summary: Mainly used to handle authentication in requests.\nClass: AuthInterceptor\nFunction: beforeHandRequest, afterHandRequest, registerHandlerInterceptor\nVariable: authHandler\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.errors.AuthException^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.proxy.auth.AuthHandler^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse^^import com.wacai.hermes.rest.interceptor.HandlerInterceptor^^import com.wacai.hermes.rest.interceptor.HandlerInterceptorRegistry^^import com.wacai.hermes.util.Assert^^import lombok.extern.slf4j.Slf4j^^import org.apache.commons.lang3.StringUtils", "RedisLockFetcher": "Summary: Mainly used to provide Redis lock fetching functionality.\nClass: RedisLockFetcher\nFunction: tryLock0, releaseLock0, rewriteFetchRequest, getPartition, getOffsetStorage, renew, reset\nVariable: redisCluster, redisLock, offsetStorage\nImport: import com.google.inject.Inject^^import com.google.inject.name.Named^^import com.wacai.common.redis.RedisCluster^^import com.wacai.common.redis.RedisException^^import com.wacai.hermes.admin.OffsetStorage^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.errors.RedisErrorException^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.fetch.FetchRequest^^import com.wacai.hermes.fetch.LockRequest^^import com.wacai.hermes.fetch.ResetVo^^import com.wacai.hermes.proxy.fetch.lock.RedisLock^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.Assert", "ProducerPool": "Summary:  \nClass: ProducerPool  \nFunction: hasNext, next, stop, order  \nVariable: next, list  \nImport: import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.runtime.HermesRuntime^^import lombok.extern.slf4j.Slf4j^^import java.io.IOException^^import java.util.Iterator^^import java.util.List^^import java.util.concurrent.atomic.AtomicInteger", "RestController": "Summary: Handles REST requests and routes them to appropriate handlers.\nClass: RestController\nFunction: register<PERSON><PERSON><PERSON>, dispatchRequest, findMeth<PERSON><PERSON><PERSON><PERSON>, tryHandler, handleBadRequest, handleMethodNotAllowed\nVariable: handlerInterceptorRegistry, pathTrie\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.core.rest.handler.BaseRestHandler^^import com.wacai.hermes.core.rest.handler.MethodHandler^^import com.wacai.hermes.core.rest.handler.MethodRestHandler^^import com.wacai.hermes.core.rest.interceptor.HandlerInterceptorChain^^import com.wacai.hermes.rest.*^^import com.wacai.hermes.rest.RestRequest.Method^^import com.wacai.hermes.rest.interceptor.HandlerInterceptorRegistry^^import com.wacai.hermes.rest.network.HttpServerTransport^^import lombok.extern.slf4j.Slf4j", "LifecycleListener": "Summary: Interface for lifecycle event callbacks.\nClass: LifecycleListener\nFunction: beforeStart, afterStart, beforeStop, afterStop\nVariable: \nImport:", "ReflectException": "Summary: Extends ApiException for reflection-related errors.\nClass: ReflectException\nVariable: serialVersionUID\nFunction: ReflectException\nImport:", "RedisOffsetStorage": "Summary: Redis offset storage implementation.\nClass: RedisOffsetStorage\nFunction: getOffset, storeOffset, resetOffset\nVariable: redisCluster\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.common.middleware.util.StringUtils^^import com.wacai.common.redis.RedisCluster^^import com.wacai.common.redis.RedisException^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.admin.OffsetStorage^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.errors.RedisErrorException^^import com.wacai.hermes.util.Assert^^import lombok.extern.slf4j.Slf4j", "LoaderException": "Summary: Extends RuntimeException for loader exceptions.  \nClass: LoaderException  \nFunction: LoaderException  \nVariable:   \nImport:", "HealthImpl": "Summary:  \nFunction: down, health, onInject, stop, order  \nClass: HealthImpl  \nVariable: health  \nImport: com.google.inject.Inject^^com.google.inject.Singleton^^com.wacai.hermes.bootstrap.Stoppable^^com.wacai.hermes.health.Health^^com.wacai.hermes.runtime.HermesRuntime^^lombok.extern.slf4j.Slf4j^^java.util.concurrent.atomic.AtomicBoolean", "LifecycleComponent": "Summary: Interface for lifecycle management.\nClass: LifecycleComponent\nFunction: start, stop, addLifecycleListener, removeLifecycleListener\nVariable: \nImport:", "ProduceServiceImpl": "Summary: Implements ProduceService for message handling.\nClass: ProduceServiceImpl\nFunction: getActionListener^^check^^send^^sendBatchByOneFlush^^sendBatch\nVariable: producerOnePool^^producerTwoPool^^producerThreePool^^producerOffsetCache^^metadataManager\nImport: com.google.inject.Inject^^com.google.inject.Singleton^^com.google.inject.name.Named^^com.wacai.common.middleware.util.StringUtils^^com.wacai.hermes.admin.MetadataManager^^com.wacai.hermes.async.ActionListener^^com.wacai.hermes.core.message.KafkaProducerWrapper^^com.wacai.hermes.proxy.produce.ProduceService^^com.wacai.hermes.errors.Errors^^com.wacai.hermes.errors.PartitionNotAssignException^^com.wacai.hermes.message.BatchMessageMeta^^com.wacai.hermes.message.HermesMessage^^com.wacai.hermes.message.MessageMeta^^com.wacai.hermes.message.ProducerPool^^com.wacai.hermes.util.Assert", "AuthExpiredException": "Summary: Represents an authentication expiration error.\nClass: AuthExpiredException\nVariable: serialVersionUID\nFunction: AuthExpiredException\nImport:", "HermesProxyLoader": "Summary: Proxy configuration loader implementation.\nClass: HermesProxyLoader\nFunction: load^^getClusterInfoFromLocal^^getClusterInfoFromCenter^^setupSettings\nVariable: clusterInfo^^clusterId^^mode^^centerUrl^^brokerAddress^^zkAddress^^brokerList^^zkServer^^producerOneSize^^producerTwoSize^^producerThreeSize\nImport: import com.wacai.hermes.command.GetClusterInfoCommand^^import com.wacai.hermes.command.resp.ClusterInfo^^import com.wacai.hermes.proxy.config.ProxyConfigMode^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.runtime.ISettings.Loader^^import com.wacai.hermes.runtime.LoaderException^^import lombok.extern.slf4j.Slf4j", "HandlerInterceptorRegistry": "Summary: Registry for handler interceptors in a REST application.\nFunction: registerHandlerInterceptor, lookup\nVariable: list\nClass: HandlerInterceptorRegistry\nImport: com.google.inject.Singleton^^com.wacai.hermes.rest.RestRequest^^java.util.ArrayList^^java.util.Comparator^^java.util.List^^java.util.regex.Pattern^^java.util.stream.Collectors", "BatchMessage": "Summary: Represents a batch message with topic, partition, and offsets.\nClass: BatchMessage\nVariable: topic, partition, startOffset, endOffset, payLoad, total, messageList, ackOffset, orignTotal\nImport: import lombok.Data^^import java.util.ArrayList^^import java.util.List", "GetClusterInfoCommand": "Summary: \nFunction: GetClusterInfoCommand, buildUrl, parseData\nVariable: LOG, centerUrl\nClass: GetClusterInfoCommand\nImport: import com.alibaba.fastjson.JSONObject^^import com.wacai.common.middleware.util.StringUtils^^import com.wacai.hermes.command.resp.ClusterInfo^^import org.slf4j.Logger^^import org.slf4j.LoggerFactory", "SendErrorException": "Summary: Message sending failure exception.\nClass: SendErrorException\nFunction: SendErrorException^^SendErrorException\nVariable: serialVersionUID\nImport:", "FetcherImpl": "Summary: Mainly used to implement fetch, ack, renew, and reset operations.\nClass: FetcherImpl\nFunction: isNoProducer, fetch, ack, renew, resetOffset\nVariable: redisLockFetcher, zkLockFetcher, metadataManager\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.admin.MetadataManager^^import com.wacai.hermes.errors.RedisErrorException^^import com.wacai.hermes.fetch.*^^import com.wacai.hermes.message.BatchMessage^^import lombok.extern.slf4j.Slf4j", "PullParam": "Summary: Mainly used to define pull parameters for message handling.\nClass: PullParam\nVariable: topic, partition, startNextOffset, pollMs, pullBeginTime, fetchMax\nImport: import lombok.Data", "HandlerInterceptor": "Summary: Interceptor for Handler  \nFunction: beforeHandRequest, afterHandRequest  \nClass: HandlerInterceptor  \nVariable:  \nImport: import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest", "Reporter": "Summary: Interface for sending alert messages.\nClass: Reporter\nFunction: send\nImport: import com.wacai.hermes.alert.AlarmMsg^^import java.util.Collection", "OffsetCache": "Summary: Synchronizes topic offsets for LAG calculation.\nClass: OffsetCache\nFunction: init, putOffset, flushCacheToRedis, close, stop, order\nVariable: offsetMap, executorService, redisCluster, settings\nImport: com.google.inject.Inject^^com.google.inject.Singleton^^com.wacai.common.middleware.util.StringUtils^^com.wacai.common.redis.RedisCluster^^com.wacai.hermes.async.ThreadUtil^^com.wacai.hermes.bootstrap.Stoppable^^com.wacai.hermes.errors.Errors^^com.wacai.hermes.runtime.HermesRuntime^^com.wacai.hermes.runtime.ISettings^^com.wacai.hermes.util.Assert^^lombok.extern.slf4j.Slf4j^^java.util.concurrent.ConcurrentHashMap^^java.util.concurrent.Executors^^java.util.concurrent.ScheduledExecutorService^^java.util.concurrent.TimeUnit", "Stoppable": "Summary: Represents a stoppable component.  \nFunction: prepareStop, stop  \nClass: Stoppable  \nVariable:  \nImport: import com.wacai.hermes.runtime.HermesRuntime", "ISettings": "Summary: Interface for runtime settings management.\nClass: ISettings\nVariable: SERVER_PORT, PROXY_CONFIG_MODE, PROXY_CLUSTER_ID, PROXY_CENTER_URL, PROXY_BROKER_ADDRESS, PROXY_ZOOKEEPER_ADDRESS, PROXY_MESSAGE_MAX_SIZE, ZK_SERVER, KAFKA_BROKER_LIST, KAFKA_PRODUCER_ONE_SIZE, KAFKA_PRODUCER_TWO_SIZE, KAFKA_PRODUCER_THREE_SIZE\nFunction: putAll, set, get, getString, containsKey, getLong, getInt, getBoolean, getEnum, remove, getAllKeys\nImport: import java.util.List^^import java.util.Map^^import java.util.Optional", "RedisLock": "Summary: Mainly used to provide Redis-based locking functionality.\nClass: RedisLock\nFunction: tryLock, tryLock, tryEnter, releaseLock\nVariable: DEL_LOCK_SCRIPT, redisCluster\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.common.redis.RedisCluster^^import com.wacai.common.redis.RedisException^^import com.wacai.hermes.errors.RedisErrorException^^import lombok.extern.slf4j.Slf4j", "RestRequest": "Summary: Interface for handling REST requests.\nClass: RestRequest, Method, ContentType\nFunction: header, headers, method, uri, rawPath, path, clientIp, getContentType, param, paramAsInt, paramAsLong, paramAsObject, hasContent, contentAsObject, contentAsList, contentAsString, mediaType, fromMediaType\nVariable: mediaType\nImport: import java.util.List", "HandlerInterceptorRegistrar": "Summary: HandlerInterceptor's Registrar  \nFunction: registerHandlerInterceptor  \nClass: HandlerInterceptorRegistrar  \nVariable:  \nImport:", "FetchModule": "Summary: \nClass: FetchModule\nImport: com.wacai.hermes.core.bootstrap.BaseModule^^com.wacai.hermes.fetch.Fetcher^^com.wacai.hermes.proxy.fetch.impl.*^^com.wacai.hermes.proxy.fetch.lock.RedisLock^^com.wacai.hermes.proxy.fetch.lock.ZkLock", "StartListener": "Summary: Interface for startup listeners with ordered execution.\nInterface: StartListener\nFunction: onStart\nVariable: \nImport: import com.wacai.hermes.runtime.HermesRuntime", "OffsetOutOfRangeException": "Summary: Represents an offset out of range error.\nClass: OffsetOutOfRangeException\nVariable: serialVersionUID\nFunction: OffsetOutOfRangeException^^OffsetOutOfRangeException\nImport:", "ThreadUtil": "Summary: Mainly used for thread utility functions.\nClass: ThreadUtil, HermesProxyThreadFactory\nFunction: numberOfProcessors, shutdownExecutor, threadName, daemonThreadFactory, newThread\nVariable: group, threadNumber, namePrefix\nImport: import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.ExecutorService^^import java.util.concurrent.ThreadFactory^^import java.util.concurrent.TimeUnit^^import java.util.concurrent.atomic.AtomicInteger", "FilterLoader": "Summary:  \nClass: FilterLoader  \nFunction: FilterLoader, load  \nVariable: loaderList  \nImport: import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.runtime.ISettings.Loader^^import java.util.List^^import java.util.stream.Collectors^^import java.util.stream.Stream", "HttpCommand": "Summary: Mainly used to provide HTTP command functionality.\nClass: HttpCommand\nFunction: execute, buildUrl, parseData, setHeader\nVariable: connTimeout, readTimeout, headers\nImport: import com.alibaba.fastjson.JSON^^import com.alibaba.fastjson.JSONObject^^import com.wacai.common.middleware.util.IOUtils^^import com.wacai.hermes.errors.ApiException^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.errors.Errors.HttpStatusException^^import com.wacai.hermes.errors.Errors.NetErrorException^^import com.wacai.hermes.util.StringUtils^^import lombok.Setter^^import lombok.extern.slf4j.Slf4j^^import java.io.InputStream^^import java.net.HttpURLConnection^^import java.net.URL^^import java.util.HashMap^^import java.util.Map", "ApiException": "Summary: Extends RuntimeException for API errors.\nClass: ApiException\nVariable: serialVersionUID\nFunction: ApiException^^fillInStackTrace\nImport:", "ZkLock": "Summary: Mainly used to provide Zookeeper lock functionality.\nClass: ZkLock\nFunction: tryLock, tryEnter, releaseLock, monitor2Path\nVariable: client, prefix\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.zk.ZkClient", "HandlerInterceptorInfo": "Summary: Mainly used to manage handler interceptor information.\nClass: HandlerInterceptorInfo\nFunction: HandlerInterceptorInfo, addPathPattern, order, order, getIncludePatterns, getHandlerInterceptor\nVariable: order, handlerInterceptor, includePatterns\nImport: import java.util.ArrayList^^import java.util.List^^import java.util.regex.Pattern", "OffsetIgnoreException": "Summary: \nClass: OffsetIgnoreException\nVariable: serialVersionUID\nImport:", "Task": "Summary: A generic task class for asynchronous operations.\nClass: Task\nFunction: timeout, isExecuted, incrementExecutedCount, getExecutedCount, complete, process, idle, idleLock, isComplete, toString\nVariable: type, startTime, nextExecuteTime, timeoutMs, latencyMs, executedCount, payload, response, exception, actionListener, abort, status, taskListener, lock\nImport: import com.wacai.common.middleware.util.FormatUtils^^import lombok.Getter^^import lombok.Setter^^import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.atomic.AtomicInteger^^import java.util.concurrent.locks.ReentrantLock", "ProduceModule": "Summary:  \nClass: ProduceModule  \nImport: import com.wacai.hermes.core.bootstrap.BaseModule^^import com.wacai.hermes.proxy.produce.ProduceService^^import com.wacai.hermes.proxy.produce.impl.OffsetCache^^import com.wacai.hermes.proxy.produce.impl.ProduceServiceImpl", "AckResult": "Summary: \nClass: AckResult\nImport: import lombok.Data", "ConsumerVo": "Summary: Represents consumer information.\nClass: ConsumerVo\nFunction: getFetcherMonitor, toString\nVariable: clusterId, consumerId, appName, groupId, topic, partitionNum, partition, offset\nImport: import lombok.Getter^^import lombok.Setter", "ClassPathLoader": "Summary: Mainly used to load settings from a config file.\nClass: ClassPathLoader\nFunction: ClassPathLoader, load\nVariable: configFileName\nImport: import com.wacai.hermes.runtime.LoaderException^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.runtime.ISettings.Loader^^import lombok.extern.slf4j.Slf4j^^import java.io.IOException^^import java.io.InputStream^^import java.net.URL^^import java.util.Properties", "ConsumerWrapper": "Summary: Interface for consumer wrapper operations.\nFunction: pull, getBeginOffset, getEndOffset, getBeginOffsetByTimestamp\nClass: ConsumerWrapper\nVariable: \nImport:", "ProduceService": "Summary: Interface for message sending services.  \nFunction: send, sendBatchByOneFlush, sendBatch  \nClass: ProduceService  \nVariable:  \nImport: import com.wacai.hermes.message.BatchMessageMeta^^import com.wacai.hermes.message.HermesMessage^^import com.wacai.hermes.message.MessageMeta^^import java.util.List", "ZkOffsetStorage": "Summary: Mainly used to manage offsets in Zookeeper.\nClass: ZkOffsetStorage\nFunction: getOffsetPath, getOffset, storeOffset, storeOffsetByPath, resetOffset\nVariable: client\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.common.middleware.util.StringUtils^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.admin.OffsetStorage^^import com.wacai.hermes.errors.OffsetIgnoreException^^import com.wacai.hermes.errors.ZkException^^import com.wacai.hermes.zk.ZkClient^^import lombok.extern.slf4j.Slf4j^^import org.apache.zookeeper.KeeperException", "ProducerWrapper": "Summary: Interface for message producer wrapper.\nClass: ProducerWrapper\nFunction: asyncSend^^asyncSend^^syncSend^^syncSend\nVariable: \nImport: import com.wacai.hermes.async.ActionListener^^import java.io.Closeable^^import java.util.List", "HttpServerTransport": "Summary: Interface for HTTP server transport and request dispatching.\nClass: HttpServerTransport, Dispatcher\nFunction: dispatchRequest\nVariable: \nImport: import com.wacai.hermes.component.LifecycleComponent^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest", "KafkaClientBuilder": "Summary: Mainly used to build Kafka clients.\nClass: KafkaClientBuilder\nFunction: createKafkaAdminClient, createKafkaConsumer, createKafkaProducer\nVariable: producerNumber, consumerNumber\nImport: import com.wacai.hermes.util.IPUtil^^import lombok.extern.slf4j.Slf4j^^import org.apache.kafka.clients.admin.AdminClientConfig^^import org.apache.kafka.clients.admin.KafkaAdminClient^^import org.apache.kafka.clients.consumer.ConsumerConfig^^import org.apache.kafka.clients.consumer.KafkaConsumer^^import org.apache.kafka.clients.producer.KafkaProducer^^import org.apache.kafka.clients.producer.ProducerConfig^^import org.apache.kafka.common.serialization.ByteArrayDeserializer^^import org.apache.kafka.common.serialization.ByteArraySerializer^^import java.util.Properties^^import java.util.concurrent.atomic.AtomicLong", "Result": "Summary:  \nClass: Result  \nVariable: code, data, error  \nImport: import lombok.Data", "PathTrie": "Summary: Path trie implementation for routing with wildcard support.\nClass: PathTrie, TrieNode\nFunction: insert, retrieve, updateKeyWithNamedWildcard, addInnerChild, insert, retrieve, put, toString, isNamedWildcard, namedWildcard, isNamedWildcard\nVariable: SEPARATOR, WILDCARD, root, rootValue, key, value, isWildcard, wildcard, namedWildcard, children\nImport: import java.util.EnumSet^^import java.util.HashMap^^import java.util.Map^^import static java.util.Collections.emptyMap^^import static java.util.Collections.unmodifiableMap", "StatusController": "Summary: Adapter for old version hermes-client.\nClass: StatusController\nFunction: status, online, offline\nVariable: \nImport: import com.google.inject.Inject^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.core.rest.handler.BaseRestHandler^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse", "HttpServerBootstrap": "Summary: Bootstrap for HTTP server.\nClass: HttpServerBootstrap\nFunction: HttpServerBootstrap, doStart, doStop\nVariable: httpServerTransport\nImport: import com.wacai.hermes.rest.network.HttpServerTransport^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.runtime.ISettings.Loader", "Encoder": "Summary:  \nFunction: encode  \nClass: Encoder  \nVariable:  \nImport: import com.wacai.hermes.alert.AlarmMsg", "HandlerInterceptorChain": "Summary: Mainly used to handle interceptor chains for REST requests.\nClass: HandlerInterceptorChain\nFunction: beforeHandRequest, afterHandRequest, handRequest\nVariable: method<PERSON>andler, handlerInterceptorList\nImport: import com.wacai.hermes.core.rest.handler.MethodHandler^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestHandler^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.interceptor.HandlerInterceptor^^import lombok.extern.slf4j.Slf4j^^import java.util.List", "Client": "Summary: Encapsulates all Hermes operations, must be asynchronous.\nClass: Client\nFunction: send^^sendBatch^^sendBatchByOneFlush^^fetch^^longFetch^^ack^^renew^^getConsumerOffset^^getTopicOffsetRange^^resetOffset^^getOffset\nVariable: \nImport: import com.wacai.hermes.admin.OffsetResp^^import com.wacai.hermes.fetch.*^^import com.wacai.hermes.admin.ConsumerOffset^^import com.wacai.hermes.admin.OffsetRange^^import com.wacai.hermes.async.ActionListener^^import com.wacai.hermes.message.BatchMessage^^import com.wacai.hermes.message.BatchMessageMeta^^import com.wacai.hermes.message.HermesMessage^^import com.wacai.hermes.message.MessageMeta^^import com.wacai.hermes.message.TopicPartition^^import java.util.List", "AccessLogInterceptor": "Summary: Mainly used to intercept and log access requests.  \nFunction: beforeHandRequest, afterHandRequest, registerHandlerInterceptor  \nClass: AccessLogInterceptor  \nVariable:  \nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.interceptor.HandlerInterceptor^^import com.wacai.hermes.rest.interceptor.HandlerInterceptorRegistry^^import lombok.extern.slf4j.Slf4j", "OffsetController": "Summary: Mainly used to handle offset-related operations in Hermes proxy.\nClass: OffsetController\nFunction: range, reset, consumer, query\nVariable: metadataManager, client, clusterId\nImport: import com.google.inject.Inject^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.admin.MetadataManager^^import com.wacai.hermes.core.rest.handler.BaseRestHandler^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.fetch.ResetVo^^import com.wacai.hermes.message.TopicPartition^^import com.wacai.hermes.proxy.client.Client^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.Assert", "ZkClient": "Summary: Interface for ZooKeeper client operations.\nFunction: getData, create, setData, casUpdate, casUpdate\nClass: ZkClient\nVariable: \nImport: import com.wacai.hermes.bootstrap.Stoppable^^import java.io.Closeable", "BootstrapAware": "Summary: Interface for Bootstrap awareness in module installation.\nClass: BootstrapAware\nFunction: setBootstrap\nVariable: \nImport: import com.google.inject.Module", "AdminModule": "Summary:  \nClass: AdminModule  \nImport: import com.google.inject.name.Names^^import com.wacai.hermes.admin.MetadataManager^^import com.wacai.hermes.admin.OffsetStorage^^import com.wacai.hermes.core.admin.MetadataManagerImpl^^import com.wacai.hermes.core.admin.RedisOffsetStorage^^import com.wacai.hermes.core.admin.ZkOffsetStorage^^import com.wacai.hermes.core.bootstrap.BaseModule", "KafkaConsumerWrapper": "Summary: Wrapper for Kafka consumer functionality.\nClass: KafkaConsumerWrapper\nFunction: setMaxPollRecordsField, pull, convertConsumerRecords, getBeginOffset, getEndOffset, getBeginOffsetByTimestamp\nVariable: kafkaConsumer, innerFetcher, maxPollRecordsField\nImport: import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.errors.OffsetOutOfRangeException^^import com.wacai.hermes.errors.ReflectException^^import com.wacai.hermes.message.BatchMessage^^import com.wacai.hermes.message.ConsumerWrapper^^import com.wacai.hermes.message.Message^^import com.wacai.hermes.message.PullParam^^import com.wacai.hermes.message.TopicPartition^^import com.wacai.hermes.util.Assert^^import com.wacai.hermes.util.ReflectUtil^^import lombok.extern.slf4j.Slf4j^^import org.apache.kafka.clients.consumer.Consumer^^import org.apache.kafka.clients.consumer.ConsumerRecord^^import org.apache.kafka.clients.consumer.ConsumerRecords^^import org.apache.kafka.clients.consumer.OffsetAndTimestamp^^import org.apache.kafka.clients.consumer.internals.Fetcher^^import org.apache.kafka.common.header.Header^^import java.lang.reflect.Field^^import java.time.Duration^^import java.util.Collections^^import java.util.Date^^import java.util.HashMap^^import java.util.Iterator^^import java.util.Map", "QueryTokenCommand": "Summary: \nClass: QueryTokenCommand\nFunction: buildUrl, parseData\nVariable: \nImport: import com.alibaba.fastjson.JSONObject^^import java.util.Map", "MetadataManagerImpl": "Summary: Mainly used to manage metadata for Hermes core.  \nFunction: getPartitionNum^^getTopicOffsetRange^^resetOffset^^isNoProducer^^getTopicInfo^^getConsumerOffset^^getOffset^^stop^^order  \nClass: MetadataManagerImpl^^PartitionSync^^NoProducerTopicSync^^TopicInfoSync  \nVariable: partitionSync^^noProducerTopicSync^^topicInfoSync^^redisOffsetStorage^^zkOffsetStorage  \nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.admin.ConsumerOffset^^import com.wacai.hermes.admin.MetadataManager^^import com.wacai.hermes.admin.OffsetRange^^import com.wacai.hermes.admin.OffsetResp^^import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.alert.Alert^^import com.wacai.hermes.async.ThreadUtil^^import com.wacai.hermes.command.QueryTopicInfoCommand^^import com.wacai.hermes.command.resp.TopicInfo^^import com.wacai.hermes.core.runtime.HermesRuntimeUtil^^import com.wacai.hermes.core.util.StopWatch^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.message.ConsumerThreadLocal^^import com.wacai.hermes.message.ConsumerWrapper^^import com.wacai.hermes.message.TopicPartition^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.StringUtils^^import com.wacai.jasmine.client.Jasmine^^import lombok.extern.slf4j.Slf4j^^import org.apache.kafka.clients.admin.DescribeTopicsResult^^import org.apache.kafka.clients.admin.KafkaAdminClient^^import org.apache.kafka.clients.admin.ListTopicsResult^^import java.util.*^^import java.util.concurrent.*^^import java.util.concurrent.atomic.AtomicBoolean^^import java.util.stream.Collectors^^import java.util.stream.Stream", "ZkLockFetcher": "Summary: Based on zk concurrent lock Fetcher implementation.\nClass: ZkLockFetcher\nFunction: tryLock0, releaseLock0, rewriteFetchRequest, getPartition, getOffsetStorage\nVariable: zkLock, offsetStorage\nImport: com.google.inject.Inject^^com.google.inject.name.Named^^com.wacai.hermes.admin.OffsetStorage^^com.wacai.hermes.fetch.ConsumerVo^^com.wacai.hermes.fetch.FetchRequest^^com.wacai.hermes.proxy.fetch.lock.ZkLock^^com.wacai.hermes.runtime.ISettings", "IllegalParameterException": "Summary: Mainly used to define an exception class for illegal parameters.\nClass: IllegalParameterException\nFunction: IllegalParameterException(String message, Throwable cause), IllegalParameterException(String message)\nVariable: serialVersionUID\nImport:", "UnknownServerException": "Summary:  \nClass: UnknownServerException  \nVariable: serialVersionUID  \nFunction: UnknownServerException^^UnknownServerException^^UnknownServerException^^UnknownServerException  \nImport:", "FilePathLoader": "Summary: Loads settings from a file path.  \nFunction: FilePathLoader, load  \nVariable: DEFAULT_CONFIG_DIR, configFileName  \nClass: FilePathLoader  \nImport: import com.wacai.hermes.runtime.LoaderException^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.runtime.ISettings.Loader^^import lombok.extern.slf4j.Slf4j^^import java.io.File^^import java.io.FileInputStream^^import java.io.IOException^^import java.io.InputStream^^import java.util.Properties", "Errors": "Summary: Defines error types and exceptions for Hermes API.\nClass: Errors, LogType, UnknownServerException, HttpStatusException, NetErrorException\nVariable: classToError, codeToError, logType, code, defaultExceptionString, builder, exception\nImport: import lombok.extern.slf4j.Slf4j^^import java.util.HashMap^^import java.util.Map", "ActionListener": "Summary: Interface for action listeners with response and exception handling.\nClass: ActionListener\nFunction: onResponse, onException, wrap, wrap\nVariable: DUMMY\nImport: import java.util.function.BiConsumer^^import java.util.function.Consumer", "HermesCenterApplication": "Summary: Mainly used to bootstrap Hermes center application.\nClass: HermesCenterApplication\nFunction: main\nVariable: bootstrap\nImport: import com.wacai.hermes.core.bootstrap.HttpServerBootstrap^^import com.wacai.hermes.core.module.CoreModule^^import com.wacai.hermes.core.rest.module.RestModule", "CoreModule": "Summary: Core module for Her<PERSON> framework.\nClass: CoreModule\nFunction: doConfigure^^configRedAlert^^setCommonProperties^^configRedis^^configZk\nVariable: \nImport: import com.wacai.common.redis.ClusterConfig^^import com.wacai.common.redis.RedisCluster^^import com.wacai.hermes.alert.Alert^^import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.core.alert.AsyncAlert^^import com.wacai.hermes.core.alert.CrowHttpReporter^^import com.wacai.hermes.core.alert.HermesEncoder^^import com.wacai.hermes.core.alert.Reporter^^import com.wacai.hermes.core.async.ThreadPool^^import com.wacai.hermes.core.bootstrap.BaseModule^^import com.wacai.hermes.core.health.HealthImpl^^import com.wacai.hermes.core.zk.ZkClientImpl^^import com.wacai.hermes.health.Health^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.zk.ZkClient^^import com.wacai.sailfish.Sailfish^^import lombok.extern.slf4j.Slf4j^^import java.io.IOException", "NettyHttpRequest": "Summary: Implements Netty HTTP request handling.\nClass: NettyHttpRequest HttpHeadersMap\nVariable: uri rawPath params headers contentType content httpMethod\nImport: import com.alibaba.fastjson.JSON^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.util.BeanCopyUtil^^import io.netty.buffer.ByteBuf^^import io.netty.handler.codec.http.FullHttpRequest^^import io.netty.handler.codec.http.HttpHeaders^^import io.netty.handler.codec.http.HttpMethod^^import lombok.extern.slf4j.Slf4j^^import java.nio.charset.StandardCharsets^^import java.util.AbstractMap^^import java.util.Collection^^import java.util.Collections^^import java.util.HashMap^^import java.util.List^^import java.util.Map^^import java.util.Set^^import java.util.stream.Collectors^^import static com.wacai.hermes.rest.RestRequest.ContentType.APPLICATION_FORM_URLENCODED", "HermesProxyApplication": "Summary: Mainly used to bootstrap and start the <PERSON><PERSON> proxy application.\nClass: HermesProxyApplication\nFunction: main\nVariable: bootstrap\nImport: import com.wacai.hermes.core.admin.module.AdminModule^^import com.wacai.hermes.core.bootstrap.HttpServerBootstrap^^import com.wacai.hermes.core.message.module.KafkaModule^^import com.wacai.hermes.core.module.CoreModule^^import com.wacai.hermes.core.rest.module.RestModule^^import com.wacai.hermes.proxy.bootstrap.Harmony^^import com.wacai.hermes.proxy.bootstrap.HermesProxyLoader^^import com.wacai.hermes.proxy.fetch.module.FetchModule^^import com.wacai.hermes.proxy.module.ProxyModule^^import com.wacai.hermes.proxy.produce.module.ProduceModule", "PartitionNotAssignException": "Summary: Exception class for partition assignment errors.\nClass: PartitionNotAssignException\nFunction: PartitionNotAssignException\nVariable: serialVersionUID\nImport:", "HealthHandler": "Summary: Health check and stop handling for backend services.\nClass: HealthHandler\nFunction: checkReadiness, checkLiveness, prepareStop\nVariable: health, hermesRuntime\nImport: import com.google.inject.Inject^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.health.Health^^import com.wacai.hermes.rest.*^^import com.wacai.hermes.runtime.HermesRuntime^^import lombok.extern.slf4j.Slf4j", "ProxyModule": "Summary:  \nClass: ProxyModule  \nImport: import com.wacai.hermes.core.bootstrap.BaseModule^^import com.wacai.hermes.proxy.client.Client^^import com.wacai.hermes.proxy.client.ClientImpl^^import com.wacai.hermes.proxy.controller.*^^import com.wacai.hermes.proxy.intercetor.AuthInterceptor", "Timers": "Summary: Mainly used to provide async timer functionality.\nClass: Timers, DelayQueueTimer, DelayQueueTimeoutFuture, DelayedTask\nFunction: newTimeout, cancel, run, compareTo, getDelay\nVariable: delayQueueTimer, delayQueue, thread, delayQueueTimer, delayedTask, delay, expireTime, callback\nImport: import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.DelayQueue^^import java.util.concurrent.Delayed^^import java.util.concurrent.TimeUnit", "AuthHandler": "Summary: <PERSON>les authentication and token management for <PERSON><PERSON> proxy.\nClass: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ey<PERSON>ache, TokenCache\nFunction: notNeedAuth, checkWhiteApp, auth, run, getSecretKeyByUserId, exist, put\nVariable: metadataManager, tokenCache, secretKeyCache, CACHE_SIZE, tokenMap, secretKeyMap, executorService, settings, centerUrl, queryTokenCommand\nImport: import com.google.inject.Inject^^import com.google.inject.Singleton^^import com.wacai.hermes.admin.MetadataManager^^import com.wacai.hermes.async.ThreadUtil^^import com.wacai.hermes.command.QueryTokenCommand^^import com.wacai.hermes.command.resp.TopicInfo^^import com.wacai.hermes.errors.AuthException^^import com.wacai.hermes.errors.AuthExpiredException^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.Assert^^import com.wacai.jasmine.client.Jasmine^^import io.jsonwebtoken.*^^import lombok.extern.slf4j.Slf4j^^import org.apache.commons.lang3.StringUtils^^import java.util.*^^import java.util.concurrent.Executors^^import java.util.concurrent.ScheduledExecutorService^^import java.util.concurrent.TimeUnit", "Message": "Summary: Mainly used to define a message structure with headers, key, and value.\nClass: Message\nFunction: getHeaderStringByKey\nVariable: topic, partition, offset, headers, key, value, timestamp\nImport: import com.wacai.hermes.util.StringUtils^^import lombok.Data^^import java.util.HashMap^^import java.util.Map", "IndexController": "Summary: Her<PERSON> message proxy controller.\nClass: IndexController\nFunction: index\nVariable: \nImport: import com.google.inject.Inject^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.core.rest.handler.BaseRestHandler^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse", "AuthException": "Summary: \nClass: AuthException\nVariable: serialVersionUID\nImport:", "CrowHttpReporter": "Summary: Mainly used for reporting alerts via HTTP.\nClass: CrowHttpReporter\nFunction: send, setDefaultValue, checkCrowURL, getHeader, convert\nVariable: crowURL, token, receivers, contentType, method, encoder\nImport: import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.util.HttpUtil^^import lombok.Builder^^import java.util.Collection^^import java.util.HashMap^^import java.util.Map^^import java.util.stream.Collectors^^import java.util.stream.Stream", "TopicInfo": "Summary: Mainly used to define topic information.\nClass: TopicInfo\nVariable: clusterId, topic, partitions, replicas, owner, needAuth, esSearch, description\nImport: import lombok.Data", "RequestMapping": "Summary: Defines a custom annotation for request mapping in REST handlers.\nClass: RequestMapping\nImport: import com.wacai.hermes.rest.RestRequest.Method^^import java.lang.annotation.ElementType^^import java.lang.annotation.Retention^^import java.lang.annotation.RetentionPolicy^^import java.lang.annotation.Target", "KafkaProducerWrapper": "Summary: Wrapper for Kafka producer functionality.\nClass: KafkaProducerWrapper, SendFuture\nFunction: asyncSend, doSend, buildFromHermesMessage, toMessageMeta, syncSend, close, get\nVariable: producer, message, future\nImport: import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.alert.Alert^^import com.wacai.hermes.async.ActionListener^^import com.wacai.hermes.core.runtime.HermesRuntimeUtil^^import com.wacai.hermes.core.util.StopWatch^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.errors.SendErrorException^^import com.wacai.hermes.message.BatchMessageMeta^^import com.wacai.hermes.message.HermesMessage^^import com.wacai.hermes.message.MessageMeta^^import com.wacai.hermes.message.ProducerWrapper^^import com.wacai.hermes.util.Assert^^import lombok.extern.slf4j.Slf4j^^import org.apache.kafka.clients.producer.KafkaProducer^^import org.apache.kafka.clients.producer.ProducerRecord^^import org.apache.kafka.clients.producer.RecordMetadata^^import org.apache.kafka.common.KafkaException^^import java.io.IOException^^import java.util.ArrayList^^import java.util.Iterator^^import java.util.List^^import java.util.Map^^import java.util.Map.Entry^^import java.util.concurrent.ExecutionException^^import java.util.concurrent.Future^^import java.util.concurrent.TimeUnit^^import java.util.concurrent.TimeoutException^^import java.util.stream.Collectors", "ConsumerOffset": "Summary: \nClass: ConsumerOffset\nVariable: consumerGroupOffset\nImport: import com.wacai.hermes.message.TopicPartition^^import lombok.Data^^import java.util.HashMap^^import java.util.Map", "ResetVo": "Summary: \nClass: ResetVo\nImport: import lombok.Getter^^import lombok.Setter", "StringUtils": "Summary: Utility class for string operations.\nClass: StringUtils\nFunction: isBlank\nVariable: EMPTY\nImport:", "OffsetResp": "Summary: Historical compatibility, only used by big data.\nClass: OffsetResp\nVariable: redisOffset, zkOffset\nImport: import lombok.Data", "NettyHttpRequestHandler": "Summary: Handles HTTP requests in a Netty server.\nClass: NettyHttpRequestHandler\nFunction: NettyHttpRequestHandler channelRead0 exceptionCaught\nVariable: serverTransport\nImport: import com.wacai.hermes.core.rest.NettyHttpChannel^^import com.wacai.hermes.core.rest.NettyHttpRequest^^import com.wacai.hermes.rest.RestResponse^^import io.netty.channel.ChannelHandler^^import io.netty.channel.ChannelHandlerContext^^import io.netty.channel.SimpleChannelInboundHandler^^import io.netty.handler.codec.http.DefaultFullHttpRequest^^import io.netty.handler.codec.http.FullHttpRequest^^import lombok.extern.slf4j.Slf4j", "MessageMeta": "Summary: Defines message metadata structure.\nClass: MessageMeta\nFunction: create\nVariable: offset, timestamp, topic, partition, proxyCostUs\nImport: import lombok.Data^^import lombok.NoArgsConstructor", "TaskListener": "Summary: Interface for task completion callback.\nClass: TaskListener\nFunction: onComplete\nVariable: \nImport:", "LongFetcherScheduler": "Summary: Mainly used to provide scheduling for long fetch tasks.\nClass: LongFetcherScheduler\nFunction: LongFetcherScheduler, determineDelayMs, prepareStop\nVariable: TASK_TYPE, TASK_TIMEOUT\nImport: com.google.inject.Inject^^com.wacai.hermes.async.Task^^com.wacai.hermes.core.async.ThreadPool.Names^^com.wacai.hermes.fetch.FetchRequest^^com.wacai.hermes.message.BatchMessage^^com.wacai.hermes.runtime.HermesRuntime", "Bootstrap": "Summary: Interface for <PERSON><PERSON> bootstrap operations.\nClass: Bootstrap\nFunction: registerStartListener, registerStoppable, install, start, stop, prepareStop, getSettings\nVariable: \nImport: import com.google.inject.Module^^import com.wacai.hermes.runtime.HermesRuntime^^import com.wacai.hermes.runtime.ISettings", "TaskHandler": "Summary: Handles asynchronous tasks with latency monitoring and scheduling.\nClass: TaskHandler\nVariable: latencyThreshold, task, scheduler\nFunction: run, handle\nImport: import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.async.Task^^import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.TimeUnit", "AlarmMsg": "Summary: Java class for alarm messages with equals and hashCode methods.\nClass: AlarmMsg\nVariable: message, exception\nFunction: equals, hashCode\nImport: import lombok.Builder^^import lombok.Data", "AbstractScheduler": "Summary: Abstract scheduler implementation for async task processing.\nClass: AbstractScheduler\nFunction: submit, doExecute, invokeNext, sendResponse, onSendResponse, onSendException, isRunning, close, stop, order, process, determineDelayMs, resetRequest\nVariable: running, workerPool, alert\nImport: import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.alert.Alert^^import com.wacai.hermes.async.Scheduler^^import com.wacai.hermes.async.Task^^import com.wacai.hermes.async.ThreadUtil^^import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.errors.FetchLockException^^import com.wacai.hermes.runtime.HermesRuntime^^import lombok.extern.slf4j.Slf4j^^import java.util.concurrent.RejectedExecutionException^^import java.util.concurrent.ScheduledThreadPoolExecutor^^import java.util.concurrent.TimeUnit^^import java.util.concurrent.atomic.AtomicBoolean^^import java.util.concurrent.locks.Lock", "ConsumerThreadLocal": "Summary: Mainly used to manage thread-local consumer wrappers.\nClass: ConsumerThreadLocal\nFunction: setSupplier, getConsumerWrapper\nVariable: consumerWrapperThreadLocal\nImport: import java.util.function.Supplier", "RedisErrorException": "Summary: Exception class for Redis errors.\nClass: RedisErrorException\nFunction: RedisErrorException^^RedisErrorException^^RedisErrorException^^RedisErrorException\nVariable: serialVersionUID\nImport:", "FaviconHandler": "Summary:  \nFunction: <PERSON>aviconHandler^^handRequest  \nClass: FaviconHandler  \nVariable:  \nImport: import com.google.inject.Inject^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestHandler^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse^^import com.wacai.hermes.util.StringUtils", "Health": "Summary: Interface for health check functionality.  \nFunction: health  \nClass: Health  \nVariable:  \nImport:", "RestResponse": "Summary: Mainly used to handle REST API responses.\nClass: RestResponse\nFunction: RestResponse, text, text, success, error, error\nVariable: status, content, contentType\nImport: import com.alibaba.fastjson.JSON^^import com.wacai.hermes.errors.ApiException^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.rest.RestRequest.ContentType^^import lombok.Data", "IPUtil": "Summary: IP related utility class.\nFunction: localIp, localIpCached, getPID\nVariable: ip\nClass: IPUtil\nImport: import java.lang.management.ManagementFactory^^import java.net.Inet4Address^^import java.net.InetAddress^^import java.net.NetworkInterface^^import java.net.SocketException^^import java.net.UnknownHostException^^import java.util.Collections^^import java.util.Enumeration", "FetcherController": "Summary: Mainly used to handle fetch, ack, and lock operations in <PERSON><PERSON> proxy.\nClass: FetcherController\nFunction: validateConsumer^^fetch^^useLongFetch^^ack^^renew\nVariable: clusterId^^client\nImport: import com.google.inject.Inject^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.core.rest.handler.BaseRestHandler^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.fetch.AckRequest^^import com.wacai.hermes.fetch.FetchRequest^^import com.wacai.hermes.fetch.LockRequest^^import com.wacai.hermes.proxy.client.Client^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.Assert^^import org.springframework.util.StringUtils", "HermesEncoder": "Summary: \nClass: HermesEncoder\nFunction: encode, getAppName\nVariable: \nImport: import com.wacai.common.middleware.util.AppUtils^^import com.wacai.common.middleware.util.FormatUtils^^import com.wacai.hermes.alert.AlarmMsg^^import com.wacai.hermes.core.runtime.HermesRuntimeUtil^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.EnvUtil^^import com.wacai.hermes.util.IPUtil", "FetchTaskManager": "Summary: Manages fetch tasks and their scheduling.\nClass: FetchTaskManager\nFunction: register, submitTask, addTask, removeTask, onComplete, getTaskList\nVariable: taskList, schedulerMap, alert\nImport: com.google.inject.Inject^^com.google.inject.Singleton^^com.wacai.hermes.alert.AlarmMsg^^com.wacai.hermes.alert.Alert^^com.wacai.hermes.async.Scheduler^^com.wacai.hermes.async.Task^^com.wacai.hermes.async.TaskListener^^com.wacai.hermes.fetch.FetchRequest^^com.wacai.hermes.message.BatchMessage^^lombok.extern.slf4j.Slf4j^^java.util.ArrayList^^java.util.List^^java.util.Map^^java.util.concurrent.ConcurrentHashMap", "HermesRuntime": "Summary: Represents a runtime with different module combinations.  \nClass: HermesRuntime  \nFunction: getInstance, getBootstrap  \nVariable:  \nImport: import com.wacai.hermes.bootstrap.Bootstrap", "StopWatch": "Summary: Utility for measuring elapsed time.\nClass: StopWatch\nImport: import java.util.function.LongConsumer", "OffsetRange": "Summary: TODO  \nClass: OffsetRange  \nVariable: topic, partition, beiginOffset, endOffset  \nImport: import lombok.Data  \nFunction:  \n^^", "ReflectUtil": "Summary: Mainly used for reflection utility operations.\nFunction: getFieldByFieldName, getFieldValueByFieldName\nClass: ReflectUtil\nVariable: \nImport: import com.wacai.hermes.errors.ReflectException^^import java.lang.reflect.Field", "BeanCopyUtil": "Summary: Utility for copying data between beans and maps.\nFunction: copyFromMap, convert\nClass: BeanCopyUtil, BeanInfoCache, BeanVisitor, BeanInfo, FiledInfo\nVariable: innerMap, beanClass, fieldList, field\nImport: import com.wacai.hermes.errors.IllegalParameterException^^import java.lang.reflect.Field^^import java.util.ArrayList^^import java.util.Date^^import java.util.List^^import java.util.Map^^import java.util.concurrent.ConcurrentHashMap", "CompletableContext": "Summary:  \nClass: CompletableContext  \nFunction: addListener, complete, completeExceptionally, isDone, isCompletedExceptionally  \nVariable: completableFuture  \nImport: import java.util.concurrent.CompletableFuture^^import java.util.function.BiConsumer", "ClusterInfo": "Summary: \nClass: ClusterInfo\nVariable: clusterId, description, brokerList, zkServer, producerOneSize, producerTwoSize, producerThreeSize, consumerPoolSize, mq\nImport: import lombok.Data", "MetadataManager": "Summary: Interface for managing metadata in Hermes admin.\nClass: MetadataManager\nFunction: getPartitionNum, getConsumerOffset, getOffset, getTopicOffsetRange, resetOffset, isNoProducer, getTopicInfo\nVariable: \nImport: import com.wacai.hermes.bootstrap.Stoppable^^import com.wacai.hermes.command.resp.TopicInfo^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.message.TopicPartition", "MethodRestHandler": "Summary: Based on Method's RestHandler  \nClass: MethodRestHandler  \nVariable: restHandler, invokeMethod  \nImport: import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestHandler^^import com.wacai.hermes.rest.RestRequest^^import java.lang.reflect.InvocationTargetException^^import java.lang.reflect.Method", "CatController": "Summary: Mainly used to handle REST API endpoints for various cat commands.\nClass: CatController\nFunction: cat, settings, threadPool, consumers, formatConsumers, tasks\nVariable: settings, threadPool, client, fetchTaskManager, clusterId\nImport: import com.google.inject.Inject^^import com.wacai.common.middleware.util.TableBuilder^^import com.wacai.hermes.admin.ConsumerOffset^^import com.wacai.hermes.async.ActionListener^^import com.wacai.hermes.async.Task^^import com.wacai.hermes.core.async.ThreadPool^^import com.wacai.hermes.core.async.ThreadPool.ExecutorHolder^^import com.wacai.hermes.core.async.ThreadPool.Info^^import com.wacai.hermes.core.rest.RestController^^import com.wacai.hermes.core.rest.handler.BaseRestHandler^^import com.wacai.hermes.errors.Errors^^import com.wacai.hermes.fetch.ConsumerVo^^import com.wacai.hermes.fetch.FetchRequest^^import com.wacai.hermes.message.BatchMessage^^import com.wacai.hermes.message.TopicPartition^^import com.wacai.hermes.proxy.client.Client^^import com.wacai.hermes.proxy.fetch.impl.FetchTaskManager^^import com.wacai.hermes.rest.RequestMapping^^import com.wacai.hermes.rest.RestChannel^^import com.wacai.hermes.rest.RestRequest^^import com.wacai.hermes.rest.RestResponse^^import com.wacai.hermes.runtime.ISettings^^import com.wacai.hermes.util.Assert^^import java.util.Comparator^^import java.util.List^^import java.util.Map^^import java.util.concurrent.ExecutorService^^import java.util.concurrent.ThreadPoolExecutor^^import java.util.stream.Collectors", "DefaultHermesRuntime": "Summary: Implements HermesRuntime interface.\nClass: DefaultHermesRuntime\nFunction: onInject, getInstance, getBootstrap\nVariable: bootstrap, injector\nImport: com.google.inject.Inject^^com.google.inject.Injector^^com.wacai.hermes.bootstrap.Bootstrap^^com.wacai.hermes.runtime.HermesRuntime", "Alert": "Summary: Interface for sending alerts.\nFunction: send\nClass: Alert\nVariable: \nImport:"}