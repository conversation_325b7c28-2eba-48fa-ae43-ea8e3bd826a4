package com.wacai.middleware.wapilot.ui.examples;

import com.intellij.icons.AllIcons;
import com.intellij.ide.BrowserUtil;
import com.intellij.ide.highlighter.JavaFileType;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.actionSystem.impl.ActionButton;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.temporary.gui.block.*;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.intellij.ui.components.panels.VerticalLayout;
import com.intellij.util.PlatformIcons;
import com.intellij.util.ui.HTMLEditorKitBuilder;
import com.intellij.util.ui.JBEmptyBorder;
import com.intellij.util.ui.JBFont;
import com.intellij.util.ui.JBUI;
import com.wacai.middleware.wapilot.ProjectFileUtilKt;
import com.wacai.middleware.wapilot.base.resource.WapilotIcons;
import com.wacai.middleware.wapilot.ui.component.RoundedLabel;
import com.wacai.middleware.wapilot.ui.examples.diff.DiffChain;
import com.wacai.middleware.wapilot.ui.examples.diff.DiffProvider;
import com.wacai.middleware.wapilot.ui.component.RoundRectPanel;
import com.wacai.middleware.wapilot.base.util.ColorUtil;
import com.wacai.middleware.wapilot.base.util.SwingUtil;
import com.wacai.middleware.wapilot.ui.examples.workspace.FileListPanel;
import lombok.Setter;

import javax.swing.*;
import javax.swing.event.HyperlinkEvent;
import javax.swing.event.HyperlinkListener;
import javax.swing.text.DefaultCaret;
import javax.swing.text.html.HTMLEditorKit;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import static com.wacai.middleware.wapilot.MarkdownToHtmlConverterKt.convertMarkdownToHtml;

import com.wacai.middleware.wapilot.ui.examples.workspace.CollapsiblePanelDemo;

/**
 * 一个MessageView组成：
 * - TextBlock
 * - CodeBlock
 * - TextBlock
 * - FileBlock
 * - TextBlock
 */
public class DemoMessageView extends JBPanel<DemoMessageView> {
    private static final JBColor BORDER_COLOR = new JBColor(0xDFE1E5, 0x474747);
    // 消息背景色：浅色主题用浅灰，深色主题用深灰
    private static final JBColor MESSAGE_BACKGROUND = new JBColor(0xE8E9EE, 0x3C3F41);

    private JPanel centerPanel;
    private Component myNameLabel;
    @Setter
    private Project project;


    public DemoMessageView(Project project) {
        this.project = project;

        setDoubleBuffered(true);
        setOpaque(true);
        setBackground(new JBColor(0xEAEEF7, 0x2d2f30));// 浅灰

        JLabel authorLabel = new JLabel();
        authorLabel.setFont(JBFont.h4());
        authorLabel.setText("Wapilot");

        this.myNameLabel = authorLabel;

        setBorder(new JBEmptyBorder(8));
        setLayout(new BorderLayout(JBUI.scale(8), 0));

        centerPanel = new JPanel(new VerticalLayout(JBUI.scale(8)));
        centerPanel.setOpaque(false);
        centerPanel.setBorder(JBUI.Borders.emptyRight(8));
        centerPanel.add(myNameLabel);
        add(centerPanel, BorderLayout.CENTER);


        //diff 内容
        DiffProvider diffProvider = new DiffProvider();
        for (DiffProvider.DiffContent diffContent : diffProvider.getDiffContents()) {
            TextView view = new TextView(diffContent.getText());//TextBlock
            DiffChain diffChain = new DiffChain(diffContent);
            centerPanel.add(view.getComponent()); //markdown

            centerPanel.add(createHeadPanel());

            //centerPanel.add(getTextBlock());
            //centerPanel.add(getFileListBlock()); //文件列表
            centerPanel.add(getCodeBlock());//代码

            centerPanel.add(getFileBlock(diffContent.getFileName(), diffChain)); //FileBlock
        }
    }

    private JPanel createHeadPanel() {
        JPanel headPanel = new JPanel(new BorderLayout());
        headPanel.setBackground(ColorUtil.getEditorBackgroundColor());
        headPanel.setBorder(BorderFactory.createMatteBorder(1, 1, 1, 1, BORDER_COLOR));
        headPanel.setPreferredSize(new Dimension(-1, 34));

        // 使用 Box 布局来更好地控制垂直居中
        JPanel leftPanel = new JPanel();
        leftPanel.setOpaque(false);
        leftPanel.setLayout(new BoxLayout(leftPanel, BoxLayout.X_AXIS));
        //leftPanel.setBorder(BorderFactory.createEmptyBorder(0, 8, 0, 0)); // 左边距
        leftPanel.setBorder(BorderFactory.createEmptyBorder(3, 8, 3, 0));

        Presentation colsText = new Presentation("");
        colsText.setIcon(WapilotIcons.Cols);
        ActionButton colsButton = new ActionButton(
                new DumbAwareAction() {
                    @Override
                    public void actionPerformed(AnActionEvent e) {

                    }
                },
                colsText,
                "",
                new Dimension(28, 28)
        );
        leftPanel.add(colsButton);

        RoundedLabel fileNameLabel = new RoundedLabel("HermesMessage.java");
        fileNameLabel.setBackground(ColorUtil.getEditorBackgroundColor());
        fileNameLabel.setAlignmentY(Component.CENTER_ALIGNMENT); // 垂直居中
        fileNameLabel.setBorder(BorderFactory.createEmptyBorder(8, 6, 8, 6)); // 上下左右边距

        fileNameLabel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                System.out.println("mouseEntered");
                fileNameLabel.setBackground(ColorUtil.getButtonHoverBackgroundColor());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                System.out.println("mouseExited");
                fileNameLabel.setBackground(ColorUtil.getEditorBackgroundColor());
            }

            @Override
            public void mouseClicked(MouseEvent e) {
                System.out.println("click....");
            }
        });

        leftPanel.add(fileNameLabel);

        // 添加水平间距（例如 10px）
        leftPanel.add(Box.createHorizontalStrut(10));

        String path = "src/main/java/com/wacai/hermes/message/HermesMessage.java";
        JLabel pathLabel = new JLabel(path);
        pathLabel.setAlignmentY(Component.CENTER_ALIGNMENT); // 垂直居中
        leftPanel.add(pathLabel);

        // 右侧面板保持不变
        JPanel rightPanel = new JPanel();
        rightPanel.setOpaque(false);
        rightPanel.setLayout(new BoxLayout(rightPanel, BoxLayout.X_AXIS));
        rightPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 8)); // 右边距

        JLabel opLabel = new JLabel("Apply",AllIcons.Actions.Annotate,JLabel.LEFT);
        opLabel.setOpaque(true);//不透明
        opLabel.setAlignmentY(Component.CENTER_ALIGNMENT); // 垂直居中
        opLabel.setBorder(BorderFactory.createEmptyBorder(4, 4, 4, 8)); // 上下左右边距
        opLabel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                opLabel.setBackground(ColorUtil.getButtonHoverBackgroundColor());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                opLabel.setBackground(ColorUtil.getEditorBackgroundColor());
            }

            @Override
            public void mouseClicked(MouseEvent e) {
                System.out.println("opLabel click....");
            }
        });

        rightPanel.add(opLabel);
        rightPanel.add(Box.createHorizontalStrut(8)); // 添加间距
        JLabel moreLabel = new JLabel("...");
        moreLabel.setAlignmentY(Component.CENTER_ALIGNMENT); // 垂直居中
        rightPanel.add(moreLabel);

        headPanel.add(leftPanel, BorderLayout.WEST);
        headPanel.add(rightPanel, BorderLayout.EAST);

        return headPanel;
    }


    private JPanel getFileListBlock() {
        FileListPanel fileListPanel = new FileListPanel();
        // 添加示例文件
        fileListPanel.addFile("Main.java", "src/main/java/com/example/Main.java");
        fileListPanel.addFile("Config.xml", "src/main/resources/config/Config.xml");
        fileListPanel.addFile("README.md", "README.md");
        return new CollapsiblePanelDemo(fileListPanel) {
            @Override
            protected JPanel setupTitlePanel(JPanel titlePanel) {
                JBLabel nameLabel = new JBLabel("3 Files Changed");
                titlePanel.add(nameLabel);
                return titlePanel;
            }
        }.getBaseComponent();
    }

    private Component getTextBlock() {
        String input = """
                test
                """;

        CollapsibleEditorPane editorPane = new CollapsibleEditorPane();
        editorPane.setText(input);
        return editorPane;
    }

    private Component getCodeBlock() {
        CodeEditorPanel editorPanel = new CodeEditorPanel(project, JavaFileType.INSTANCE);

        String input = """
                public class CollapsibleCodeEditor extends BorderLayoutPanel implements Disposable {
                    private final Project project;
                    private final EditorEx editor;
                    private boolean collapsed = false;
                    private String fullContent = "";
                    private final FileType fileType;
                    private JPanel headPanel;
                   \s
                    // 定义边框颜色常量
                    private static final JBColor BORDER_COLOR = new JBColor(0xDFE1E5, 0x474747);

                    public CollapsibleCodeEditor(Project project, FileType fileType) {
                        this.project = project;
                        this.fileType = fileType != null ? fileType : FileTypes.PLAIN_TEXT;

                        // 设置细灰色边框
                        setBorder(BorderFactory.createLineBorder(BORDER_COLOR, 1));
                       \s
                        // ... 其余构造函数代码 ...
                    }

                    private JPanel createHeaderPanel() {
                        headPanel = new JPanel(new BorderLayout());
                        JPanel left = new JPanel();
                       \s
                        // 设置背景色
                        Color editorBackground = editor.getBackgroundColor();
                        headPanel.setBackground(editorBackground);
                        left.setBackground(editorBackground);
                       \s
                        // 设置与主面板相同的边框
                        headPanel.setBorder(BorderFactory.createLineBorder(BORDER_COLOR, 1));
                       \s
                        left.add(createButton());
                        JBLabel nameLabel = new JBLabel("CollapsibleEditorPane.java");
                        left.add(nameLabel);

                        headPanel.add(left, BorderLayout.WEST);
                        return headPanel;
                    }
                   \s
                    // ... 其余代码 ...
                }
                """;
        editorPanel.setText(input);
        return new CollapsiblePanelDemo( editorPanel) {
            @Override
            protected JPanel setupTitlePanel(JPanel titlePanel) {

                JBLabel fileNameLabel = new JBLabel("HermesMessage.java");
                titlePanel.add(fileNameLabel);

                JBLabel pathLabel = new JBLabel("src/main/java/com/wacai/hermes/message/HermesMessage.java");
                titlePanel.add(pathLabel);

                return titlePanel;
            }
        }.getBaseComponent();
    }

    private Component getFileBlock(String fileName, DiffChain diffChain) {

        RoundRectPanel content = new RoundRectPanel(new BorderLayout());
        content.setOpaque(true);
        content.setBorder(JBUI.Borders.empty(2, 2, 2, 2));
        JBColor borderColor = new JBColor(0xDFE1E5, 0x474747);
        content.setBorderColor(borderColor);
        content.setBackground(ColorUtil.getEditorBackgroundColor());

        MouseAdapter mouseAdapter = new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {

                content.setBackground(ColorUtil.getSelectBackground());
            }

            @Override
            public void mouseExited(MouseEvent e) {

                content.setBackground(ColorUtil.getEditorBackgroundColor());
            }
        };


        JPanel filePanel = new JPanel();
        filePanel.setOpaque(false);
        filePanel.setBorder(JBUI.Borders.empty(4));

//        JLabel iconLabel = new JLabel(PlatformIcons.CLASS_ICON);
//        filePanel.add(iconLabel);

        JBLabel nameLabel = new JBLabel(fileName);
        nameLabel.setIcon(PlatformIcons.CLASS_ICON);
        nameLabel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                VirtualFile virtualFile = ProjectFileUtilKt.findFile(project, fileName);
                if (virtualFile != null) {
                    FileEditorManager.getInstance(project).openFile(virtualFile);
                }
            }
        });


        filePanel.add(nameLabel);
        JPanel operation = new JPanel();
        operation.setOpaque(false);
        operation.add(createDiff(diffChain));
        operation.add(createAccept());
        operation.add(createReject());

        content.add(filePanel, BorderLayout.WEST);
        content.add(operation, BorderLayout.EAST);

        SwingUtil.foreachChildComponent(content, mouseAdapter);
        return content;
    }

    private ActionButton createDiff(DiffChain diffChain) {
        Presentation acceptText = new Presentation("查看");
        acceptText.setIcon(WapilotIcons.Eye);
        ActionButton button = new ActionButton(
                new DumbAwareAction() {
                    @Override
                    public void actionPerformed(AnActionEvent e) {
                        diffChain.showDiff(project);
                    }
                },
                acceptText,
                "",
                new Dimension(24, 24)
        );
        return button;
    }

    private ActionButton createAccept() {
        Presentation acceptText = new Presentation("接受");
        acceptText.setIcon(WapilotIcons.CheckGreen);
        ActionButton button = new ActionButton(
                new DumbAwareAction() {
                    @Override
                    public void actionPerformed(AnActionEvent e) {
                        System.out.println("accept");
                    }
                },
                acceptText,
                "",
                new Dimension(24, 24)
        );
        return button;
    }

    private ActionButton createReject() {
        Presentation acceptText = new Presentation("拒绝");
        acceptText.setIcon(WapilotIcons.CrossRed);
        ActionButton button = new ActionButton(
                new DumbAwareAction() {
                    @Override
                    public void actionPerformed(AnActionEvent e) {
                        System.out.println("reject");
                    }
                },
                acceptText,
                "",
                new Dimension(24, 24)
        );
        return button;
    }

    static class TextView {

        private JEditorPane editorPane;

        public Component getComponent() {
            return editorPane;
        }

        public TextView(String text) {
            editorPane = createComponent();
            text = convertMarkdownToHtml(text);
            //System.out.println("=======to HTML begin=========");
            //System.out.println(text);
            //System.out.println("=======to HTML end =========");
            editorPane.setText(text);
        }

        public JEditorPane createBaseComponent() {
            JEditorPane jEditorPane = new JEditorPane();
            jEditorPane.setContentType("text/html");

            HTMLEditorKit htmlEditorKit = new HTMLEditorKitBuilder()
                    .withViewFactoryExtensions(new LineSpacingExtension(0.2f))
                    .build();
            htmlEditorKit.getStyleSheet().addRule("p {margin-top: 1px}");

            jEditorPane.setEditorKit(htmlEditorKit);
            jEditorPane.setEditable(false);
            jEditorPane.putClientProperty("JEditorPane.honorDisplayProperties", true);
            jEditorPane.setOpaque(false);
            jEditorPane.setBorder(null);
            jEditorPane.putClientProperty("AccessibleName", StringUtil.unescapeXmlEntities(StringUtil.stripHtml("", " ")));
            jEditorPane.setText("");

            if (jEditorPane.getCaret() != null) {
                jEditorPane.setCaretPosition(0);
                if (jEditorPane.getCaret() instanceof DefaultCaret) {
                    ((DefaultCaret) jEditorPane.getCaret()).setUpdatePolicy(DefaultCaret.ALWAYS_UPDATE);
                }
            }

            return jEditorPane;
        }

        private JEditorPane createComponent() {
            JEditorPane jEditorPane = createBaseComponent();
            jEditorPane.addHyperlinkListener(new HyperlinkListener() {
                @Override
                public void hyperlinkUpdate(HyperlinkEvent e) {
                    if (e.getEventType() == HyperlinkEvent.EventType.ACTIVATED) {
                        BrowserUtil.browse(e.getURL());
                    }
                }
            });

            jEditorPane.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
                    if (e != null) {
                        if (jEditorPane.getParent() != null) {
                            jEditorPane.getParent().dispatchEvent(e);
                        }
                    }
                }
            });
            return jEditorPane;
        }
    }
}
