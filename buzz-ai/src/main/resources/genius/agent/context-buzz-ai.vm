```java buzz-ai/src/main/java/com/wacai/buzz/code/CodeChat0.java
package com.buzz.ai.agent;

import com.buzz.ai.llm.ChatMessage;
import com.buzz.ai.llm.DefaultLLMProvider;
import com.buzz.ai.llm.LLMProvider;
import com.buzz.ai.prompt.TemplateRender;
import com.buzz.ai.util.AutoRequestSubscriber;
import com.buzz.ai.view.Console;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Flow;


/**
 * 模型是顺序地阅读消息数组的，因此靠近用户问题的内容权重更高
 * [
 * { "role": "system", "content": "你是一个 AI 编程助手..." },
 * { "role": "user", "content": "请优化 ProducerController，为发送消息增加 header 属性" },
 * { "role": "user", "content": "为了更好地理解，请参考以下信息：\n<project_structure>...</project_structure>" }
 * ]
 */
public class CodeChat0 {
    private ExecutorService threadPool  = Executors.newFixedThreadPool(4);
    private LLMProvider llmProvider = new DefaultLLMProvider();
    List<ChatMessage> historyChatList = new ArrayList<>();
    private Console console = new Console();
    private StringBuffer out = new StringBuffer();

    public void chat(String question) {
        System.out.println("User=>" + question);
        threadPool.submit(() -> {
            historyChatList.add(new ChatMessage("system", getSystemPrompt()));
            historyChatList.add(new ChatMessage("user", question));
            historyChatList.add(new ChatMessage("user", "为了更好地理解，请参考以下信息：\n" + getCodePrompt()));
            Flow.Publisher<String> response = llmProvider.stream(historyChatList, false);
            processResponse(response);
        });

    }

    protected void processResponse(Flow.Publisher<String> response) {
        console.beginResponse();
        response.subscribe(new AutoRequestSubscriber<String>() {
            @Override
            protected void handleItem(String item) {
                console.updateMessage(item);
                out.append(item);
            }

            @Override
            public void onComplete() {
                console.completeResponse();
                saveResult();
            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
            }
        });
    }

    protected String getSystemPrompt() {
        TemplateRender templateRender = new TemplateRender("agent");
        String agentTemplate = templateRender.getTemplate("lite-search-replace.vm");
        return templateRender.renderTemplate(agentTemplate);
    }

    protected String getCodePrompt() {
        TemplateRender templateRender = new TemplateRender("agent");
        String agentTemplate = templateRender.getTemplate("context-hermes.vm");
        return templateRender.renderTemplate(agentTemplate);
    }


    private void saveResult(){
          try {
              Path projectRoot = Paths.get("").toAbsolutePath();
              Path testDir = projectRoot.resolve("test");
              if (!Files.exists(testDir)) {
                  Files.createDirectories(testDir);
              }

              // Find the next available file number
              int fileNumber = 1;
              Path filePath;
              do {
                  filePath = testDir.resolve("test-code-" + fileNumber + ".md");
                  fileNumber++;
              } while (Files.exists(filePath));

              // Get current time
              String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

              // Create content with timestamp
              String content = timestamp + "\n\n" + out.toString();

              // Write to file
              Files.writeString(filePath, content, StandardCharsets.UTF_8);
              System.out.println("Result saved to: " + filePath);

              // Clear the buffer after saving
              out.setLength(0);
          } catch (IOException e) {
              System.err.println("Error saving result: " + e.getMessage());
          }
    }

    public static void main(String[] args) {
        new CodeChat0().chat("请优化 ProducerController，为发送消息增加 header 属性");
    }
}
```