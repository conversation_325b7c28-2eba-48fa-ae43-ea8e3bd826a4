```java hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
package com.wacai.hermes.proxy.controller;

import com.google.inject.Inject;
import com.wacai.hermes.async.ActionListener;
import com.wacai.hermes.core.rest.RestController;
import com.wacai.hermes.core.rest.handler.BaseRestHandler;
import com.wacai.hermes.core.util.StopWatch;
import com.wacai.hermes.errors.Errors;
import com.wacai.hermes.message.BatchMessageMeta;
import com.wacai.hermes.message.HermesMessage;
import com.wacai.hermes.message.MessageMeta;
import com.wacai.hermes.proxy.client.Client;
import com.wacai.hermes.rest.RequestMapping;
import com.wacai.hermes.rest.RestChannel;
import com.wacai.hermes.rest.RestRequest;
import com.wacai.hermes.rest.RestResponse;
import com.wacai.hermes.runtime.ISettings;
import com.wacai.hermes.util.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wacai.hermes.rest.RestRequest.Method.POST;


/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class ProducerController extends BaseRestHandler {

 private final long messageMaxSize;

 @Inject
 private Client client;

 @Inject
 public ProducerController(RestController controller, ISettings settings) {
 super(controller);
 //消息限制默认3MB
 messageMaxSize = settings.getLong(ISettings.PROXY_MESSAGE_MAX_SIZE, 3145728l);
 }

 /**
 * @param restRequest
 * @param restChannel
 */
 @RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
 public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
 String topic = restRequest.param("topic");
 String ack = restRequest.param("ack", "1");
 long timeout = restRequest.paramAsLong("timeout", 5000l);
 HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
 validateSingleMessage(hermesMessage, topic);
 StopWatch watch = StopWatch.create().start();
 ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
 log.info("sendSingle, topic {} client {} offset {} cost(ms) {}",
 topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed());
 });
 client.send(ack, hermesMessage, timeout, actionListener);
 }

 private void validateSingleMessage(HermesMessage hermesMessage, String topic) {
 Assert.notNull(hermesMessage, Errors.ILLEGAL_PARAMETER, "发送的消息体不能为空");
 long payLoad = hermesMessage.getPayLoad();
 Assert.isTrue(payLoad > 0, Errors.ILLEGAL_PARAMETER, "消息的key和value不能同时为空");
 Assert.isTrue(payLoad <= messageMaxSize, Errors.ILLEGAL_PARAMETER, "消息长度不能大于" + messageMaxSize + "(key+value),topic:" + hermesMessage.getTopic());
 Assert.isTrue(StringUtils.equals(topic, hermesMessage.getTopic()), Errors.ILLEGAL_PARAMETER, "body中的消息的topic和请求参数中topic不一致");
 }

 /**
 * @param restRequest
 * @param restChannel
 */
 @RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
 public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
 String topic = restRequest.param("topic");
 String ack = restRequest.param("ack", "1");
 long timeout = restRequest.paramAsLong("timeout", 5000l);
 String trace = restRequest.param("trace", "off");
 List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);

 validateBatchMessages(messages, topic);
 StopWatch watch = StopWatch.create().start();
 ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
 log.info("sendBatch topic {} client {} offset {} cost(ms) {}",
 topic, restChannel.getClientIp(), r.getMessageMetaList().get(0).getOffset(), watch.stop().elapsed());
 });
 if (StringUtils.equalsIgnoreCase("on", trace)) {
 client.sendBatch(ack, messages, timeout, actionListener);
 } else {
 client.sendBatchByOneFlush(ack, messages, timeout, actionListener);
 }
 }


 private void validateBatchMessages(List<HermesMessage> hermesMessages, String topic) {
 Assert.isTrue(hermesMessages.size() > 0, Errors.ILLEGAL_PARAMETER, "批量发送至少包含一条消息");
 long batchPayLoad = 0;
 Map topicMap = new HashMap();
 for (HermesMessage hermesMessage : hermesMessages) {
 Assert.isTrue(hermesMessage.getPayLoad() > 0, Errors.ILLEGAL_PARAMETER, "批量消息中每条消息的key和value不能同时为空");
 Assert.isTrue(hermesMessage.getPayLoad() <= messageMaxSize, Errors.ILLEGAL_PARAMETER, "单条消息长度不能大于" + messageMaxSize + "(key+value),topic:" + topic);
 batchPayLoad += hermesMessage.getPayLoad();
 topicMap.put(hermesMessage.getTopic(), hermesMessage.getTopic());
 }

 Assert.isTrue(topicMap.size() == 1, Errors.ILLEGAL_PARAMETER, "批量消息发送只能是相同topic");
 Assert.isTrue(StringUtils.equals(topic, hermesMessages.get(0).getTopic()), Errors.ILLEGAL_PARAMETER, "body中的消息的topic和请求参数中topic不一致");

 }

 @RequestMapping(value = {"/kafka/publish", "/kafka-proxy/kafka/publish"}, method = POST)
 public void publish(RestRequest restRequest, RestChannel restChannel) throws UnsupportedEncodingException {

 String topic = parseTopic(restRequest);
 final String msgKey = restRequest.param("message_key");
 final String msgVal = restRequest.param("message");
 Assert.isTrue(StringUtils.isNotEmpty(msgVal), Errors.ILLEGAL_PARAMETER, "message cannot be empty");
 HermesMessage hermesMessage = new HermesMessage();
 hermesMessage.setTopic(topic);
 if (StringUtils.isNotBlank(msgKey)) {
 hermesMessage.setKey(msgKey.getBytes("UTF-8"));
 }
 hermesMessage.setData(msgVal.getBytes("UTF-8"));
 StopWatch watch = StopWatch.create().start();
 ActionListener<MessageMeta> actionListener = ActionListener.wrap((ret, e) -> {
 if (ret != null) {
 log.info("publish, topic {} client {} offset {} cost(ms) {}",
 topic, restChannel.getClientIp(), ret.getOffset(), watch.stop().elapsed());
 }else{
 log.error("publish error",e);
 }
 //兼容老版本kafka-http-client，这里只返回ok
 restChannel.sendResponse(RestResponse.text("ok"));
 });
 client.send("1", hermesMessage, 20000, actionListener);
 }

 private String parseTopic(RestRequest request) {
 String topic = request.param("topic");
 if (topic == null || topic.isEmpty()) {
 StringBuilder errMsg = new StringBuilder("topic not specified. from ");
 String xff = request.header("X-Forwarded-For");
 if (xff != null && !xff.isEmpty()) {
 errMsg.append(" X-Forwarded-For: ").append(xff);
 }
 String clientId = request.header("Client-Id");
 if (clientId != null && !clientId.isEmpty()) {
 errMsg.append(" Client-Id: ").append(clientId);
 }
 log.error(errMsg.toString());
 }
 return topic;
 }
}

```

## file path:  hermes-proxy/hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
```java hermes-proxy/hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
package com.wacai.hermes.message;

import com.wacai.hermes.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.wacai.hermes.constants.HermesHeader.TAG;

@Getter
@Setter
public class HermesMessage {

    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    //时间戳
    private long timestamp;
    private UUID messageId = UUID.randomUUID();

    public HermesMessage() {
    }

    public HermesMessage(byte[] key, byte[] data) {
        this.key = key;
        this.data = data;
    }

    public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
    }

    public static Builder builder() {
        return new Builder();
    }

    public long getPayLoad() {
        long size = 0;
        if (key != null) {
            size += key.length;
        }
        if (data != null) {
            size += data.length;
        }
        return size;
    }

    public String getTraceId() {
        return null;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HermesMessage message = (HermesMessage)o;
        return Objects.equals(partition, message.partition) && timestamp == message.timestamp
                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
                messageId, message.messageId);
    }

    @Override
    public int hashCode() {

        int result = Objects.hash( topic, partition, timestamp, messageId);
        result = 31 * result + Arrays.hashCode(key);
        result = 31 * result + Arrays.hashCode(data);
        return result;
    }

    @Override
    public String toString() {
        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
    }

    public static class Builder {
        private String topic;
        private Integer partition;
        private byte[] key;
        private byte[] data;
        private Map<String, byte[]> headers = new HashMap<>();
        private long timestamp;

        public Builder setHeaders(Map<String, byte[]> headers) {
            // headers.entrySet().removeIf(entry -> entry.getKey().startsWith("hermes."));
            this.headers.putAll(headers);
            return this;
        }

        public Builder setTopic(String topic) {
            this.topic = topic;
            return this;
        }

        public Builder setPartition(Integer partition) {
            this.partition = partition;
            return this;
        }

        public Builder setKey(byte[] key) {
            this.key = key;
            return this;
        }

        public Builder setData(byte[] data) {
            this.data = data;
            return this;
        }

        public Builder setTimestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder setTag(String tag) {
            if (null != tag && !"".equals(tag = tag.trim())) {
                this.headers.put(TAG, tag.getBytes());
            }
            return this;
        }

        public HermesMessage build() {
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
        }
    }
}

```
## file path: hermes-core/src/main/java/com/wacai/hermes/core/message/KafkaProducerWrapper.java
```java hermes-core/src/main/java/com/wacai/hermes/core/message/KafkaProducerWrapper.java
package com.wacai.hermes.core.message;


import com.wacai.hermes.alert.AlarmMsg;
import com.wacai.hermes.alert.Alert;
import com.wacai.hermes.async.ActionListener;
import com.wacai.hermes.core.runtime.HermesRuntimeUtil;
import com.wacai.hermes.core.util.StopWatch;
import com.wacai.hermes.errors.Errors;
import com.wacai.hermes.errors.SendErrorException;
import com.wacai.hermes.message.BatchMessageMeta;
import com.wacai.hermes.message.HermesMessage;
import com.wacai.hermes.message.MessageMeta;
import com.wacai.hermes.message.ProducerWrapper;
import com.wacai.hermes.util.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.KafkaException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <p>
 * KafkaProducer 重要事项：
 * 1. ack 三种：0-不需要确认，1-需要leader确认，all-需要ISR所有成员确认
 * 2. 数据是先发送到缓存区，再通过后台线程发送到broker，可以配置时间和大小控制，默认不堆积
 * 3. 如果topic或者partition不存在，不会马上报错会等到超时时间默认10秒
 * </p>
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class KafkaProducerWrapper implements ProducerWrapper {

    private KafkaProducer producer;

    public KafkaProducerWrapper(KafkaProducer producer) {
        this.producer = producer;
    }


    public BatchMessageMeta asyncSend(List<HermesMessage> hermesMessageList, ActionListener<MessageMeta> actionListener) {
        BatchMessageMeta batchMessageMeta = new BatchMessageMeta();
        List<MessageMeta> metaList = hermesMessageList.stream()
                .map(message -> asyncSend(message, actionListener))
                .collect(Collectors.toList());
        batchMessageMeta.setMessageMetaList(metaList);
        return batchMessageMeta;
    }


    public MessageMeta asyncSend(HermesMessage message, ActionListener<MessageMeta> actionListener) {
        // fire-and-forget
        doSend(message, actionListener);
        return MessageMeta.create(message.getTopic());
    }

    protected SendFuture<RecordMetadata> doSend(HermesMessage message, ActionListener<MessageMeta> actionListener) {
        try {
            return new SendFuture(message, producer.send(buildFromHermesMessage(message), (metadata, e) -> {
                if (e == null) {
                    if (actionListener != null)
                        actionListener.onResponse(toMessageMeta(metadata));
                } else {
                    log.error("send error topic " + message.getTopic(), e);
                    if (actionListener != null)
                        actionListener.onException(e);
                }
            }));
        } catch (KafkaException e) {
            log.error("send error topic " + message.getTopic(), e);
            throw new SendErrorException("send error topic " + message.getTopic(), e);
        }
    }


    protected ProducerRecord buildFromHermesMessage(HermesMessage message) {
        ProducerRecord<byte[], byte[]> record =
                new ProducerRecord(message.getTopic(), message.getPartition(), null, message.getKey(), message.getData());
        return record;
    }

    private MessageMeta toMessageMeta(RecordMetadata metadata) {
        MessageMeta message = new MessageMeta();
        message.setTopic(metadata.topic());
        message.setPartition(metadata.partition());
        message.setOffset(metadata.offset());
        return message;
    }

    /**
     * 同步发布单条消息
     *
     * @param message
     * @param timeout
     * @param actionListener 异步回调
     * @return
     */
    public MessageMeta syncSend(HermesMessage message, long timeout, ActionListener<MessageMeta> actionListener) {
        if (timeout <= 0) {
            timeout = 5000;
        }

        StopWatch watch = StopWatch.create().start();
        SendFuture<RecordMetadata> future = doSend(message, actionListener);
        MessageMeta meta = toMessageMeta(future.get(timeout, TimeUnit.MILLISECONDS));
        watch.stop().trigger(2000, (cost) -> {
            AlarmMsg msg = AlarmMsg.builder()
                    .message("send kafka message too slow more than 2000ms, cost " + cost+" ms")
                    .build();
            Alert alert = HermesRuntimeUtil.get().getInstance(Alert.class);
            alert.send(msg);
        });
        return meta;
    }

    /**
     * 同步批量发送
     *
     * @param messages 消息
     * @param timeout  超时
     * @return
     */
    public BatchMessageMeta syncSend(List<HermesMessage> messages, long timeout, ActionListener<MessageMeta> actionListener) {
        Assert.isTrue(messages.size() > 0, Errors.ILLEGAL_PARAMETER, "同步发送消息列表不能为空");
        String topic = messages.get(0).getTopic();
        if (timeout <= 0) {
            timeout = 5000;
        }
        BatchMessageMeta batchMessageMeta = new BatchMessageMeta();
        List<SendFuture<RecordMetadata>> futures = new ArrayList<>();
        List<MessageMeta> mms = new ArrayList<>();
        for (HermesMessage message : messages) {
            SendFuture<RecordMetadata> future = this.doSend(message, actionListener);
            futures.add(future);
        }
        producer.flush();//flush全部发送出去，再统一取出回执
        for (SendFuture<RecordMetadata> future : futures) {
            MessageMeta message = toMessageMeta(future.get(timeout, TimeUnit.MILLISECONDS));
            mms.add(message);
        }
        batchMessageMeta.setMessageMetaList(mms);
        return batchMessageMeta;
    }

    @Override
    public void close() throws IOException {
        producer.close();
    }

    private static class SendFuture<T> {

        private HermesMessage message;
        private Future<T> future;

        public SendFuture(HermesMessage message, Future<T> future) {
            this.message = message;
            this.future = future;
        }

        public T get(long timeout, TimeUnit unit) {
            try {
                return this.future.get(timeout, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                throw new SendErrorException(
                        "send error interrupted topic " + message.getTopic() + " cause " + e.getMessage());
            } catch (ExecutionException e) {
                throw new SendErrorException("send error execution topic " + message.getTopic() + " cause " + e.getMessage());
            } catch (TimeoutException e) {
                throw new SendErrorException("send error timeout topic " + message.getTopic() + " cause " + e.getMessage());
            } catch (KafkaException e) {
                log.error("send error message topic " + message.getTopic(), e);
                throw new SendErrorException("send error topic " + message.getTopic(), e);
            }

        }
    }
}

```