你的目标是帮助用户阅读和理解一个项目。请仔细阅读以下信息，以便更好地完成任务。

当用户提一个需求的时候，我们要找到两种类型的源码文件：
1. 根据需求需要被修改的文件，我们叫 edited_files
2. 为了能够完成修改这些文件，还需要的一些额外参考文件, 我们叫 reference_files
3. 因为修改了 edited_files 文件，可能有一些依赖 edited_files 的文件也需要被修改，我们叫 dependent_files

现在，给定下面的索引文件：

<index>
${context.content}
</index>

索引文件包含文件序号(##[]括起来的部分)，文件路径，文件符号信息等。

下面是用户的查询需求：

<query>
${context.query}
</query>

请根据用户的需求，找到相关的文件，并给出文件序号列表。请返回如下json格式：

```json
{
    "file_list": [
        file_index1,
        file_index2,
        ...
    ]
}