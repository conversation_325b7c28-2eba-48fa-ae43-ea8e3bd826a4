你负责将修改的代码片段合并到用户原始代码，按照 apply_diff 工具要求输出合并后的代码，不用做其他任何解释。

## apply_diff
Description: Request to replace existing code using a search and replace block.
This tool allows for precise, surgical replaces to files by specifying exactly what content to search for and what to replace it with.
The tool will maintain proper indentation and formatting while making changes.
Only a single operation is allowed per tool use.
The SEARCH section must exactly match existing content including whitespace and indentation.
When applying the diffs, be extra careful to remember to change any closing brackets or other syntax that may be affected by the diff farther down in the file.
ALWAYS make as many changes in a single 'apply_diff' request as possible using multiple SEARCH/REPLACE blocks

Parameters:
- path: (required) The path of the file to modify (relative to the current workspace directory /System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4)
- diff: (required) The search/replace block defining the changes.

Diff format:
```
<<<<<<< SEARCH
:start_line: (required) The line number of original content where the search block starts.
-------
[exact content to find including whitespace]
=======
[new content to replace with]
>>>>>>> REPLACE

```


Example:

Original file:
```
1 | def calculate_total(items):
2 |     total = 0
3 |     for item in items:
4 |         total += item
5 |     return total
```

Search/Replace content:
```
<<<<<<< SEARCH
:start_line:1
-------
def calculate_total(items):
    total = 0
    for item in items:
        total += item
    return total
=======
def calculate_total(items):
    """Calculate total with 10% markup"""
    return sum(item * 1.1 for item in items)
>>>>>>> REPLACE

```

Search/Replace content with multi edits:
```
<<<<<<< SEARCH
:start_line:1
-------
def calculate_total(items):
    sum = 0
=======
def calculate_sum(items):
    sum = 0
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:4
-------
        total += item
    return total
=======
        sum += item
    return sum
>>>>>>> REPLACE
```


Usage:
<apply_diff>
<path>File path here</path>
<diff>
Your search/replace content here
You can use multi search/replace block in one diff block, but make sure to include the line numbers for each block.
Only use a single line of '=======' between search and replacement content, because multiple '=======' will corrupt the file.
</diff>
</apply_diff>



现在我们来完成实际的任务，用户的文件：
<file><path>hermes-proxy/pom.xml</path>
<content lines="1-153">
  1 | <?xml version="1.0" encoding="UTF-8"?>
  2 | <project xmlns="http://maven.apache.org/POM/4.0.0"
  3 |          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  4 |          xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  5 |     <parent>
  6 |         <artifactId>hermes-parent</artifactId>
  7 |         <groupId>com.wacai.middleware</groupId>
  8 |         <version>4.0-SNAPSHOT</version>
  9 |     </parent>
 10 |     <modelVersion>4.0.0</modelVersion>
 11 |
 12 |     <artifactId>hermes-proxy</artifactId>
 13 |
 14 |     <properties>
 15 |         <springboot.version>2.2.6.RELEASE</springboot.version>
 16 |     </properties>
 17 |
 18 |     <dependencies>
 19 |         <dependency>
 20 |             <groupId>com.wacai.middleware</groupId>
 21 |             <artifactId>hermes-api</artifactId>
 22 |         </dependency>
 23 |         <dependency>
 24 |             <groupId>com.wacai.middleware</groupId>
 25 |             <artifactId>hermes-core</artifactId>
 26 |         </dependency>
 27 |
 28 |         <dependency>
 29 |             <groupId>io.netty</groupId>
 30 |             <artifactId>netty-all</artifactId>
 31 |         </dependency>
 32 |
 33 |         <dependency>
 34 |             <groupId>org.apache.kafka</groupId>
 35 |             <artifactId>kafka-clients</artifactId>
 36 |         </dependency>
 37 |
 38 |         <dependency>
 39 |             <groupId>ch.qos.logback</groupId>
 40 |             <artifactId>logback-classic</artifactId>
 41 |             <version>1.2.3</version>
 42 |             <scope>compile</scope>
 43 |         </dependency>
 44 |
 45 |         <dependency>
 46 |             <groupId>com.wacai</groupId>
 47 |             <artifactId>redis-client-all-in</artifactId>
 48 |             <version>3.14.2</version>
 49 |             <exclusions>
 50 |                 <exclusion>
 51 |                     <groupId>log4j</groupId>
 52 |                     <artifactId>log4j</artifactId>
 53 |                 </exclusion>
 54 |             </exclusions>
 55 |         </dependency>
 56 |
 57 |         <dependency>
 58 |             <groupId>com.google.inject</groupId>
 59 |             <artifactId>guice</artifactId>
 60 |             <version>5.0.1</version>
 61 |         </dependency>
 62 |
 63 |         <!--注意：之前hermes使用的zk server为3.4，不能依赖高版本的curator -->
 64 |         <dependency>
 65 |             <groupId>org.apache.curator</groupId>
 66 |             <artifactId>curator-framework</artifactId>
 67 |         </dependency>
 68 |
 69 |         <dependency>
 70 |             <groupId>org.apache.curator</groupId>
 71 |             <artifactId>curator-recipes</artifactId>
 72 |             <version>2.13.0</version>
 73 |         </dependency>
 74 |
 75 |         <dependency>
 76 |             <groupId>io.jsonwebtoken</groupId>
 77 |             <artifactId>jjwt</artifactId>
 78 |         </dependency>
 79 |
 80 |         <!--
 81 |         <dependency>
 82 |             <groupId>org.springframework.boot</groupId>
 83 |             <artifactId>spring-boot-starter-actuator</artifactId>
 84 |             <version>${springboot.version}</version>
 85 |         </dependency>
 86 |          -->
 87 |     </dependencies>
 88 |
 89 |
 90 |     <build>
 91 |         <plugins>
 92 |             <plugin>
 93 |                 <groupId>org.springframework.boot</groupId>
 94 |                 <artifactId>spring-boot-maven-plugin</artifactId>
 95 |                 <version>${springboot.version}</version>
 96 |                 <configuration>
 97 |                     <!-- 工程主入口-->
 98 |                     <mainClass>com.wacai.hermes.proxy.HermesProxyApplication</mainClass>
 99 |                 </configuration>
100 |                 <executions>
101 |                     <execution>
102 |                         <goals>
103 |                             <goal>repackage</goal>
104 |                         </goals>
105 |                     </execution>
106 |                 </executions>
107 |             </plugin>
108 |             <plugin>
109 |                 <groupId>org.apache.maven.plugins</groupId>
110 |                 <artifactId>maven-compiler-plugin</artifactId>
111 |                 <version>3.7.0</version>
112 |                 <configuration>
113 |                     <source>1.8</source>
114 |                     <target>1.8</target>
115 |                     <encoding>UTF-8</encoding>
116 |                     <compilerArgument>-parameters</compilerArgument>
117 |                 </configuration>
118 |             </plugin>
119 |             <plugin>
120 |                 <groupId>org.apache.maven.plugins</groupId>
121 |                 <artifactId>maven-source-plugin</artifactId>
122 |                 <version>3.0.1</version>
123 |                 <executions>
124 |                     <execution>
125 |                         <id>attach-sources</id>
126 |                         <goals>
127 |                             <goal>jar</goal>
128 |                         </goals>
129 |                     </execution>
130 |                 </executions>
131 |             </plugin>
132 |             <plugin>
133 |                 <groupId>org.apache.maven.plugins</groupId>
134 |                 <artifactId>maven-surefire-plugin</artifactId>
135 |                 <configuration>
136 |                     <skip>true</skip>
137 |                 </configuration>
138 |             </plugin>
139 |         </plugins>
140 |         <resources>
141 |             <resource>
142 |                 <directory>src/main/java</directory>
143 |                 <includes>
144 |                     <include>**/*.xml</include>
145 |                 </includes>
146 |             </resource>
147 |             <resource>
148 |                 <directory>src/main/resources</directory>
149 |             </resource>
150 |         </resources>
151 |     </build>
152 |
153 | </project>
</content>
</file>

修改的代码片段：
```
// ... 保留原有内容 ...

<dependencies>
    // ... 保留原有依赖 ...

    <!-- 新增Dubbo依赖 -->
    <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo</artifactId>
    </dependency>
    <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo-dependencies-zookeeper</artifactId>
        <version>${dubbo.version}</version>
        <type>pom</type>
    </dependency>
</dependencies>

// ... 保留其余内容 ...
```