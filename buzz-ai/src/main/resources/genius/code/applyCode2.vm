你负责将修改的代码片段合并到用户原始代码，按照 apply_diff 工具要求输出合并后的代码，不用做其他任何解释。

## apply_diff
Description: Request to replace existing code using a search and replace block.
This tool allows for precise, surgical replaces to files by specifying exactly what content to search for and what to replace it with.
The tool will maintain proper indentation and formatting while making changes.
Only a single operation is allowed per tool use.
The SEARCH section must exactly match existing content including whitespace and indentation.
When applying the diffs, be extra careful to remember to change any closing brackets or other syntax that may be affected by the diff farther down in the file.
ALWAYS make as many changes in a single 'apply_diff' request as possible using multiple SEARCH/REPLACE blocks

Parameters:
- path: (required) The path of the file to modify (relative to the current workspace directory /System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4)
- diff: (required) The search/replace block defining the changes.

Diff format:
```
<<<<<<< SEARCH
:start_line: (required) The line number of original content where the search block starts.
-------
[exact content to find including whitespace]
=======
[new content to replace with]
>>>>>>> REPLACE

```


Example:

Original file:
```
1 | def calculate_total(items):
2 |     total = 0
3 |     for item in items:
4 |         total += item
5 |     return total
```

Search/Replace content:
```
<<<<<<< SEARCH
:start_line:1
-------
def calculate_total(items):
    total = 0
    for item in items:
        total += item
    return total
=======
def calculate_total(items):
    """Calculate total with 10% markup"""
    return sum(item * 1.1 for item in items)
>>>>>>> REPLACE

```

Search/Replace content with multi edits:
```
<<<<<<< SEARCH
:start_line:1
-------
def calculate_total(items):
    sum = 0
=======
def calculate_sum(items):
    sum = 0
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:4
-------
        total += item
    return total
=======
        sum += item
    return sum
>>>>>>> REPLACE
```


Usage:
<apply_diff>
<path>File path here</path>
<diff>
Your search/replace content here
You can use multi search/replace block in one diff block, but make sure to include the line numbers for each block.
Only use a single line of '=======' between search and replacement content, because multiple '=======' will corrupt the file.
</diff>
</apply_diff>



现在我们来完成实际的任务，用户的文件：
<file><path>hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>
<content lines="1-140">
  1 | package com.wacai.hermes.message;
  2 |
  3 | import com.wacai.hermes.util.StringUtils;
  4 | import lombok.Getter;
  5 | import lombok.Setter;
  6 |
  7 | import java.util.Arrays;
  8 | import java.util.HashMap;
  9 | import java.util.Map;
 10 | import java.util.Objects;
 11 | import java.util.UUID;
 12 |
 13 | import static com.wacai.hermes.constants.HermesHeader.TAG;
 14 |
 15 | @Getter
 16 | @Setter
 17 | public class HermesMessage {
 18 |
 19 |     private String topic;
 20 |     private Integer partition;
 21 |     private byte[] key;
 22 |     private byte[] data;
 23 |     private long timestamp;
 24 |     private UUID messageId = UUID.randomUUID();
 25 |
 26 |     public HermesMessage() {
 27 |     }
 28 |
 29 |     public HermesMessage(byte[] key, byte[] data) {
 30 |         this.key = key;
 31 |         this.data = data;
 32 |     }
 33 |
 34 |     public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
 35 |         this.topic = topic;
 36 |         this.partition = partition;
 37 |         this.key = key;
 38 |         this.data = data;
 39 |         this.timestamp = timestamp;
 40 |     }
 41 |
 42 |     public static Builder builder() {
 43 |         return new Builder();
 44 |     }
 45 |
 46 |     public long getPayLoad() {
 47 |         long size = 0;
 48 |         if (key != null) {
 49 |             size += key.length;
 50 |         }
 51 |         if (data != null) {
 52 |             size += data.length;
 53 |         }
 54 |         return size;
 55 |     }
 56 |
 57 |     public String getTraceId() {
 58 |         return null;
 59 |     }
 60 |
 61 |
 62 |     @Override
 63 |     public boolean equals(Object o) {
 64 |         if (this == o) {
 65 |             return true;
 66 |         }
 67 |         if (o == null || getClass() != o.getClass()) {
 68 |             return false;
 69 |         }
 70 |         HermesMessage message = (HermesMessage)o;
 71 |         return Objects.equals(partition, message.partition) && timestamp == message.timestamp
 72 |                 && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
 73 |                 messageId, message.messageId);
 74 |     }
 75 |
 76 |     @Override
 77 |     public int hashCode() {
 78 |
 79 |         int result = Objects.hash( topic, partition, timestamp, messageId);
 80 |         result = 31 * result + Arrays.hashCode(key);
 81 |         result = 31 * result + Arrays.hashCode(data);
 82 |         return result;
 83 |     }
 84 |
 85 |     @Override
 86 |     public String toString() {
 87 |         return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
 88 |     }
 89 |
 90 |     public static class Builder {
 91 |         private String topic;
 92 |         private Integer partition;
 93 |         private byte[] key;
 94 |         private byte[] data;
 95 |         private Map<String, byte[]> headers = new HashMap<>();
 96 |         private long timestamp;
 97 |
 98 |         public Builder setHeaders(Map<String, byte[]> headers) {
 99 |             // headers.entrySet().removeIf(entry -> entry.getKey().startsWith("hermes."));
100 |             this.headers.putAll(headers);
101 |             return this;
102 |         }
103 |
104 |         public Builder setTopic(String topic) {
105 |             this.topic = topic;
106 |             return this;
107 |         }
108 |
109 |         public Builder setPartition(Integer partition) {
110 |             this.partition = partition;
111 |             return this;
112 |         }
113 |
114 |         public Builder setKey(byte[] key) {
115 |             this.key = key;
116 |             return this;
117 |         }
118 |
119 |         public Builder setData(byte[] data) {
120 |             this.data = data;
121 |             return this;
122 |         }
123 |
124 |         public Builder setTimestamp(long timestamp) {
125 |             this.timestamp = timestamp;
126 |             return this;
127 |         }
128 |
129 |         public Builder setTag(String tag) {
130 |             if (null != tag && !"".equals(tag = tag.trim())) {
131 |                 this.headers.put(TAG, tag.getBytes());
132 |             }
133 |             return this;
134 |         }
135 |
136 |         public HermesMessage build() {
137 |             return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
138 |         }
139 |     }
140 | }
</content>
</file>

修改的代码片段：
```
@Getter
@Setter
public class HermesMessage {
    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    private long timestamp;
    private UUID messageId = UUID.randomUUID();
    private Map<String, byte[]> headers; // 新增 headers 字段

    // ... 其他代码保持不变 ...

    public static class Builder {
        private String topic;
        private Integer partition;
        private byte[] key;
        private byte[] data;
        private Map<String, byte[]> headers = new HashMap<>();
        private long timestamp;

        // ... 其他方法保持不变 ...

        public HermesMessage build() {
            HermesMessage message = new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
            message.setHeaders(this.headers); // 设置 headers
            return message;
        }
    }
}
```