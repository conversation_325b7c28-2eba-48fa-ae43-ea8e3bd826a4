package com.buzz.ai.view;


import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.actionSystem.impl.ActionButton;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.labels.LinkLabel;
import com.wacai.middleware.wapilot.base.resource.WapilotIcons;
import com.wacai.middleware.wapilot.base.util.ColorUtil;
import com.wacai.middleware.wapilot.ui.component.RoundRectPanel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;


@Deprecated
public abstract class CollapsiblePanelDemo {
    private static final JBColor BORDER_COLOR = new JBColor(0xDFE1E5, 0x474747);
    private JPanel content;
    private boolean collapsed = false;
    private JPanel headPanel;
    private Presentation buttonText;
    private RoundRectPanel basePanel;

    public CollapsiblePanelDemo(JPanel content) {
        this(content, false);
    }

    public CollapsiblePanelDemo(JPanel content, boolean collapsed) {
        this.content = content;
        this.basePanel = new RoundRectPanel(new BorderLayout());
        setupUI();
        this.collapsed = collapsed;
    }

    private void setupUI() {
        basePanel.setBorder(BorderFactory.createMatteBorder(1, 1, 0, 1, BORDER_COLOR));
        basePanel.setBorderColor(BORDER_COLOR);
        basePanel.setLayout(new BorderLayout(0, 0));
        basePanel.add(createHeaderPanel(), BorderLayout.NORTH);
        basePanel.add(this.content, BorderLayout.CENTER);
        // 初始状态
        updateDisplay();
    }

    public RoundRectPanel getBaseComponent() {
        return basePanel;
    }

    private JPanel createHeaderPanel() {
        headPanel = new JPanel(new BorderLayout());
        // 使用 FlowLayout 并设置垂直间距为 0
        JPanel titlePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 8, 3));

        // 设置背景色
        Color backgroundColor = ColorUtil.getEditorBackgroundColor();
        headPanel.setBackground(backgroundColor);
        titlePanel.setBackground(backgroundColor);

        // header只在底部添加边框
        headPanel.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, BORDER_COLOR));

        // 添加折叠按钮
        titlePanel.add(createButton());

        //添加文件名
        JLabel fileNameLabel = new JLabel("HermesMessage.java");
        fileNameLabel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                fileNameLabel.setBackground(ColorUtil.getListBackground());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                fileNameLabel.setBackground(ColorUtil.getEditorBackgroundColor());
            }

            @Override
            public void mouseClicked(MouseEvent e) {
                System.out.println("click....");
            }
        });
        titlePanel.add(fileNameLabel);

        //添加路径
        JBLabel pathLabel = new JBLabel("src/main/java/com/wacai/hermes/message/HermesMessage.java");
        pathLabel.setForeground(ColorUtil.getAuxiliaryForegroundColor());
        pathLabel.setFont(pathLabel.getFont().deriveFont(12.0f));
        titlePanel.add(pathLabel);

        // 设置固定高度
        headPanel.setPreferredSize(new Dimension(-1, 34));

        headPanel.add(titlePanel);
        headPanel.add(createOperationPanel(),BorderLayout.EAST);

        return headPanel;
    }

    protected JPanel createOperationPanel(){
        JPanel operationPanel = new JPanel(new FlowLayout(FlowLayout.LEFT,8, 3));

        JBLabel apply = new JBLabel("Apply");
        operationPanel.add(apply);


        JBLabel more = new JBLabel("...");
        operationPanel.add(more);
        return operationPanel;
    }

    protected abstract JPanel setupTitlePanel(JPanel titlePanel);

    private ActionButton createButton() {
        buttonText = new Presentation("");
        buttonText.setIcon(WapilotIcons.Cols);
        ActionButton button = new ActionButton(
                new DumbAwareAction() {
                    @Override
                    public void actionPerformed(AnActionEvent e) {
                        toggleCollapse();
                    }
                },
                buttonText,
                "",
                new Dimension(28, 28)
        );
        return button;
    }

    private void toggleCollapse() {
        collapsed = !collapsed;
        updateDisplay();
    }

    private void updateDisplay() {
        content.setVisible(!collapsed);
        if (collapsed) {
            buttonText.setIcon(WapilotIcons.Expand);
        } else {
            buttonText.setIcon(WapilotIcons.Cols);
        }
        basePanel.revalidate();
        basePanel.repaint();
    }


}