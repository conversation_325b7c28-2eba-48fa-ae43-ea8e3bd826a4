//package com.buzz.ai.markdown;
//
//import io.noties.prism4j.*;
//import org.apache.commons.text.StringEscapeUtils;
//
//import java.util.List;
//
//public class PrismConvert {
//
//    final Prism4j prism4j = new Prism4j();
//
//    public String convert(String code, String language) {
//
//        Grammar grammar = this.prism4j.grammar(language);
//        if (grammar == null) {
//            return null;
//        }
//        final StringBuilder sb = new StringBuilder();
//        List nodes = this.prism4j.tokenize(code, grammar);
//        Visitor visitor = new Visitor(){
//
//            protected void visitText( Text text) {
//
//                String data = StringEscapeUtils.escapeHtml3((String)text.literal());
//                sb.append(data);
//            }
//
//            protected void visitSyntax( Syntax syntax) {
//                sb.append("<span class=\"").append(syntax.type()).append("\">");
//                this.visit(syntax.children());
//                sb.append("</span>");
//            }
//
//        };
//        visitor.visit(nodes);
//        return sb.toString();
//
//    }
//
//    public static void main(String[] args) {
//        String input = """
//                package com.buzz.ai.markdown;
//
//                import io.noties.prism4j.*;
//                import org.apache.commons.text.StringEscapeUtils;
//
//                import java.util.List;
//
//                public class PrismConvert {
//
//                    final Prism4j prism4j = new Prism4j();
//
//                    public String convert(String code, String language) {
//
//                        Grammar grammar = this.prism4j.grammar(language);
//                        if (grammar == null) {
//                            return null;
//                        }
//                        final StringBuilder sb = new StringBuilder();
//                        List nodes = this.prism4j.tokenize(code, grammar);
//                        Visitor visitor = new Visitor(){
//
//                            protected void visitText( Text text) {
//
//                                String data = StringEscapeUtils.escapeHtml3((String)text.literal());
//                                sb.append(data);
//                            }
//
//                            protected void visitSyntax( Syntax syntax) {
//                                sb.append("<span class=\\"").append(syntax.type()).append("\\">");
//                                this.visit(syntax.children());
//                                sb.append("</span>");
//                            }
//
//                        };
//                        visitor.visit(nodes);
//                        return sb.toString();
//
//                    }
//
//                    public static void main(String[] args) {
//                        String input = ""\"
//                                ""\";
//                        new PrismConvert().convert(input, "java");
//
//                    }
//                }
//
//                """;
//        String out = new PrismConvert().convert(input, "java");
//        System.out.println(out);
//    }
//}
