package com.buzz.ai.prompt;

import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import java.io.StringWriter;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

public class TemplateRender {

    public static final String ROOT = "genius";
    public static final String GENIUS_SRE = "/sre";
    public static final String GENIUS_MIGRATION = "/migration";
    public static final String GENIUS_SQL = "/sql";
    public static final String GENIUS_HARMONYOS = "/harmonyos";
    public static final String GENIUS_PAGE = "/page";
    public static final String GENIUS_PRACTISES = "/practises";
    public static final String GENIUS_CODE = "/code";
    public static final String GENIUS_AGENT = "/agent";
    public static final String GENIUS_CICD = "/cicd";
    public static final String GENIUS_ERROR = "/error";

    private final String pathPrefix;
    private final VelocityContext velocityContext;
    //private final TemplateRoleSplitter splitter;
    private TemplateContext context;
    private Object actions;
    private final Map<String, String> templateCache;

    public TemplateRender(String pathPrefix) {
        this.pathPrefix = pathPrefix;
        this.velocityContext = new VelocityContext();
        //this.splitter = new TemplateRoleSplitter();
        this.actions = "";
        this.templateCache = new HashMap<>();
    }

    public void setContext(TemplateContext context) {
        this.context = context;
    }

    public void setActions(Object actions) {
        this.actions = actions;
    }

    public String getTemplate(String filename) {

        return retrieveDefaultTemplate(filename);
    }


    private String retrieveDefaultTemplate(String filename) {
        if (templateCache.containsKey(filename)) {
            return templateCache.get(filename);
        }

        String path = getDefaultFilePath(filename);
        var resourceUrl = getClass().getClassLoader().getResource(path);
        if (resourceUrl == null) {
            throw new TemplateNotFoundError(path);
        }

        try {
            byte[] bytes = resourceUrl.openStream().readAllBytes();
            String string = new String(bytes, Charset.forName("UTF-8"));
            templateCache.put(filename, string);
            return string;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private String getDefaultFilePath(String filename) {
        String languagePrefix = ROOT + "/" + pathPrefix;
        languagePrefix = languagePrefix.replaceAll("/$", "");
        String path = languagePrefix + "/" + filename;

        if (getClass().getClassLoader().getResource(path) != null) {
            return path;
        }

        String defaultLanguagePrefix = ROOT + "/zh/" + pathPrefix;
        defaultLanguagePrefix = defaultLanguagePrefix.replaceAll("/$", "");
        return defaultLanguagePrefix + "/" + filename;
    }



    public String renderTemplate(String template) {
        ClassLoader oldContextClassLoader = Thread.currentThread().getContextClassLoader();
        Thread.currentThread().setContextClassLoader(TemplateRender.class.getClassLoader());

        velocityContext.put("context", context);
        StringWriter sw = new StringWriter();
        Velocity.evaluate(velocityContext, sw, "#" + this.getClass().getName(), template);
        String result = sw.toString();

        Thread.currentThread().setContextClassLoader(oldContextClassLoader);

        return result;
    }


    public class TemplateNotFoundError extends RuntimeException {
        public TemplateNotFoundError(String path) {
            super("Prompt not found at path: " + path);
        }
    }
}