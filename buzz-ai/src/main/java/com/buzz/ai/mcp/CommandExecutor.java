package com.buzz.ai.mcp;

import com.buzz.ai.mcp.commands.*;
import java.io.*;

public class CommandExecutor {
    public void execute(CommandSpec cmdSpec) throws IOException {
        Command cmd = createCommand(cmdSpec.getCommand());
        cmd.setArgs(cmdSpec.getArgs());
        System.out.print(cmd.execute());
    }

    private Command createCommand(String commandType) {
        switch (commandType) {
            case "git":
                return new GitCommand();
            case "dir":
                return new DirCommand();
            case "local_search":
                return new LocalSearchCommand();
            default:
                throw new IllegalArgumentException("Unknown command: " + commandType);
        }
    }
}