package com.buzz.ai.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MCPParser {
    private static final Pattern MCP_BLOCK = Pattern.compile("<\\|mcp\\|>(.*?)<\\|end\\|>", Pattern.DOTALL);
    private static final ObjectMapper mapper = new ObjectMapper();

    public static List<CommandSpec> extractCommands(String response) throws Exception {
        List<CommandSpec> commands = new ArrayList<>();
        Matcher matcher = MCP_BLOCK.matcher(response);
        while (matcher.find()) {
            String json = matcher.group(1).trim();
            CommandSpec cmd = mapper.readValue(json, CommandSpec.class);
            commands.add(cmd);
        }
        return commands;
    }
}
