package com.buzz.ai.mcp;

import java.util.*;

public class MCPClient {
    private final CommandExecutor executor = new CommandExecutor();

    public void run(String userInput) throws Exception {

        String response = DeepSeekAPI.chat(userInput);
        System.out.println("LLM Response:\n" + response);

        try {
            List<CommandSpec> commands = MCPParser.extractCommands(response);
            for (CommandSpec cmd : commands) {
                executor.execute(cmd);
            }
        } catch (Exception e) {
            System.err.println("Failed to parse MCP response: " + e.getMessage());
        }
    }

    public static void main(String[] args) throws Exception {
        MCPClient client = new MCPClient();
//        client.run("显示src/main/java目录结构");
//        client.run("请提交 Main.java 的修改，注释写“修复打印语句");
//        client.run("把 main.java 加入 Git 暂存区，然后提交，备注为 \"初始化\"");
//        client.run("查找 Blog");
        client.run("优化 ProducerController，为发送消息增加 header，并设置到kafka中");

//        Scanner scanner = new Scanner(System.in);
//        System.out.println("输入你要执行的操作：");
//        while (true) {
//            System.out.print("> ");
//            String input = scanner.nextLine();
//            if ("exit".equalsIgnoreCase(input)) break;
//            client.run(input);
//        }
    }
}
