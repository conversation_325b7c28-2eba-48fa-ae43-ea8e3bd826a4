package com.buzz.ai.mcp.commands;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.util.*;
import java.text.DecimalFormat;

public class DirCommand implements Command {
    private static final Set<String> EXCLUDED_DIRS = Set.of(
            ".idea", "build", "target", "node_modules"
    );
    private static final DecimalFormat SIZE_FORMAT = new DecimalFormat("#.#");

    private Path basePath;
    private final StringBuilder output;
    private Set<Path> gitIgnoredPaths;
    private Map<String, Object> args;

    public DirCommand() {
        this.output = new StringBuilder();
    }

    @Override
    public void setArgs(Map<String, Object> args) {
        this.args = args;
        String path = (String) args.get("path");
        this.basePath = Paths.get("/work/dist/branch/wacai/middleware/hermes-parent4").toAbsolutePath();

        try {
            this.gitIgnoredPaths = loadGitIgnored();
        } catch (IOException e) {
            this.gitIgnoredPaths = new HashSet<>();
        }
    }

    private Set<Path> loadGitIgnored() throws IOException {
        Set<Path> ignored = new HashSet<>();
        Path gitIgnore = basePath.resolve(".gitignore");
        if (Files.exists(gitIgnore)) {
            List<String> patterns = Files.readAllLines(gitIgnore);
            for (String pattern : patterns) {
                if (!pattern.startsWith("#") && !pattern.isEmpty()) {
                    ignored.add(basePath.resolve(pattern));
                }
            }
        }
        return ignored;
    }

    @Override
    public String execute() throws IOException {
        output.setLength(0); // Clear any previous output
        output.append(basePath.getFileName()).append("/\n");
        listDirectory(basePath, "");
        return output.toString();
    }

    private void listDirectory(Path path, String indent) throws IOException {
        if (shouldSkipDirectory(path)) {
            return;
        }

        File[] files = path.toFile().listFiles();
        if (files == null) return;

        Arrays.sort(files, Comparator.comparing(File::getName));

        for (int i = 0; i < files.length; i++) {
            File file = files[i];
            boolean isLast = (i == files.length - 1);
            String prefix = indent + (isLast ? "└── " : "├── ");

            if (file.isFile()) {
                output.append(prefix)
                        .append(file.getName())
                        .append("\n");
            } else if (file.isDirectory()) {
                output.append(prefix).append(file.getName()).append("/\n");
                String newIndent = indent + (isLast ? "    " : "│   ");
                listDirectory(file.toPath(), newIndent);
            }
        }
    }

    private boolean shouldSkipDirectory(Path path) {
        String name = path.getFileName().toString();
        return EXCLUDED_DIRS.contains(name) || gitIgnoredPaths.contains(path);
    }


}