package com.buzz.ai.mcp.commands;

import lombok.Setter;
import java.io.*;
import java.util.*;

public class GitCommand implements Command {
    @Setter
    private Map<String, Object> args;

    @Override
    public String execute() throws IOException {
        String cmd = buildGitCommand();
        return runCommand(cmd);
    }

    private String buildGitCommand() {
        String operation = (String) args.get("operation");
        if (operation == null) {
            throw new IllegalArgumentException("Git operation must be specified");
        }

        switch (operation) {
            case "add":
                return "git add " + String.join(" ", (List<String>) args.get("files"));
            case "commit":
                return "git commit -m \"" + args.get("message") + "\"";
            case "push":
                String branch = (String) args.getOrDefault("branch", "");
                return "git push" + (branch.isEmpty() ? "" : " origin " + branch);
            default:
                throw new IllegalArgumentException("Unknown git operation: " + operation);
        }
    }

    private String runCommand(String cmd) throws IOException {
        StringBuilder output = new StringBuilder();
        output.append("Executing: ").append(cmd).append("\n");
        
        Process proc = Runtime.getRuntime().exec(cmd);
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(proc.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }
        return output.toString();
    }
}