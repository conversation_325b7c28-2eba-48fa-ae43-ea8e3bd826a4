package com.buzz.ai.mcp;

import java.net.http.*;
import java.net.URI;
import java.net.http.HttpRequest.BodyPublishers;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.*;

public class DeepSeekAPI {
    private static final String API_URL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
    private static final String API_KEY = "b76b6732-4312-4c9e-b533-4b91cb177387";
    private static final ObjectMapper mapper = new ObjectMapper();

    public static String chat(String userPrompt) throws Exception {
        Map<String, Object> request = new HashMap<>();
        request.put("model", "deepseek-v3-241226");
        request.put("messages", List.of(
                Map.of("role", "user", "content", userPrompt),
                Map.of("role", "system", "content", PromptBuilder.buildSystemPrompt())

        ));
        request.put("temperature", 0);
        System.out.println(JSON.toJSONString(request.get("messages")));

        HttpClient client = HttpClient.newHttpClient();
        HttpRequest httpRequest = HttpRequest.newBuilder()
                .uri(URI.create(API_URL))
                .header("Authorization", "Bearer " + API_KEY)
                .header("Content-Type", "application/json")
                .POST(BodyPublishers.ofString(mapper.writeValueAsString(request)))
                .build();

        HttpResponse<String> response = client.send(httpRequest, HttpResponse.BodyHandlers.ofString());

        // 检查响应状态码
        if (response.statusCode() != 200) {
            throw new RuntimeException("Failed to get response: " + response.body());
        }

        Map<String, Object> result = mapper.readValue(response.body(), Map.class);
        List<?> choices = (List<?>) result.get("choices");
        if (choices.isEmpty()) {
            throw new RuntimeException("No choices returned from API");
        }

        Map<String, Map> message = (Map<String, Map>) choices.get(0);
        return (String) message.get("message").get("content");
    }
}
