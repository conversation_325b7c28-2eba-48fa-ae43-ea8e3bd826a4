package com.buzz.ai.agent;

import com.buzz.ai.llm.ChatMessage;
import com.buzz.ai.llm.DefaultLLMProvider;
import com.buzz.ai.llm.LLMProvider;
import com.buzz.ai.prompt.TemplateRender;
import com.buzz.ai.util.AutoRequestSubscriber;
import com.buzz.ai.view.Console;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Flow;

public class CodeApplyAgent {

    private ExecutorService threadPool  = Executors.newFixedThreadPool(4);
    private LLMProvider llmProvider = new DefaultLLMProvider();
    List<ChatMessage> historyChatList = new ArrayList<>();
    private Console console = new Console();
    private StringBuffer  out = new StringBuffer();
    private CountDownLatch waitFinish = new CountDownLatch(1);


    public CodeApplyAgent() {
    }

    public void chat(String code) {
        threadPool.submit(() -> {
            historyChatList.add(new ChatMessage("user", getCodeApplyPrompt(code)));
            Flow.Publisher<String> response = llmProvider.stream(null,historyChatList);
            processResponse(response);
        });

        try {
            waitFinish.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }

    protected void processResponse(Flow.Publisher<String> response) {
        console.beginResponse();
        response.subscribe(new AutoRequestSubscriber<String>() {
            @Override
            protected void handleItem(String item) {
                console.updateMessage(item);
                out.append(item);
            }

            @Override
            public void onComplete() {
                console.completeResponse();
                waitFinish.countDown();
                //saveResult();
            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
            }
        });
    }

    protected String getCodeApplyPrompt(String code) {
        TemplateRender templateRender = new TemplateRender("code");
        String agentTemplate = templateRender.getTemplate(code);
        return templateRender.renderTemplate(agentTemplate);
    }




    private void saveResult() {
        try {
            Path projectRoot = Paths.get("").toAbsolutePath();
            Path testDir = projectRoot.resolve("test/code");
            if (!Files.exists(testDir)) {
                Files.createDirectories(testDir);
            }

            // Find the next available file number
            int fileNumber = 1;
            Path filePath;
            do {
                filePath = testDir.resolve("test-code-" + fileNumber + ".md");
                fileNumber++;
            } while (Files.exists(filePath));

            // Get current time
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

            // Create content with timestamp
            String content = timestamp + "\n\n" + out.toString();

            // Write to file
            Files.writeString(filePath, content, StandardCharsets.UTF_8);
            System.out.println("Result saved to: " + filePath);

            // Clear the buffer after saving
            out.setLength(0);
        } catch (IOException e) {
            System.err.println("Error saving result: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
//         new CodeChat("context-hermes").chat("请优化 ProducerController，为发送消息增加 header 属性");
//        new CodeChat("context-hermes").chat("在 `HermesMessage` 类中添加 `headers` 字段，并更新相关方法");
//        new CodeChat("context-hermes").chat("修改HermesMessage，删除 Builder 中的headers，因为实际并没有用到");
//        new CodeChat("context-buzz-ai").chat("优化 saveResult()方法，把timestamp和out改为json格式保存");
        new CodeChat("context-wapilot").chat("我想为 createHeadPanel() 方法中的path，增加一个自动截取方法，但发现JLabel宽度不够显示内容，可以自动截取，截取是从头开始截取增加...");
//        new CodeChat().chat("删除border");
    }
}
