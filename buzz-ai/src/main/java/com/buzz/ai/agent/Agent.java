package com.buzz.ai.agent;

import com.buzz.ai.devin.command.DevinEngineer;
import com.buzz.ai.llm.ChatMessage;
import com.buzz.ai.llm.DefaultLLMProvider;
import com.buzz.ai.llm.LLMProvider;
import com.buzz.ai.prompt.*;
import com.buzz.ai.tools.ToolCommand;
import com.buzz.ai.tools.command.ToolExecutor;
import com.buzz.ai.util.AutoRequestSubscriber;
import com.buzz.ai.util.CodeFence;
import com.buzz.ai.util.DebugLogger;
import com.buzz.ai.view.*;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class Agent {

    private LLMProvider llmProvider = new DefaultLLMProvider();
    private Console console = new Console();
    private ExecutorService threadPool;
    private DevinEngineer devinEngineer = new DevinEngineer();
    StringBuffer records = new StringBuffer();

    public Agent() {
        threadPool = Executors.newFixedThreadPool(4);
    }

    private CountDownLatch waitFinish = new CountDownLatch(1);

    private String question;
    List<ChatMessage> historyChatList = new ArrayList<>();

    public void submit(String question) {
        this.question = question;
        threadPool.submit(() -> {
            try {
                System.out.println("User=>" + question);
                historyChatList.add(new ChatMessage("user", question + "\n" + getUserEnvironment()));
                Flow.Publisher<String> response = llmProvider.stream(getSystemPrompt(), historyChatList);
                processResponse(response);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        try {
            waitFinish.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private int group = 1;

    protected String getUserEnvironment() {
        return "<environment_details># Open Tabs\n hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java\n # Current Workspace Directory (/System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4) Files\nactions/\nhermes-agent/\nhermes-api/\nhermes-center/\nhermes-core/\nhermes-proxy/\n</environment_details>";
    }

    protected void handleMessage(String message) {
        //解析完整消息
        List<CodeFence> codeFenceList = CodeFence.parseAll(message);
        List<CodeFence> readToolList = codeFenceList.stream().filter(i -> ToolCommand.read_tools.contains(i.getName())).toList();
        if (readToolList.size() > 1) {
            DebugLogger.error(this.getClass(), "only support one tool use per message: {}", message);
            return;
        }

        List<CodeFence> writeToolList = codeFenceList.stream().filter(i -> ToolCommand.write_tools.contains(i.getName())).toList();

        //如果没有 toolUse 结束
        if (readToolList.isEmpty() && writeToolList.isEmpty()) {
            waitFinish.countDown();
            System.out.println("devinList.isEmpty! finished!");
            saveResult();
            return;
        }

        //记录llm返回的数据
        historyChatList.add(new ChatMessage("assistant", message));

        String input = null;
        //执行read命令
        if (!readToolList.isEmpty()) {
            CodeFence codeFence = readToolList.get(0);
            try {
                input = ToolExecutor.execute(codeFence);
            } catch (Exception e) {
                e.printStackTrace();
                //LLM返回的devin不合法，我们需要告诉LLM
                //String devinStr = devinList.stream().map(i -> i.getText()).distinct().collect(Collectors.joining("\n"));
                //input = "你输入的devin格式不正确，请重新输入！你输入的devinStr：" + chunk.getText();
                return;
            }
        }

        //write结果
        if (!writeToolList.isEmpty()) {
            CodeFence chunk = writeToolList.get(0);
            String name = chunk.getName();
            String path = chunk.getAttribute("path");
            input= String.format(" [%s for '%s'] Result:Changes successfully",name,path);
        }


//        System.out.println("========devin begin==================");
//        System.out.println(devinStr);
//        System.out.println("========devin end  ==================");


        historyChatList.add(new ChatMessage("user", input));
        threadPool.submit(() -> {
            Flow.Publisher<String> response = llmProvider.stream(getSystemPrompt(), historyChatList);
            processResponse(response);
        });

    }

    protected void processResponse(Flow.Publisher<String> response) {
        StringBuffer out = new StringBuffer();
        console.beginResponse();
        response.subscribe(new AutoRequestSubscriber<String>() {
            @Override
            protected void handleItem(String item) {
                console.updateMessage(item);
                out.append(item);
                records.append(item);
            }

            @Override
            public void onComplete() {
                console.completeResponse();
                handleMessage(out.toString());

            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
            }
        });
    }

    private void saveResult() {
        try {
            Path projectRoot = Paths.get("").toAbsolutePath();
            Path testDir = projectRoot.resolve("test/agent");
            if (!Files.exists(testDir)) {
                Files.createDirectories(testDir);
            }

            // Find the next available file number
            int fileNumber = 1;
            Path filePath;
            do {
                filePath = testDir.resolve("test-agent-" + fileNumber + ".md");
                fileNumber++;
            } while (Files.exists(filePath));

            // Get current time
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

            // Create content with timestamp
            String content = timestamp + "\n\n" + records.toString();

            // Write to file
            Files.writeString(filePath, content, StandardCharsets.UTF_8);
            System.out.println("Result saved to: " + filePath);

            // Clear the buffer after saving
            records.setLength(0);
        } catch (IOException e) {
            System.err.println("Error saving result: " + e.getMessage());
        }
    }


    protected String getSystemPrompt() {
        TemplateRender templateRender = new TemplateRender("agent");
        String agentTemplate = templateRender.getTemplate("system.vm");
        return templateRender.renderTemplate(agentTemplate);
    }


    protected String getContext() {
        TemplateRender templateRender = new TemplateRender("agent");
        String agentTemplate = templateRender.getTemplate("context-hermes.vm");
        return templateRender.renderTemplate(agentTemplate);
    }


    protected String getPomContext() {
        TemplateRender templateRender = new TemplateRender("agent");
        String agentTemplate = templateRender.getTemplate("context-buzz-ai.vm");
        return templateRender.renderTemplate(agentTemplate);
    }
}
