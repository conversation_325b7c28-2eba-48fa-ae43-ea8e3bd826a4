package com.buzz.ai.agent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.buzz.ai.llm.ChatMessage;
import com.buzz.ai.llm.DefaultLLMProvider;
import com.buzz.ai.llm.LLMProvider;
import com.buzz.ai.prompt.TemplateContext;
import com.buzz.ai.prompt.TemplateRender;
import com.buzz.ai.util.AutoRequestSubscriber;
import com.buzz.ai.view.Console;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Flow;

public class AutoChatAgent {

    private LLMProvider llmProvider = new DefaultLLMProvider();
    private Console console = new Console();
    private ExecutorService threadPool;

    public AutoChatAgent() {
        threadPool = Executors.newFixedThreadPool(4);
    }

    private String question;

    public static void main(String[] args) {
        new AutoChatAgent().submit("优化 ProducerController，为发送消息增加 header，并设置到kafka中");
    }
    public void submit(String question) {
        this.question = question;
        threadPool.submit(() -> {
            System.out.println("User=>" + question);
            List<ChatMessage> messageList = new ArrayList<>();
            messageList.add(new ChatMessage("user", getSystemPrompt(question)));
            Flow.Publisher<String> response = llmProvider.stream(getSystemPrompt(question), messageList);
            processResponse(response);
        });
    }

    protected void processResponse(Flow.Publisher<String> response) {
        StringBuffer out = new StringBuffer();
        response.subscribe(new AutoRequestSubscriber<String>() {
            @Override
            protected void handleItem(String item) {
                console.updateMessage(item);
                out.append(item);
            }

            @Override
            public void onComplete() {
               System.exit(-1);
            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
            }
        });
    }

    protected String getSystemPrompt(String question) {
        TemplateRender templateRender = new TemplateRender("agent");
        String agentTemplate = templateRender.getTemplate("auto-chat.vm");
        String content = loadIndex();
        templateRender.setContext(new Context(content, question));
        return templateRender.renderTemplate(agentTemplate);
    }

    protected String loadIndex() {
        String path = "/System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4/.auto-coder/index.json";
        StringBuilder content = new StringBuilder();

        try (BufferedReader br = new BufferedReader(new FileReader(path))) {
            String line;
            while ((line = br.readLine()) != null) {
                content.append(line).append("\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        JSONObject jsonObject = JSON.parseObject(content.toString());
        StringBuffer files = new StringBuffer();
        int i = 0;
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            JSONObject value = (JSONObject) entry.getValue();
            files.append("##[" + i + "]");
            files.append(key + "\n");
            files.append("```\n");
            files.append(value.getString("symbols"));
            files.append("\n");
            files.append("```\n");
            ++i;
        }
        return files.toString();
    }


    @Data
    @AllArgsConstructor
    public static class Context implements TemplateContext {
        private String content;
        private String query;
    }
}
