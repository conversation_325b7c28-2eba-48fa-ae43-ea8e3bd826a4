package com.buzz.ai.apply;

import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;

import java.io.IOException;

/**
 *  test-code-10.md
 */
public class Code {

    public static String InterfaceOriginal= """
            public interface AuthService {
            
                void startAuthServer();
                void stopAuthServer();
            }
            """;

    public static String InterfaceLLM= """
            public interface AuthService {
            
                void startAuthServer();
                void stopAuthServer();
                boolean isAuthenticated();
                String getCurrentUser();
            }
            """;

    public static String StreamTestCodeOriginal = """
            public class Demo{
                @Override
                protected void configure(HttpSecurity http) throws Exception {
                        http
                        .authorizeRequests()
                            .antMatchers("/wapilot/user/info").authenticated()
                            .anyRequest().permitAll()
                        .and()
                        .exceptionHandling()
                            .authenticationEntryPoint(casAuthenticationEntryPoint());
                }          
            }  
            """;

    public static String StreamTestCodeLLM = """
            public class Demo{
                @Override
                protected void configure(HttpSecurity http) throws Exception {
                        http
                        .authorizeRequests()
                            .antMatchers("/wapilot/user/info").authenticated()
                            .anyRequest().permitAll()
                        .and()
                        .exceptionHandling()
                            .authenticationEntryPoint(casAuthenticationEntryPoint())
                        .and()
                        .addFilter(casAuthenticationFilter());
                }          
            }  
            """;

    public static String ProducerControllerOriginal = """
        package com.wacai.hermes.proxy.controller;
        
        import com.google.inject.Inject;
        import com.wacai.hermes.async.ActionListener;
        import com.wacai.hermes.core.rest.RestController;
        import com.wacai.hermes.core.rest.handler.BaseRestHandler;
        import com.wacai.hermes.core.util.StopWatch;
        import com.wacai.hermes.errors.Errors;
        import com.wacai.hermes.message.BatchMessageMeta;
        import com.wacai.hermes.message.HermesMessage;
        import com.wacai.hermes.message.MessageMeta;
        import com.wacai.hermes.proxy.client.Client;
        import com.wacai.hermes.rest.RequestMapping;
        import com.wacai.hermes.rest.RestChannel;
        import com.wacai.hermes.rest.RestRequest;
        import com.wacai.hermes.rest.RestResponse;
        import com.wacai.hermes.runtime.ISettings;
        import com.wacai.hermes.util.Assert;
        import lombok.extern.slf4j.Slf4j;
        import org.apache.commons.lang3.StringUtils;
        
        import java.io.UnsupportedEncodingException;
        import java.util.HashMap;
        import java.util.List;
        import java.util.Map;
        
        import static com.wacai.hermes.rest.RestRequest.Method.POST;
        
        
        /**
         * <AUTHOR>
         * @description
         **/
        @Slf4j
        public class ProducerController extends BaseRestHandler {
        
            private final long messageMaxSize;
        
            @Inject
            private Client client;
        
            @Inject
            public ProducerController(RestController controller, ISettings settings) {
                super(controller);
                //消息限制默认3MB
                messageMaxSize = settings.getLong(ISettings.PROXY_MESSAGE_MAX_SIZE, 3145728l);
            }
        
            /**
             * @param restRequest
             * @param restChannel
             */
            @RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
            public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
                String topic = restRequest.param("topic");
                String ack = restRequest.param("ack", "1");
                long timeout = restRequest.paramAsLong("timeout", 5000l);
                HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
                validateSingleMessage(hermesMessage, topic);
                StopWatch watch = StopWatch.create().start();
                ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
                    log.info("sendSingle, topic {} client {} offset {} cost(ms) {}",
                            topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed());
                });
                client.send(ack, hermesMessage, timeout, actionListener);
            }
        
            private void validateSingleMessage(HermesMessage hermesMessage, String topic) {
                Assert.notNull(hermesMessage, Errors.ILLEGAL_PARAMETER, "发送的消息体不能为空");
                long payLoad = hermesMessage.getPayLoad();
                Assert.isTrue(payLoad > 0, Errors.ILLEGAL_PARAMETER, "消息的key和value不能同时为空");
                Assert.isTrue(payLoad <= messageMaxSize, Errors.ILLEGAL_PARAMETER, "消息长度不能大于" + messageMaxSize + "(key+value),topic:" + hermesMessage.getTopic());
                Assert.isTrue(StringUtils.equals(topic, hermesMessage.getTopic()), Errors.ILLEGAL_PARAMETER, "body中的消息的topic和请求参数中topic不一致");
            }
        
            /**
             * @param restRequest
             * @param restChannel
             */
            @RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
            public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
                String topic = restRequest.param("topic");
                String ack = restRequest.param("ack", "1");
                long timeout = restRequest.paramAsLong("timeout", 5000l);
                String trace = restRequest.param("trace", "off");
                List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);
        
                validateBatchMessages(messages, topic);
                StopWatch watch = StopWatch.create().start();
                ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
                    log.info("sendBatch topic {} client {} offset {} cost(ms) {}",
                            topic, restChannel.getClientIp(), r.getMessageMetaList().get(0).getOffset(), watch.stop().elapsed());
                });
                if (StringUtils.equalsIgnoreCase("on", trace)) {
                    client.sendBatch(ack, messages, timeout, actionListener);
                } else {
                    client.sendBatchByOneFlush(ack, messages, timeout, actionListener);
                }
            }
        
        
            private void validateBatchMessages(List<HermesMessage> hermesMessages, String topic) {
                Assert.isTrue(hermesMessages.size() > 0, Errors.ILLEGAL_PARAMETER, "批量发送至少包含一条消息");
                long batchPayLoad = 0;
                Map topicMap = new HashMap();
                for (HermesMessage hermesMessage : hermesMessages) {
                    Assert.isTrue(hermesMessage.getPayLoad() > 0, Errors.ILLEGAL_PARAMETER, "批量消息中每条消息的key和value不能同时为空");
                    Assert.isTrue(hermesMessage.getPayLoad() <= messageMaxSize, Errors.ILLEGAL_PARAMETER, "单条消息长度不能大于" + messageMaxSize + "(key+value),topic:" + topic);
                    batchPayLoad += hermesMessage.getPayLoad();
                    topicMap.put(hermesMessage.getTopic(), hermesMessage.getTopic());
                }
        
                Assert.isTrue(topicMap.size() == 1, Errors.ILLEGAL_PARAMETER, "批量消息发送只能是相同topic");
                Assert.isTrue(StringUtils.equals(topic, hermesMessages.get(0).getTopic()), Errors.ILLEGAL_PARAMETER, "body中的消息的topic和请求参数中topic不一致");
        
            }
        
            @RequestMapping(value = {"/kafka/publish", "/kafka-proxy/kafka/publish"}, method = POST)
            public void publish(RestRequest restRequest, RestChannel restChannel) throws UnsupportedEncodingException {
        
                String topic = parseTopic(restRequest);
                final String msgKey = restRequest.param("message_key");
                final String msgVal = restRequest.param("message");
                Assert.isTrue(StringUtils.isNotEmpty(msgVal), Errors.ILLEGAL_PARAMETER, "message cannot be empty");
                HermesMessage hermesMessage = new HermesMessage();
                hermesMessage.setTopic(topic);
                if (StringUtils.isNotBlank(msgKey)) {
                    hermesMessage.setKey(msgKey.getBytes("UTF-8"));
                }
                hermesMessage.setData(msgVal.getBytes("UTF-8"));
                StopWatch watch = StopWatch.create().start();
                ActionListener<MessageMeta> actionListener = ActionListener.wrap((ret, e) -> {
                    if (ret != null) {
                        log.info("publish, topic {} client {} offset {} cost(ms) {}",
                                topic, restChannel.getClientIp(), ret.getOffset(), watch.stop().elapsed());
                    }else{
                        log.error("publish error",e);
                    }
                    //兼容老版本kafka-http-client，这里只返回ok
                    restChannel.sendResponse(RestResponse.text("ok"));
                });
                client.send("1", hermesMessage, 20000, actionListener);
            }
        
            private String parseTopic(RestRequest request) {
                String topic = request.param("topic");
                if (topic == null || topic.isEmpty()) {
                    StringBuilder errMsg = new StringBuilder("topic not specified. from ");
                    String xff = request.header("X-Forwarded-For");
                    if (xff != null && !xff.isEmpty()) {
                        errMsg.append(" X-Forwarded-For: ").append(xff);
                    }
                    String clientId = request.header("Client-Id");
                    if (clientId != null && !clientId.isEmpty()) {
                        errMsg.append(" Client-Id: ").append(clientId);
                    }
                    log.error(errMsg.toString());
                }
                return topic;
            }
        } 
    """;

    public static String ProducerControllerLLM = """
         @RequestMapping(value = "/hermes-proxy/sendSingle", method = POST)
         public void sendSingle(RestRequest restRequest, RestChannel restChannel) {
             String topic = restRequest.param("topic");
             String ack = restRequest.param("ack", "1");
             long timeout = restRequest.paramAsLong("timeout", 5000l);
             HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
             Map<String, String> headers = restRequest.contentAsMap(); // 从请求中提取 headers
             hermesMessage.setHeaders(headers); // 设置 headers
             validateSingleMessage(hermesMessage, topic);
             StopWatch watch = StopWatch.create().start();
             ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
                 log.info("sendSingle, topic {} client {} offset {} cost(ms) {}",
                         topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed());
             });
             client.send(ack, hermesMessage, timeout, actionListener);
         }
    
         @RequestMapping(value = "/hermes-proxy/sendBatch", method = POST)
         public void sendBatch(RestRequest restRequest, RestChannel restChannel) {
             String topic = restRequest.param("topic");
             String ack = restRequest.param("ack", "1");
             long timeout = restRequest.paramAsLong("timeout", 5000l);
             String trace = restRequest.param("trace", "off");
             List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);
             Map<String, String> headers = restRequest.contentAsMap(); // 从请求中提取 headers
             messages.forEach(message -> message.setHeaders(headers)); // 设置 headers
             validateBatchMessages(messages, topic);
             StopWatch watch = StopWatch.create().start();
             ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
                 log.info("sendBatch topic {} client {} offset {} cost(ms) {}",
                         topic, restChannel.getClientIp(), r.getMessageMetaList().get(0).getOffset(), watch.stop().elapsed());
             });
             if (StringUtils.equalsIgnoreCase("on", trace)) {
                 client.sendBatch(ack, messages, timeout, actionListener);
             } else {
                 client.sendBatchByOneFlush(ack, messages, timeout, actionListener);
             }
         }
    
         @RequestMapping(value = {"/kafka/publish", "/kafka-proxy/kafka/publish"}, method = POST)
         public void publish(RestRequest restRequest, RestChannel restChannel) throws UnsupportedEncodingException {
             String topic = parseTopic(restRequest);
             final String msgKey = restRequest.param("message_key");
             final String msgVal = restRequest.param("message");
             Assert.isTrue(StringUtils.isNotEmpty(msgVal), Errors.ILLEGAL_PARAMETER, "message cannot be empty");
             HermesMessage hermesMessage = new HermesMessage();
             hermesMessage.setTopic(topic);
             if (StringUtils.isNotBlank(msgKey)) {
                 hermesMessage.setKey(msgKey.getBytes("UTF-8"));
             }
             hermesMessage.setData(msgVal.getBytes("UTF-8"));
             Map<String, String> headers = restRequest.contentAsMap(); // 从请求中提取 headers
             hermesMessage.setHeaders(headers); // 设置 headers
             StopWatch watch = StopWatch.create().start();
             ActionListener<MessageMeta> actionListener = ActionListener.wrap((ret, e) -> {
                 if (ret != null) {
                     log.info("publish, topic {} client {} offset {} cost(ms) {}",
                             topic, restChannel.getClientIp(), ret.getOffset(), watch.stop().elapsed());
                 }else{
                     log.error("publish error",e);
                 }
                 restChannel.sendResponse(RestResponse.text("ok"));
             });
             client.send("1", hermesMessage, 20000, actionListener);
         }
    """;

    public static String hermesMessageOriginal = """
            package com.wacai.hermes.message;
            import com.wacai.hermes.util.StringUtils;
            import lombok.Getter;
            import lombok.Setter;
            import java.util.Arrays;
            import java.util.HashMap;
            import java.util.Map;
            import java.util.Objects;
            import java.util.UUID;
            
            @Getter
            @Setter
            public class HermesMessage {
            
                public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
                    this.topic = topic;
                    this.partition = partition;
                    this.key = key;
                    this.data = data;
                    this.timestamp = timestamp;
                }
            
                public static Builder builder() {
                    return new Builder();
                }
            
                public long getPayLoad() {
                    long size = 0;
                    if (key != null) {
                        size += key.length;
                    }
                    if (data != null) {
                        size += data.length;
                    }
                    return size;
                }
            
                public String getTraceId() {
                    return null;
                }
            
                @Override
                public boolean equals(Object o) {
                    if (this == o) {
                        return true;
                    }
                    if (o == null || getClass() != o.getClass()) {
                        return false;
                    }
                    HermesMessage message = (HermesMessage)o;
                    return Objects.equals(partition, message.partition) && timestamp == message.timestamp
                            && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
                            messageId, message.messageId);
                }
            
                @Override
                public int hashCode() {
            
                    int result = Objects.hash( topic, partition, timestamp, messageId);
                    result = 31 * result + Arrays.hashCode(key);
                    result = 31 * result + Arrays.hashCode(data);
                    return result;
                }
            
                @Override
                public String toString() {
                    return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
                }
            
                public static class Result {
                    private String topic;
                }
                
                public static class Builder {
                    private String topic;
                    private Integer partition;
                    private byte[] key;
                    private byte[] data;
                    private Map<String, byte[]> headers = new HashMap<>();
                    private long timestamp;
            
                    public Builder setHeaders(Map<String, byte[]> headers) {
                        // headers.entrySet().removeIf(entry -> entry.getKey().startsWith("hermes."));
                        this.headers.putAll(headers);
                        return this;
                    }
            
                    public Builder setTopic(String topic) {
                        this.topic = topic;
                        return this;
                    }
            
                    public Builder setPartition(Integer partition) {
                        this.partition = partition;
                        return this;
                    }
            
                    public Builder setKey(byte[] key) {
                        this.key = key;
                        return this;
                    }
            
                    public Builder setData(byte[] data) {
                        this.data = data;
                        return this;
                    }
            
                    public Builder setTimestamp(long timestamp) {
                        this.timestamp = timestamp;
                        return this;
                    }
            
                    public Builder setTag(String tag) {
                        if (null != tag && !"".equals(tag = tag.trim())) {
                            this.headers.put(TAG, tag.getBytes());
                        }
                        return this;
                    }
            
                    public HermesMessage build() {
                        return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
                    }
                }
            }            
            """;
    public static String hermesMessageLLM = """
            @Getter
            @Setter
            public class HermesMessage {
                private String topic;
                private byte[] key;
                private byte[] data;
                private int partition;
                @Getter 
                @Setter
                private Map<String, String> headers;
                
                public HermesMessage( String topic) {
                    this.topic = topic;
 
                }
                public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
                    this.topic = topic;
 
                }
                
                // ... existing methods ...
                
                public long getPayLoad() {
                    long size = 0;
                    return size;
                }
                    
                @Override
                public int hashCode() {
            
                    int result = Objects.hash( topic,
                                partition, 
                                timestamp, 
                                messageId, 
                                headers);
                                
                    result = 31 * result + Arrays.hashCode(key);
                    result = 31 * result + Arrays.hashCode(data);
                    result = 31 * result + Arrays.hashCode(headers);
                    
                    return result;
                }
                
                public Builder addHeader(String key, byte[] value) {
                    this.headers.put(key, value);
                    return this;
                }
            
                public Builder addHeader(String key, String value) {
                    this.headers.put(key, value.getBytes(StandardCharsets.UTF_8));
                    return this;
                }
                
                 public static class Result {
                    private String topic;
                    private String partition;
                }
                
                public static class Builder {
                    // ... existing fields ...
            
                    private Map<String, byte[]> headers = new HashMap<>();
            
                    // ... existing methods ...
            
                    public Builder addHeader(String key, byte[] value) {
                        this.headers.put(key, value);
                        return this;
                    }
            
                    public Builder addHeader(String key, String value) {
                        this.headers.put(key, value.getBytes(StandardCharsets.UTF_8));
                        return this;
                    }
            
                    public HermesMessage build() {
                        HermesMessage message = new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
                        message.setHeaders(this.headers);
                        return message;
                    }
                }
            }
            """;


    public  static final String StoppedPanel_Original = """
            public class StoppedPanel extends JPanel {
            
            }            
            """;

    public  static final String StoppedPanel_LLM = """
            public class StoppedPanel extends JPanel {
                private final JLabel iconLabel;
                private final JLabel textLabel;
            
                public StoppedPanel() {
                    setLayout(new BorderLayout());
                    setOpaque(false);
                   
                    // Create icon label
                    iconLabel = new JLabel(WapilotIcons.StopGray);
                    iconLabel.setHorizontalAlignment(SwingConstants.CENTER);
                    
                    // Create text label
                    textLabel = new JLabel("已停止");
                    textLabel.setHorizontalAlignment(SwingConstants.CENTER);
                    textLabel.setFont(SwingUtil.getDefaultFont().deriveFont(Font.BOLD, 14));
                   
                    // Add components to panel
                    add(iconLabel, BorderLayout.CENTER);
                    add(textLabel, BorderLayout.SOUTH);
                    
                    // Add some padding
                    setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
                }
            
                public void setText(String text) {
                    textLabel.setText(text);
                }
            }          
            """;

    public static void main(String[] args) throws IOException {

        CompilationUnit originalCU = StaticJavaParser.parse(Code.hermesMessageOriginal);
        System.out.println(originalCU.getType(0).getNameAsString());


//        Path projectRoot = Paths.get("").toAbsolutePath();
//        Path dataDir = projectRoot.resolve("test/data");
//        String code = new String(java.nio.file.Files.readAllBytes(dataDir.resolve("KafkaProducerWrapper.java")));


//        ClassOrInterfaceDeclaration clazz = llmCU.getClassByName("HermesMessage").orElseThrow(() -> new RuntimeException("类未找到"));
//
//        // 创建一个新方法
//        MethodDeclaration newMethod = clazz.addMethod("newMethod", Modifier.Keyword.PUBLIC);
//        newMethod.setType("void");
//        newMethod.setBody(StaticJavaParser.parseBlock("{ System.out.println(\"新方法执行\"); }"));
//
//        System.out.println(llmCU);

    }

}
