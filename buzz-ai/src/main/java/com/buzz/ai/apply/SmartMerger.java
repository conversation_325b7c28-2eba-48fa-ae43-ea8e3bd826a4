package com.buzz.ai.apply;

import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.*;
import com.github.javaparser.printer.lexicalpreservation.LexicalPreservingPrinter;
import lombok.Data;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class SmartMerger {
    private static final Pattern PLACEHOLDER_COMMENT =
            Pattern.compile("^\\s*//\\s*\\.\\.\\..*?\\.\\.\\.\\s*$", Pattern.MULTILINE);

    private String originalCode;
    private CompilationUnit originalCU;
    private ClassOrInterfaceDeclaration originalNode;
    private int indentSize;
    private String currentResult;
    private boolean replace; //是否替换

    @Data
    public static class CodeDiff {
        ClassOrInterfaceDeclaration type;
        List<FieldAddition> fieldAdditions = new ArrayList<>();
        List<MethodAddition> methodAdditions = new ArrayList<>();
        List<MethodUpdate> methodUpdates = new ArrayList<>();
        List<FieldUpdate> fieldUpdates = new ArrayList<>();
        List<CodeDiff> innerCodeDiffs = new ArrayList<>();
        // Add new lists for deletions
        List<FieldDeletion> fieldDeletions = new ArrayList<>();
        List<MethodDeletion> methodDeletions = new ArrayList<>();
        // Add new lists for constructors
        List<ConstructorAddition> constructorAdditions = new ArrayList<>();
        List<ConstructorUpdate> constructorUpdates = new ArrayList<>();
        List<ConstructorDeletion> constructorDeletions = new ArrayList<>();
    }

    @Data
    public static class FieldAddition {
        String name;
        String declaration;
    }

    @Data
    public static class MethodAddition {
        String signature;
        String declaration;
    }

    @Data
    public static class MethodUpdate {
        String signature;
        String newBody;
        int bodyStartLine;
        int bodyEndLine;
    }

    @Data
    public static class FieldUpdate {
        String name;
        String newDeclaration;
        int startLine;
        int endLine;
    }

    @Data
    public static class FieldDeletion {
        String name;
        int startLine;
        int endLine;
    }

    @Data
    public static class MethodDeletion {
        String signature;
        int startLine;
        int endLine;
    }

    @Data
    public static class ConstructorAddition {
        String signature;
        String declaration;
    }

    @Data
    public static class ConstructorUpdate {
        String signature;
        String newBody;
        int bodyStartLine;
        int bodyEndLine;
    }

    @Data
    public static class ConstructorDeletion {
        String signature;
        int startLine;
        int endLine;
    }

    private boolean isFullClass(String llmCode) {
        return llmCode.indexOf(" class ") != -1 || llmCode.indexOf(" interface ") != -1;
    }

    public String merge(String originalCode, String llmCode) {
        try {
            if (isFullClass(llmCode)) {
                replace = !isLazyText(llmCode);
                return fullMerge(originalCode, llmCode);
            } else {
                return partialMerge(originalCode, llmCode);
            }


        } catch (Exception e) {
            throw new IllegalStateException("merge failed! llmCode=" + llmCode, e);
        }
    }

    private String partialMerge(String originalCode, String llmCode) {
        try {
            CompilationUnit originalCU = StaticJavaParser.parse(originalCode);
            ClassOrInterfaceDeclaration originalNode = (ClassOrInterfaceDeclaration) originalCU.getType(0);
            String className = originalNode.getNameAsString();

            // 构建完整的临时代码，包装llmCode成一个完整类
            StringBuilder tempCode = new StringBuilder();
            // 保留原始包名
            if (originalCU.getPackageDeclaration().isPresent()) {
                tempCode.append(originalCU.getPackageDeclaration().get());
            }

            // 保留原始导入
            originalCU.getImports().forEach(imp -> tempCode.append(imp.toString()));
            // 创建临时类
            tempCode.append("public class ").append(className).append(" {\n");

            String[] lines = llmCode.split("\n");
            for (String line : lines) {
                //去除package和import语句
                if (line.startsWith("package") || line.startsWith("import")) {
                    continue;
                }
                tempCode.append(line).append("\n");
            }
            tempCode.append("\n}\n");

            // 解析临时代码
            CompilationUnit tempCU = LexicalPreservingPrinter.setup(StaticJavaParser.parse(tempCode.toString()));
            ClassOrInterfaceDeclaration tempNode = (ClassOrInterfaceDeclaration) tempCU.getType(0);

            return doFullMerge(originalCode, originalCU, originalNode, tempNode, 1);
        } catch (Exception e) {
            throw new IllegalStateException("partialMerge failed! llmCode=" + llmCode, e);
        }
    }

    private String fullMerge(String originalCode, String llmCode) {
        CompilationUnit originalCU = StaticJavaParser.parse(originalCode);
        ClassOrInterfaceDeclaration originalNode = (ClassOrInterfaceDeclaration) originalCU.getType(0);
        ClassOrInterfaceDeclaration llmNode = (ClassOrInterfaceDeclaration) LexicalPreservingPrinter.setup(StaticJavaParser.parse(llmCode)).getType(0);
        return doFullMerge(originalCode, originalCU, originalNode, llmNode, 1);
    }

    private boolean isLazyText(String llmCode) {
        return PLACEHOLDER_COMMENT.matcher(llmCode).find();
    }

    private String doFullMerge(String originalCode,
                               CompilationUnit originalCU,
                               ClassOrInterfaceDeclaration originalNode,
                               ClassOrInterfaceDeclaration llmNode,
                               int indentSize) {
        this.originalCode = originalCode;
        this.originalCU = originalCU;
        this.originalNode = originalNode;
        this.indentSize = indentSize;

        // 分析差异
        CodeDiff diff = analyzeDiff(originalNode, llmNode);
        //应用
        currentResult = applyDiff(diff);
        //处理内部类
        handleInnerClasses(llmNode);
        return currentResult;
    }

    private void handleInnerClasses(ClassOrInterfaceDeclaration llmNode) {

        final List<ClassOrInterfaceDeclaration> llmInnerClasses = llmNode.getMembers().stream()
                .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                .map(m -> (ClassOrInterfaceDeclaration) m)
                .collect(Collectors.toList());

        if (llmInnerClasses.isEmpty()) {
            return;
        }

        CompilationUnit resultCU = StaticJavaParser.parse(currentResult);
        ClassOrInterfaceDeclaration resultNode = (ClassOrInterfaceDeclaration) resultCU.getType(0);
        List<ClassOrInterfaceDeclaration> resultInnerClasses = resultNode.getMembers().stream()
                .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                .map(m -> (ClassOrInterfaceDeclaration) m)
                .collect(Collectors.toList());


        // For each llm inner class
        for (ClassOrInterfaceDeclaration llmInnerClass : llmInnerClasses) {
            String innerClassName = llmInnerClass.getNameAsString();

            // Find the corresponding result inner class
            Optional<ClassOrInterfaceDeclaration> resultInnerClass = resultInnerClasses.stream()
                    .filter(c -> c.getNameAsString().equals(innerClassName))
                    .findFirst();

            //The result inner class maybe not existed
            if (resultInnerClass.isPresent()) {
                // Recursively merge the inner classes
                doFullMerge(currentResult, resultCU, resultInnerClass.get(), llmInnerClass, indentSize + 1);

                //更新结果
                resultCU = StaticJavaParser.parse(currentResult);
                resultNode = (ClassOrInterfaceDeclaration) resultCU.getType(0);
                resultInnerClasses = resultNode.getMembers().stream()
                        .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                        .map(m -> (ClassOrInterfaceDeclaration) m)
                        .collect(Collectors.toList());
            }
        }

    }


    private CodeDiff analyzeDiff(ClassOrInterfaceDeclaration originalNode, ClassOrInterfaceDeclaration llmNode) {
        // 收集原始代码中的字段和方法
        Map<String, FieldDeclaration> originalFields = new LinkedHashMap<>();
        Map<String, MethodDeclaration> originalMethods = new LinkedHashMap<>();
        Map<String, ConstructorDeclaration> originalConstructors = new LinkedHashMap<>();
        collectMembers(originalNode, originalFields, originalMethods, originalConstructors);

        // 收集LLM代码中的字段和方法
        Map<String, FieldDeclaration> llmFields = new LinkedHashMap<>();
        Map<String, MethodDeclaration> llmMethods = new LinkedHashMap<>();
        Map<String, ConstructorDeclaration> llmConstructors = new LinkedHashMap<>();
        collectMembers(llmNode, llmFields, llmMethods, llmConstructors);

        // 计算差异
        CodeDiff diff = new CodeDiff();

        // 处理字段差异
        for (String fieldName : llmFields.keySet()) {
            // 找出新增的字段
            if (!originalFields.containsKey(fieldName)) {
                FieldAddition addition = new FieldAddition();
                addition.setName(fieldName);
                addition.setDeclaration(removeComment(llmFields.get(fieldName).toString()));
                diff.fieldAdditions.add(addition);
            } else {
                // 检查字段是否更新
                FieldDeclaration originalField = originalFields.get(fieldName);
                FieldDeclaration llmField = llmFields.get(fieldName);

                if (!originalField.toString().equals(removeComment(llmField.toString()))) {
                    FieldUpdate update = new FieldUpdate();
                    update.setName(fieldName);
                    update.setNewDeclaration(removeComment(llmField.toString()));
                    // 保存原始字段的位置信息
                    update.setStartLine(originalField.getBegin().get().line);
                    update.setEndLine(originalField.getEnd().get().line);
                    diff.fieldUpdates.add(update);
                }
            }
        }

        // 检查删除的字段
        if (replace) {
            for (String fieldName : originalFields.keySet()) {
                if (!llmFields.containsKey(fieldName)) {
                    FieldDeclaration originalField = originalFields.get(fieldName);
                    FieldDeletion deletion = new FieldDeletion();
                    deletion.setName(fieldName);
                    deletion.setStartLine(originalField.getBegin().get().line);
                    deletion.setEndLine(originalField.getEnd().get().line);
                    diff.fieldDeletions.add(deletion);
                }
            }
        }

        // 处理方法差异
        for (String methodSig : llmMethods.keySet()) {
            // 找出新增的方法
            if (!originalMethods.containsKey(methodSig)) {
                MethodAddition addition = new MethodAddition();
                addition.setSignature(methodSig);
                addition.setDeclaration(removeComment(llmMethods.get(methodSig).toString()));
                diff.methodAdditions.add(addition);
            } else {
                MethodDeclaration originalMethod = originalMethods.get(methodSig);
                MethodDeclaration llmMethod = llmMethods.get(methodSig);

                // 比较方法体
                if (!originalMethod.getBody().equals(llmMethod.getBody())) {
                    MethodUpdate update = new MethodUpdate();
                    update.setSignature(methodSig);
                    String methodBody =LexicalPreservingPrinter.print(llmMethod.getBody().get());
                    update.setNewBody(removeComment(methodBody));
                    // 保存原始方法体的位置信息
                    if (originalMethod.getBody().isPresent()) {
                        update.setBodyStartLine(originalMethod.getBody().get().getBegin().get().line);
                        update.setBodyEndLine(originalMethod.getBody().get().getEnd().get().line);
                    }
                    diff.methodUpdates.add(update);
                }
            }
        }

        // 处理构造函数差异
        for (String constructorSig : llmConstructors.keySet()) {
            // 找出新增的构造函数
            if (!originalConstructors.containsKey(constructorSig)) {
                ConstructorDeclaration constructorDeclaration = llmConstructors.get(constructorSig);
                ConstructorAddition addition = new ConstructorAddition();
                addition.setSignature(constructorSig);
                addition.setDeclaration(removeComment(constructorDeclaration.toString()));
                diff.constructorAdditions.add(addition);
            } else {
                ConstructorDeclaration originalConstructor = originalConstructors.get(constructorSig);
                ConstructorDeclaration llmConstructor = llmConstructors.get(constructorSig);

                // 比较构造函数体
                if (!originalConstructor.getBody().equals(llmConstructor.getBody())) {
                    ConstructorUpdate update = new ConstructorUpdate();
                    update.setSignature(constructorSig);
                    update.setNewBody(removeComment(llmConstructor.getBody().toString()));
                    // 保存原始构造函数体的位置信息
                    update.setBodyStartLine(originalConstructor.getBody().getBegin().get().line);
                    update.setBodyEndLine(originalConstructor.getBody().getEnd().get().line);
                    diff.constructorUpdates.add(update);
                }
            }
        }

        // 检查删除的方法
        if (replace) {
            for (String methodSig : originalMethods.keySet()) {
                if (!llmMethods.containsKey(methodSig)) {
                    MethodDeclaration originalMethod = originalMethods.get(methodSig);
                    MethodDeletion deletion = new MethodDeletion();
                    deletion.setSignature(methodSig);
                    deletion.setStartLine(originalMethod.getBegin().get().line);
                    deletion.setEndLine(originalMethod.getEnd().get().line);
                    diff.methodDeletions.add(deletion);
                }
            }
        }

        return diff;
    }


    private static String removeComment(String source) {
        Matcher matcher = PLACEHOLDER_COMMENT.matcher(source);
        String result = matcher.replaceAll("").trim();
        return result;
    }

    private void collectMembers(ClassOrInterfaceDeclaration node,
                                Map<String, FieldDeclaration> fields,
                                Map<String, MethodDeclaration> methods,
                                Map<String, ConstructorDeclaration> constructors) {

        // 收集字段
        node.getFields().forEach(field -> {
            field.getVariables().forEach(var -> {
                fields.put(var.getNameAsString(), field);
            });
        });

        // 收集方法
        node.getMethods().forEach(method -> {
            String signature = getMethodSignature(method);
            methods.put(signature, method);
        });


        // 收集构造函数
        node.getConstructors().forEach(constructor -> {
            String signature = getConstructorSignature(constructor);
            constructors.put(signature, constructor);
        });

    }

    private String getConstructorSignature(ConstructorDeclaration constructor) {
        StringBuilder sb = new StringBuilder();
        sb.append(constructor.getNameAsString()).append("(");

        constructor.getParameters().forEach(param -> {
            sb.append(param.getType().asString()).append(",");
        });

        if (constructor.getParameters().size() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        sb.append(")");
        return sb.toString();
    }

    private String getMethodSignature(MethodDeclaration method) {
        StringBuilder sb = new StringBuilder();
        sb.append(method.getNameAsString()).append("(");

        method.getParameters().forEach(param -> {
            sb.append(param.getType().asString()).append(",");
        });

        if (method.getParameters().size() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        sb.append(")");
        return sb.toString();
    }


    //注意：必须先处理更新后处理新增，否则更新的位置会错位
    private String applyDiff(CodeDiff diff) {
        StringBuilder result = new StringBuilder(originalCode);
        int offset = 0; // 用于跟踪由于插入/修改导致的位置偏移

        // 应用字段更新
        for (FieldUpdate update : diff.fieldUpdates) {
            // 使用保存的位置信息找到字段在原始代码中的位置
            int startPos = findPositionInCode(update.getStartLine() - 1);//前一行
            int endPos = findPositionInCode(update.getEndLine());

            // 获取适当的缩进
            String indentation = getIndentation(originalCode, indentSize);
            String fieldText = applyFieldIndentation(update.getNewDeclaration(), indentation) + "\n";

            if (startPos >= 0 && endPos >= 0) {
                result.replace(startPos + offset, endPos + offset, fieldText);
                offset += fieldText.length() - (endPos - startPos);
            }
        }

        // 应用字段添加
        int lastFieldPos = findLastFieldPosition();
        for (FieldAddition field : diff.fieldAdditions) {
            // 获取适当的缩进
            String indentation = getIndentation(originalCode, indentSize);
            String fieldText = applyFieldIndentation(field.getDeclaration(), indentation) + "\n";

            if (lastFieldPos == -1) {//如果没有field
                lastFieldPos = originalCode.indexOf("{") + 1;
                fieldText = "\n" + fieldText;
            }
            result.insert(lastFieldPos + offset, fieldText);
            offset += fieldText.length();
        }

        // 构造函数更新
        for (ConstructorUpdate update : diff.constructorUpdates) {
            // 使用保存的位置信息找到构造函数体在原始代码中的位置
            int bodyStart = findPositionInCode(update.getBodyStartLine());
            int bodyEnd = findPositionInCode(update.getBodyEndLine());

            if (bodyStart >= 0 && bodyEnd >= 0) {
                // 获取构造函数体的缩进级别
                String constructorIndentation = getIndentation(originalCode, indentSize);

                // 处理构造函数体的缩进
                String newBody = update.getNewBody();
                String indentedBody = applyMethodIndentation(newBody, constructorIndentation, true);

                // 替换构造函数体
                result.replace(bodyStart + offset, bodyEnd + offset, indentedBody);
                offset += indentedBody.length() - (bodyEnd - bodyStart);
            }
        }

        // 构造函数添加
        int lastConstructorPos = findLastConstructorPosition();
        if (lastConstructorPos == -1) {
            lastConstructorPos = originalCode.indexOf("{") + 1;
        }
        for (ConstructorAddition constructor : diff.constructorAdditions) {
            // 获取适当的缩进
            String indentation = getIndentation(originalCode, indentSize);

            // 处理构造函数体的缩进
            String constructorDeclaration = constructor.getDeclaration();
            String indentedConstructor = applyMethodIndentation(constructorDeclaration, indentation, false);

            // 添加构造函数
            String constructorText = "\n" + indentedConstructor;
            result.insert(lastConstructorPos + offset, constructorText);
            offset += constructorText.length();
        }


        // 应用方法更新
        for (MethodUpdate update : diff.methodUpdates) {
            // 使用保存的位置信息找到方法体在原始代码中的位置
            int bodyStart = findPositionInCode(update.getBodyStartLine());
            int bodyEnd = findPositionInCode(update.getBodyEndLine());

            if (bodyStart >= 0 && bodyEnd >= 0) {
                // 获取方法体的缩进级别
                String methodIndentation = getIndentation(originalCode, indentSize);

                // 处理方法体的缩进
                String newBody = update.getNewBody();
                String indentedBody = applyMethodIndentation(newBody, methodIndentation, true);
                String methodText = indentedBody;
                // 替换方法体
                result.replace(bodyStart + offset, bodyEnd + offset, methodText);
                offset += methodText.length() - (bodyEnd - bodyStart);
            }
        }

        // 应用方法添加
        int lastMethodPos = findLastMethodPosition();
        if (lastMethodPos == -1) {
            lastMethodPos = originalCode.indexOf("{") + 1;
        }
        for (MethodAddition method : diff.methodAdditions) {
            // 获取适当的缩进
            String indentation = getIndentation(originalCode, indentSize);

            // 处理方法体的缩进
            String methodDeclaration = method.getDeclaration();
            String indentedMethod = applyMethodIndentation(methodDeclaration, indentation, false);

            // 替换方法体
            String methodText = "\n" + indentedMethod;
            result.insert(lastMethodPos + offset, methodText);
            offset += methodText.length();
        }

        return result.toString();
    }

    // 找到最后一个字段的位置
    private int findLastFieldPosition() {
        List<FieldDeclaration> fields = originalNode.getFields();
        if (!fields.isEmpty()) {
            FieldDeclaration lastField = fields.get(fields.size() - 1);
            return findPositionInCode(lastField.getEnd().get().line);
        }
        // 如果没有字段返回-1
        return -1;
    }

    // 找到最后一个方法的位置
    private int findLastMethodPosition() {
        List<MethodDeclaration> methods = originalNode.getMethods();
        if (!methods.isEmpty()) {
            MethodDeclaration lastMethod = methods.get(methods.size() - 1);
            return findPositionInCode(lastMethod.getEnd().get().line);
        }
        // 如果没有方法，返回最后一个字段后的位置或类声明后的位置
        return findLastFieldPosition();
    }

    /**
     * 根据行号找到在代码中的实际位置
     * @param lineNumber
     * @return
     */
    private int findPositionInCode(int lineNumber) {
        String[] lines = originalCode.split("\n", -1);
        int position = 0;

        for (int i = 0; i < Math.min(lineNumber, lines.length); i++) {
            position += lines[i].length() + 1; // +1 for the newline character
        }

        return position;
    }


    /**
     * 找到最后一个构造函数的位置
     * @return
     */
    private int findLastConstructorPosition() {
        List<ConstructorDeclaration> constructors = originalNode.getConstructors();
        if (!constructors.isEmpty()) {
            ConstructorDeclaration lastConstructor = constructors.get(constructors.size() - 1);
            return findPositionInCode(lastConstructor.getEnd().get().line);
        }
        // 如果没有构造函数查找lastField
        return findLastFieldPosition();
    }

    /**
     * 获取代码的基本缩进
     * @param code
     * @param indentSize
     * @return
     */
    private String getIndentation(String code, int indentSize) {
        // 查找类中的第一个成员（字段或方法）的缩进
        String[] lines = code.split("\n");
        int classFound = 0;

        for (String line : lines) {
            String trimmedLine = line.trim();

            // 先找到类声明
            if (classFound < indentSize && trimmedLine.indexOf(" class ") != -1) {
                ++classFound;
                continue;
            }

            // 在找到类声明后，查找第一个成员
            if (classFound >= indentSize && (trimmedLine.startsWith("private ") ||
                    trimmedLine.startsWith("protected ") ||
                    trimmedLine.startsWith("public ") ||
                    trimmedLine.startsWith("static "))) {

                int leadingSpaces = line.indexOf(trimmedLine);
                if (leadingSpaces > 0) {
                    return line.substring(0, leadingSpaces);
                }
            }
        }

        // 如果没有找到合适的缩进，尝试从JavaParser获取
        if (!originalCU.getTypes().isEmpty()) {
            TypeDeclaration<?> type = originalCU.getType(0);
            if (type instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration classDecl = (ClassOrInterfaceDeclaration) type;
                if (!classDecl.getMembers().isEmpty()) {
                    // 获取第一个成员的位置
                    int memberLine = classDecl.getMembers().get(0).getBegin().get().line;
                    String memberLineText = lines[memberLine - 1]; // 行号从1开始，数组索引从0开始
                    int leadingSpaces = memberLineText.indexOf(memberLineText.trim());
                    if (leadingSpaces > 0) {
                        return memberLineText.substring(0, leadingSpaces);
                    }
                }
            }
        }

        // 默认缩进为4个空格
        return "    ";
    }

    /**
     * 获取方法体的缩进
     * @param code
     * @param bodyStart
     * @return
     */
    private String getMethodBodyIndentation(String code, int bodyStart) {
        // 找到方法体开始后的第一个非空行的缩进
        String codeAfterBodyStart = code.substring(bodyStart);
        String[] lines = codeAfterBodyStart.split("\n");

        for (int i = 1; i < lines.length; i++) { // 从第二行开始，跳过 {
            String line = lines[i];
            if (!line.trim().isEmpty()) {
                int leadingSpaces = line.indexOf(line.trim());
                if (leadingSpaces > 0) {
                    return line.substring(0, leadingSpaces);
                }
            }
        }

        // 默认方法体缩进为8个空格（类缩进4个空格 + 方法体额外4个空格）
        return "        ";
    }

    /**
     * 应用缩进到多行代码
     *
     * @param code
     * @param indentation
     * @return
     */
    private String applyFieldIndentation(String code, String indentation) {
        String[] lines = code.split("\n");
        StringBuilder indentedCode = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            indentedCode.append(indentation).append(line).append("\n");
        }

        return indentedCode.toString();
    }

    /**
     * 应用缩进到多行代码
     *
     * @param code
     * @param indentation
     * @param isUpdate
     * @return
     */
    private String applyMethodIndentation(String code, String indentation, boolean isUpdate) {
        String[] lines = code.split("\n");
        StringBuilder indentedCode = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            if (isUpdate && i == 0) {//更新跳过第一行{
                continue;
            }
            String line = lines[i];
            indentedCode.append(indentation).append(line).append("\n");
        }

        return indentedCode.toString();
    }

}