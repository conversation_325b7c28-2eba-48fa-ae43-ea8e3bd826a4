package com.buzz.ai.apply;

import com.alibaba.fastjson.JSON;
import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.*;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;
import lombok.Data;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SmartJavaMerger {

    public static void main(String[] args) {
        String result = new SmartJavaMerger().merge(Code.hermesMessageOriginal,Code.hermesMessageLLM);
        System.out.println(result);
    }
    // 存储原始代码和LLM代码的差异
    @Data
    public static class CodeDiff {
        Set<String> addedFields = new HashSet<>();
        Set<String> addedMethods = new HashSet<>();
        Map<String, String> updatedMethods = new HashMap<>();
        Set<String> removedFields = new HashSet<>();
        Set<String> removedMethods = new HashSet<>();
    }
    
    public String merge(String originalCode, String llmCode) {
        try {
            // 使用JavaParser分析差异
            CodeDiff diff = analyzeDiff(originalCode, llmCode);
            System.out.println(JSON.toJSONString(diff));
            // 应用差异到原始代码文本
            return applyDiff(originalCode, diff, llmCode);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    private CodeDiff analyzeDiff(String originalCode, String llmCode) {
        CompilationUnit originalCU = StaticJavaParser.parse(originalCode);
        CompilationUnit llmCU = StaticJavaParser.parse(llmCode);
        
        // 收集原始代码中的字段和方法
        Map<String, FieldDeclaration> originalFields = new HashMap<>();
        Map<String, MethodDeclaration> originalMethods = new HashMap<>();
        collectMembers(originalCU, originalFields, originalMethods);
        
        // 收集LLM代码中的字段和方法
        Map<String, FieldDeclaration> llmFields = new HashMap<>();
        Map<String, MethodDeclaration> llmMethods = new HashMap<>();
        collectMembers(llmCU, llmFields, llmMethods);
        
        // 计算差异
        CodeDiff diff = new CodeDiff();
        
        // 找出新增的字段
        for (String fieldName : llmFields.keySet()) {
            if (!originalFields.containsKey(fieldName)) {
                diff.addedFields.add(fieldName);
            }
        }
        
        // 找出删除的字段
        for (String fieldName : originalFields.keySet()) {
            if (!llmFields.containsKey(fieldName)) {
                diff.removedFields.add(fieldName);
            }
        }
        
        // 找出新增的方法
        for (String methodSig : llmMethods.keySet()) {
            if (!originalMethods.containsKey(methodSig)) {
                diff.addedMethods.add(methodSig);
            }
        }
        
        // 找出删除的方法
        for (String methodSig : originalMethods.keySet()) {
            if (!llmMethods.containsKey(methodSig)) {
                diff.removedMethods.add(methodSig);
            }
        }
        
        // 找出更新的方法
        for (String methodSig : llmMethods.keySet()) {
            if (originalMethods.containsKey(methodSig)) {
                MethodDeclaration originalMethod = originalMethods.get(methodSig);
                MethodDeclaration llmMethod = llmMethods.get(methodSig);
                
                // 比较方法体
                if (!originalMethod.getBody().equals(llmMethod.getBody())) {
                    diff.updatedMethods.put(methodSig, llmMethod.getBody().get().toString());
                }
            }
        }
        
        return diff;
    }
    
    private void collectMembers(CompilationUnit cu, 
                               Map<String, FieldDeclaration> fields, 
                               Map<String, MethodDeclaration> methods) {
        cu.findAll(ClassOrInterfaceDeclaration.class).forEach(classDecl -> {
            // 收集字段
            classDecl.getFields().forEach(field -> {
                field.getVariables().forEach(var -> {
                    fields.put(var.getNameAsString(), field);
                });
            });
            
            // 收集方法
            classDecl.getMethods().forEach(method -> {
                String signature = getMethodSignature(method);
                methods.put(signature, method);
            });
            
            // 处理内部类
            classDecl.findAll(ClassOrInterfaceDeclaration.class).forEach(innerClass -> {
                if (innerClass != classDecl) {
                    collectMembers(innerClass, fields, methods);
                }
            });
        });
    }
    
    private void collectMembers(ClassOrInterfaceDeclaration classDecl, 
                               Map<String, FieldDeclaration> fields, 
                               Map<String, MethodDeclaration> methods) {
        // 收集字段
        classDecl.getFields().forEach(field -> {
            field.getVariables().forEach(var -> {
                fields.put(var.getNameAsString(), field);
            });
        });
        
        // 收集方法
        classDecl.getMethods().forEach(method -> {
            String signature = getMethodSignature(method);
            methods.put(signature, method);
        });
        
        // 处理内部类
        classDecl.findAll(ClassOrInterfaceDeclaration.class).forEach(innerClass -> {
            if (innerClass != classDecl) {
                collectMembers(innerClass, fields, methods);
            }
        });
    }
    
    private String getMethodSignature(MethodDeclaration method) {
        StringBuilder sb = new StringBuilder();
        sb.append(method.getNameAsString()).append("(");
        
        method.getParameters().forEach(param -> {
            sb.append(param.getType().asString()).append(",");
        });
        
        if (method.getParameters().size() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        
        sb.append(")");
        return sb.toString();
    }
    
    private String applyDiff(String originalCode, CodeDiff diff, String llmCode) {
        StringBuilder result = new StringBuilder(originalCode);
        
        // 应用字段添加
        for (String fieldName : diff.addedFields) {
            String fieldDecl = extractFieldDeclaration(llmCode, fieldName);
            if (fieldDecl != null) {
                int insertPos = findFieldInsertPosition(originalCode);
                result.insert(insertPos, "\n    " + fieldDecl + "\n");
            }
        }
        
        // 应用方法添加
        for (String methodSig : diff.addedMethods) {
            String methodDecl = extractMethodDeclaration(llmCode, methodSig);
            if (methodDecl != null) {
                int insertPos = findMethodInsertPosition(originalCode);
                result.insert(insertPos, "\n    " + methodDecl + "\n");
            }
        }
        
        // 应用方法更新
        for (Map.Entry<String, String> entry : diff.updatedMethods.entrySet()) {
            String methodSig = entry.getKey();
            String newBody = entry.getValue();
            
            // 找到方法在原始代码中的位置
            int methodStart = findMethodPosition(originalCode, methodSig);
            if (methodStart >= 0) {
                int bodyStart = findMethodBodyStart(originalCode, methodStart);
                int bodyEnd = findMethodBodyEnd(originalCode, bodyStart);
                
                if (bodyStart >= 0 && bodyEnd >= 0) {
                    // 替换方法体
                    result.replace(bodyStart, bodyEnd, newBody);
                }
            }
        }
        
        // 应用字段删除 (如果需要)
        for (String fieldName : diff.removedFields) {
            int fieldPos = findFieldPosition(originalCode, fieldName);
            if (fieldPos >= 0) {
                int fieldEnd = findFieldEnd(originalCode, fieldPos);
                if (fieldEnd >= 0) {
                    result.delete(fieldPos, fieldEnd);
                }
            }
        }
        
        // 应用方法删除 (如果需要)
        for (String methodSig : diff.removedMethods) {
            int methodPos = findMethodPosition(originalCode, methodSig);
            if (methodPos >= 0) {
                int methodEnd = findMethodEnd(originalCode, methodPos);
                if (methodEnd >= 0) {
                    result.delete(methodPos, methodEnd);
                }
            }
        }
        
        return result.toString();
    }
    
    // 辅助方法：从LLM代码中提取字段声明
    private String extractFieldDeclaration(String code, String fieldName) {
        Pattern pattern = Pattern.compile("(\\s*private|\\s*protected|\\s*public)\\s+[\\w<>\\[\\]]+\\s+" + 
                                         Pattern.quote(fieldName) + "\\s*=?[^;]*;", 
                                         Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(code);
        if (matcher.find()) {
            return matcher.group().trim();
        }
        return null;
    }
    
    // 辅助方法：从LLM代码中提取方法声明
    private String extractMethodDeclaration(String code, String methodSig) {
        // 简化的方法，实际实现需要更复杂的正则表达式
        String methodName = methodSig.substring(0, methodSig.indexOf('('));
        Pattern pattern = Pattern.compile("(\\s*private|\\s*protected|\\s*public)\\s+[\\w<>\\[\\]]+\\s+" + 
                                         Pattern.quote(methodName) + "\\s*\\([^\\{]*\\{[^\\}]*\\}", 
                                         Pattern.DOTALL);
        Matcher matcher = pattern.matcher(code);
        if (matcher.find()) {
            return matcher.group().trim();
        }
        return null;
    }
    
    // 辅助方法：找到适合插入字段的位置
    private int findFieldInsertPosition(String code) {
        // 简化实现，实际应该找到类声明后的位置
        Pattern pattern = Pattern.compile("\\s*class\\s+[\\w<>]+\\s*\\{");
        Matcher matcher = pattern.matcher(code);
        if (matcher.find()) {
            return matcher.end();
        }
        return 0;
    }
    
    // 辅助方法：找到适合插入方法的位置
    private int findMethodInsertPosition(String code) {
        // 简化实现，实际应该找到最后一个字段或方法后的位置
        int lastBrace = code.lastIndexOf('}');
        if (lastBrace > 0) {
            int prevBrace = code.lastIndexOf('}', lastBrace - 1);
            if (prevBrace > 0) {
                return prevBrace + 1;
            }
        }
        return code.length() - 2; // 假设最后两个字符是 }
    }
    
    // 辅助方法：找到方法在代码中的位置
    private int findMethodPosition(String code, String methodSig) {
        String methodName = methodSig.substring(0, methodSig.indexOf('('));
        Pattern pattern = Pattern.compile("(\\s*private|\\s*protected|\\s*public)\\s+[\\w<>\\[\\]]+\\s+" + 
                                         Pattern.quote(methodName) + "\\s*\\(");
        Matcher matcher = pattern.matcher(code);
        if (matcher.find()) {
            return matcher.start();
        }
        return -1;
    }
    
    // 辅助方法：找到方法体开始的位置
    private int findMethodBodyStart(String code, int methodStart) {
        int bracePos = code.indexOf('{', methodStart);
        if (bracePos > 0) {
            return bracePos + 1;
        }
        return -1;
    }
    
    // 辅助方法：找到方法体结束的位置
    private int findMethodBodyEnd(String code, int bodyStart) {
        int braceCount = 1;
        for (int i = bodyStart; i < code.length(); i++) {
            char c = code.charAt(i);
            if (c == '{') {
                braceCount++;
            } else if (c == '}') {
                braceCount--;
                if (braceCount == 0) {
                    return i;
                }
            }
        }
        return -1;
    }
    
    // 辅助方法：找到字段在代码中的位置
    private int findFieldPosition(String code, String fieldName) {
        Pattern pattern = Pattern.compile("(\\s*private|\\s*protected|\\s*public)\\s+[\\w<>\\[\\]]+\\s+" + 
                                         Pattern.quote(fieldName) + "\\s*=?[^;]*;");
        Matcher matcher = pattern.matcher(code);
        if (matcher.find()) {
            return matcher.start();
        }
        return -1;
    }
    
    // 辅助方法：找到字段结束的位置
    private int findFieldEnd(String code, int fieldStart) {
        int semicolonPos = code.indexOf(';', fieldStart);
        if (semicolonPos > 0) {
            return semicolonPos + 1;
        }
        return -1;
    }
    
    // 辅助方法：找到方法结束的位置
    private int findMethodEnd(String code, int methodStart) {
        int bracePos = code.indexOf('{', methodStart);
        if (bracePos > 0) {
            int braceCount = 1;
            for (int i = bracePos + 1; i < code.length(); i++) {
                char c = code.charAt(i);
                if (c == '{') {
                    braceCount++;
                } else if (c == '}') {
                    braceCount--;
                    if (braceCount == 0) {
                        return i + 1;
                    }
                }
            }
        }
        return -1;
    }
}