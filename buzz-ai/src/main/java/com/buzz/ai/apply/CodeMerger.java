package com.buzz.ai.apply;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.NodeList;
import com.github.javaparser.ast.body.*;
import com.github.javaparser.utils.SourceRoot;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.NodeList;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.FieldDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.body.TypeDeclaration;
import com.github.javaparser.ast.nodeTypes.NodeWithMembers;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

public class CodeMerger {
    
    private static final String PLACEHOLDER_COMMENT = "... 其他代码保持不变 ...";
    
    public String merge(String originalCode, String llmCode) {
        try {
            CompilationUnit originalCU = StaticJavaParser.parse(originalCode);
            CompilationUnit llmCU = StaticJavaParser.parse(llmCode);
            
            // Create a new compilation unit based on LLM code
            CompilationUnit mergedCU = StaticJavaParser.parse(llmCode);
            
            // Check for placeholders in the LLM code
            if (llmCode.contains(PLACEHOLDER_COMMENT)) {
                // If LLM code has placeholders, we need to merge with original code
                if (!originalCU.getTypes().isEmpty() && !llmCU.getTypes().isEmpty()) {
                    mergeTypesWithPlaceholders(originalCU.getType(0), mergedCU.getType(0), llmCode);
                }
            }
            
            return mergedCU.toString();
        } catch (Exception e) {
            throw new RuntimeException("Failed to merge Java files", e);
        }
    }
    
    private void mergeImports(CompilationUnit originalCU, CompilationUnit llmCU) {
        Set<String> originalImports = new HashSet<>();
        originalCU.getImports().forEach(imp -> originalImports.add(imp.getNameAsString()));
        
        llmCU.getImports().forEach(imp -> {
            if (!originalImports.contains(imp.getNameAsString())) {
                originalCU.addImport(imp);
            }
        });
    }
    
    private void mergeTypesWithPlaceholders(TypeDeclaration<?> originalType, TypeDeclaration<?> mergedType, String llmCode) {
        if (originalType instanceof ClassOrInterfaceDeclaration && mergedType instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration originalClass = (ClassOrInterfaceDeclaration) originalType;
            ClassOrInterfaceDeclaration mergedClass = (ClassOrInterfaceDeclaration) mergedType;
            
            // Handle fields with placeholders
            handleMembersWithPlaceholders(originalClass, mergedClass, FieldDeclaration.class, llmCode);
            
            // Handle methods with placeholders
            handleMembersWithPlaceholders(originalClass, mergedClass, MethodDeclaration.class, llmCode);
            
            // Handle inner classes recursively
            handleInnerClassesWithPlaceholders(originalClass, mergedClass, llmCode);
        }
    }
    
    private <T extends BodyDeclaration<?>> void handleMembersWithPlaceholders(
            NodeWithMembers<?> originalNode, 
            NodeWithMembers<?> mergedNode, 
            Class<T> memberType, 
            String llmCode) {
        
        // Get all members of the specified type from both nodes
        List<T> originalMembers = originalNode.getMembers().stream()
                .filter(m -> memberType.isInstance(m))
                .map(m -> memberType.cast(m))
                .collect(Collectors.toList());
        
        List<T> mergedMembers = new ArrayList<>(mergedNode.getMembers().stream()
                .filter(m -> memberType.isInstance(m))
                .map(m -> memberType.cast(m))
                .collect(Collectors.toList()));
        
        // Check each merged member for placeholders
        for (int i = 0; i < mergedMembers.size(); i++) {
            T mergedMember = mergedMembers.get(i);
            String memberStr = mergedMember.toString();
            
            if (memberStr.contains(PLACEHOLDER_COMMENT)) {
                // Find the corresponding original member
                String signature = getMemberSignature(mergedMember);
                Optional<T> originalMember = originalMembers.stream()
                        .filter(m -> getMemberSignature(m).equals(signature))
                        .findFirst();
                
                if (originalMember.isPresent()) {
                    // Replace the placeholder member with the original one
                    int index = mergedNode.getMembers().indexOf(mergedMember);
                    if (index >= 0) {
                        mergedNode.getMembers().set(index, originalMember.get());
                    }
                }
            }
        }
    }
    
    private void handleInnerClassesWithPlaceholders(
            ClassOrInterfaceDeclaration originalClass, 
            ClassOrInterfaceDeclaration mergedClass, 
            String llmCode) {
        
        // Get all inner classes from both classes
        List<ClassOrInterfaceDeclaration> originalInnerClasses = originalClass.getMembers().stream()
                .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                .map(m -> (ClassOrInterfaceDeclaration) m)
                .collect(Collectors.toList());
        
        List<ClassOrInterfaceDeclaration> mergedInnerClasses = mergedClass.getMembers().stream()
                .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                .map(m -> (ClassOrInterfaceDeclaration) m)
                .collect(Collectors.toList());
        
        // For each merged inner class
        for (ClassOrInterfaceDeclaration mergedInnerClass : mergedInnerClasses) {
            String innerClassName = mergedInnerClass.getNameAsString();
            
            // Find the corresponding original inner class
            Optional<ClassOrInterfaceDeclaration> originalInnerClass = originalInnerClasses.stream()
                    .filter(c -> c.getNameAsString().equals(innerClassName))
                    .findFirst();
            
            if (originalInnerClass.isPresent() && mergedInnerClass.toString().contains(PLACEHOLDER_COMMENT)) {
                // Recursively merge the inner classes
                mergeTypesWithPlaceholders(originalInnerClass.get(), mergedInnerClass, llmCode);
            }
        }
    }
    
    private <T extends BodyDeclaration<?>> String getMemberSignature(T member) {
        if (member instanceof MethodDeclaration) {
            MethodDeclaration method = (MethodDeclaration) member;
            return method.getNameAsString() + method.getParameters().toString();
        } else if (member instanceof FieldDeclaration) {
            FieldDeclaration field = (FieldDeclaration) member;
            return field.getVariables().get(0).getNameAsString();
        } else if (member instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration innerClass = (ClassOrInterfaceDeclaration) member;
            return innerClass.getNameAsString();
        }
        return member.toString();
    }
    
    public static void main(String[] args) {
        CodeMerger merger = new CodeMerger();
        String result = merger.merge(Code.hermesMessageOriginal, Code.hermesMessageLLM);
        System.out.println(result);
    }
}
