package com.buzz.ai.apply;

import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.FieldDeclaration;
import com.github.javaparser.ast.body.TypeDeclaration;
import com.github.javaparser.ast.body.*;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class JavaMerger {

    private static final Pattern PLACEHOLDER_COMMENT =
            Pattern.compile("^\\s*//\\s*\\.\\.\\..*?\\.\\.\\.\\s*$", Pattern.MULTILINE);

    private int index;

    private boolean isLazyText(String llmCode) {
        return PLACEHOLDER_COMMENT.matcher(llmCode).find();
    }

    private boolean isFullClass(String llmCode) {
        return llmCode.contains(" class ");
    }

    private String fullMerge(String originalCode, String llmCode) {
        if (!isLazyText(llmCode)) {
            //没有Lazy format直接替换，不过要注意llmCode可能没有package、import
            return null;
        }
        CompilationUnit originalCU = StaticJavaParser.parse(originalCode);
        CompilationUnit llmCU = StaticJavaParser.parse(llmCode);
        // Create a new compilation unit based on LLM code
        CompilationUnit mergedCU = StaticJavaParser.parse(llmCode);

        mergePackage(originalCU, mergedCU);
        mergeImports(originalCU, mergedCU);
        mergeTypes(originalCU.getType(0), llmCU.getType(0), mergedCU.getType(0));

        return removeComment(mergedCU.toString());
    }

    private static String removeComment(String source) {
        Matcher matcher = PLACEHOLDER_COMMENT.matcher(source);
        return matcher.replaceAll("");
    }

    private void mergePackage(CompilationUnit originalCU, CompilationUnit llmCU) {
        if (!llmCU.getPackageDeclaration().isPresent()) {
            llmCU.setPackageDeclaration(originalCU.getPackageDeclaration().get());
        }
    }

    private void mergeImports(CompilationUnit originalCU, CompilationUnit llmCU) {
        Set<String> originalImports = new HashSet<>();
        originalCU.getImports().forEach(imp -> originalImports.add(imp.getNameAsString()));
        if (llmCU.getImports().isEmpty()) {
            originalCU.getImports().stream().forEach(i -> llmCU.addImport(i));
        }
    }

    private void mergeTypes(TypeDeclaration<?> originalType, TypeDeclaration<?> llmType, TypeDeclaration<?> mergedType) {
        if (originalType instanceof ClassOrInterfaceDeclaration && llmType instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration originalClass = (ClassOrInterfaceDeclaration) originalType;
            ClassOrInterfaceDeclaration llmClass = (ClassOrInterfaceDeclaration) llmType;
            ClassOrInterfaceDeclaration mergedClass = (ClassOrInterfaceDeclaration) mergedType;

            //reset index
            index=0;
            // Merge fields
            mergeFields(originalClass, llmClass, mergedClass);

            // Merge methods
            mergeMethods(originalClass, llmClass, mergedClass);

            // Merge inner classes
            handleInnerClasses(originalClass, llmClass, mergedClass);
        }
    }

    private void mergeFields(ClassOrInterfaceDeclaration originalNode, ClassOrInterfaceDeclaration llmNode, ClassOrInterfaceDeclaration mergedNode) {


        Map<String, FieldDeclaration> originalMembers = new LinkedHashMap<>();
        Map<String, FieldDeclaration> llmMembers = new LinkedHashMap<>();

        // Collect original members
        for (FieldDeclaration member : originalNode.getFields()) {
            originalMembers.put(getMemberSignature(member), member);
        }

        // Collect llm members
        List<FieldDeclaration> llmNodeFields = llmNode.getFields();
        for (int i = 0; i < llmNodeFields.size(); ++i) {
            FieldDeclaration field = llmNodeFields.get(i);
            llmMembers.put(getMemberSignature(field), field);
        }

        //merge
        //int i = 0;
        for (Map.Entry<String, FieldDeclaration> e : originalMembers.entrySet()) {
            String signature = e.getKey();
            FieldDeclaration field = e.getValue();
            if (!llmMembers.containsKey(signature)) {
                mergedNode.getMembers().add(index,field);
            }
            ++index;
        }

        index+=llmMembers.size();
    }


    private void mergeMethods(ClassOrInterfaceDeclaration originalNode, ClassOrInterfaceDeclaration llmNode, ClassOrInterfaceDeclaration mergedNode) {
        Map<String, MethodDeclaration> originalMembers = new LinkedHashMap<>();
        Map<String, MethodDeclaration> llmMembers = new LinkedHashMap<>();

        // Collect original members
        for (MethodDeclaration member : originalNode.getMethods()) {
            originalMembers.put(getMemberSignature(member), member);
        }

        // Collect llm members
        for (MethodDeclaration method : llmNode.getMethods()) {
            llmMembers.put(getMemberSignature(method), method);
        }

        //merge
        //int i = 0;
        for (Map.Entry<String, MethodDeclaration> e : originalMembers.entrySet()) {
            String signature = e.getKey();
            MethodDeclaration method = e.getValue();
            if (!llmMembers.containsKey(signature)) {
                mergedNode.getMembers().add(index,method);
            }
            ++index;
        }
        index+=llmMembers.size();
    }

    private void handleInnerClasses(ClassOrInterfaceDeclaration originalNode, ClassOrInterfaceDeclaration llmNode, ClassOrInterfaceDeclaration mergedNode) {

        // Get all inner classes from both classes
        List<ClassOrInterfaceDeclaration> originalInnerClasses = originalNode.getMembers().stream()
                .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                .map(m -> (ClassOrInterfaceDeclaration) m)
                .collect(Collectors.toList());

        List<ClassOrInterfaceDeclaration> llmInnerClasses = llmNode.getMembers().stream()
                .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                .map(m -> (ClassOrInterfaceDeclaration) m)
                .collect(Collectors.toList());

        List<ClassOrInterfaceDeclaration> mergedInnerClasses = mergedNode.getMembers().stream()
                .filter(m -> m instanceof ClassOrInterfaceDeclaration)
                .map(m -> (ClassOrInterfaceDeclaration) m)
                .collect(Collectors.toList());


        // For each merged inner class
        for (ClassOrInterfaceDeclaration llmInnerClass : llmInnerClasses) {
            String innerClassName = llmInnerClass.getNameAsString();

            // Find the corresponding original inner class
            Optional<ClassOrInterfaceDeclaration> originalInnerClass = originalInnerClasses.stream()
                    .filter(c -> c.getNameAsString().equals(innerClassName))
                    .findFirst();

            //The original inner class maybe not existed
            if (originalInnerClass.isPresent()) {

                // Find the corresponding merged inner class
                ClassOrInterfaceDeclaration mergedInnerClass = mergedInnerClasses
                        .stream().filter(c -> c.getNameAsString().equals(innerClassName))
                        .findFirst().get();

                // Recursively merge the inner classes
                mergeTypes(originalInnerClass.get(), llmInnerClass, mergedInnerClass);
            }

        }

    }

    private <T extends BodyDeclaration<?>> String getMemberSignature(T member) {
        if (member instanceof MethodDeclaration) {
            MethodDeclaration method = (MethodDeclaration) member;
            return method.getNameAsString() + method.getParameters().toString();
        } else if (member instanceof FieldDeclaration) {
            FieldDeclaration field = (FieldDeclaration) member;
            return field.getVariables().get(0).getNameAsString();
        } else if (member instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration innerClass = (ClassOrInterfaceDeclaration) member;
            return innerClass.getNameAsString();
        }
        return member.toString();
    }


    private String partialMerge(String originalCode, String llmCode) {
        return null;
    }

    public String merge(String originalCode, String llmCode) {
        if (isFullClass(llmCode)) {
            return fullMerge(originalCode, llmCode);
        } else {
            return partialMerge(originalCode, llmCode);
        }


    }

    public static void main(String[] args) {
        String result = new JavaMerger().merge(Code.hermesMessageOriginal, Code.hermesMessageLLM);
        System.out.println(result);
    }

}
