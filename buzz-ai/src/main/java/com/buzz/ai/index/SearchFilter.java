package com.buzz.ai.index;

import lombok.Data;

import java.util.List;

@Data
public class SearchFilter {
    private List<String> pathFilters;
    private Double minScore;
    private Integer limit;

    @Override
    public String toString() {
        return "SearchFilter{" +
                "pathFilters=" + pathFilters +
                ", minScore=" + minScore +
                ", limit=" + limit +
                '}';
    }
}

