package com.buzz.ai.index;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class QdrantVectorStore {

    private static final Logger logger = LoggerFactory.getLogger(QdrantVectorStore.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final String DISTANCE_METRIC = "Cosine";

    private final String qdrantUrl;
    private final String apiKey;
    private final int vectorSize;
    private final String collectionName;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    public QdrantVectorStore() {
        this(null, Constants.DEFAULT_QDRANT_URL, Constants.DEFAULT_VECTOR_DIMENSION, null);
    }

    public QdrantVectorStore(String workspacePath, String qdrantUrl, int vectorSize, String apiKey) {
        this.qdrantUrl = qdrantUrl.endsWith("/") ? qdrantUrl.substring(0, qdrantUrl.length() - 1) : qdrantUrl;
        this.apiKey = apiKey;
        this.vectorSize = vectorSize;
        this.objectMapper = new ObjectMapper();

        // Generate collection name from workspace path

        this.collectionName = "hermes-class-summary";

        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS);

        this.httpClient = clientBuilder.build();
    }

    public QdrantVectorStore(String workspacePath, String qdrantUrl, int vectorSize) {
        this(workspacePath, qdrantUrl, vectorSize, null);
    }


    public boolean initialize() {

        try {
            boolean created = false;

            // Check if collection exists
            if (!collectionExists()) {
                // Create collection
                createCollection();
                created = true;
            } else {
                // Verify vector size matches
                JsonNode collectionInfo = getCollectionInfo();
                if (collectionInfo != null) {
                    JsonNode vectorsConfig = collectionInfo.path("config").path("params").path("vectors");
                    int existingSize = vectorsConfig.path("size").asInt(0);

                    if (existingSize != vectorSize) {
                        logger.warn("Collection {} exists with vector size {}, but expected {}. Recreating collection.",
                                collectionName, existingSize, vectorSize);
                        deleteCollection();
                        createCollection();
                        created = true;
                    }
                }
            }

            // Create payload index for filePath
            createPayloadIndex();

            return created;
        } catch (Exception e) {
            logger.error("Failed to initialize Qdrant collection: {}", collectionName, e);
            throw new RuntimeException("Failed to initialize vector store", e);
        }

    }


    public void upsertPoints(List<PointStruct> points) {

        try {
            if (points.isEmpty()) {
                return;
            }

            // Process points and add path segments
            List<Map<String, Object>> processedPoints = new ArrayList<>();
            for (PointStruct point : points) {
                // Debug: Check if vector is valid
                double[] vector = point.getVector();
                if (vector == null || vector.length == 0) {
                    logger.error("Point {} has empty or null vector!", point.getId());
                    continue;
                }
                if (vector.length != vectorSize) {
                    logger.error("Point {} has vector size {} but expected {}", point.getId(), vector.length, vectorSize);
                    continue;
                }

                Map<String, Object> processedPoint = new HashMap<>();
                processedPoint.put("id", point.getId());
                processedPoint.put("vector", vector);

                Map<String, Object> payload = new HashMap<>(point.getPayload());

                // Add path segments for better filtering
                String filePath = (String) payload.get("filePath");
                if (filePath != null) {
                    String[] segments = filePath.split("[/\\\\]");
                    Map<String, String> pathSegments = new HashMap<>();
                    for (int i = 0; i < segments.length; i++) {
                        if (!segments[i].isEmpty()) {
                            pathSegments.put(String.valueOf(i), segments[i]);
                        }
                    }
                    payload.put("pathSegments", pathSegments);
                }

                processedPoint.put("payload", payload);
                processedPoints.add(processedPoint);

                // Debug: Log first point details
                if (processedPoints.size() == 1) {
                    logger.debug("First point - ID: {}, Vector length: {}, Payload keys: {}",
                            point.getId(), vector.length, payload.keySet());
                }
            }

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("points", processedPoints);
            requestBody.put("wait", true);

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            RequestBody body = RequestBody.create(jsonBody, JSON);

            Request request = buildRequest("PUT", "/collections/" + collectionName + "/points", body);

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new IOException("Failed to upsert points: " + response.code() + " - " + errorBody);
                } else {
                    // Only read body for debugging if successful
                    String responseBody = response.body() != null ? response.body().string() : "";
                    logger.debug("PUT {} result={}", collectionName, responseBody);
                }
            }

            logger.debug("Successfully upserted {} points to collection {}", points.size(), collectionName);

        } catch (Exception e) {
            logger.error("Failed to upsert points", e);
            throw new RuntimeException("Failed to upsert points", e);
        }

    }


    public List<VectorStoreSearchResult> search(double[] queryVector, SearchFilter filter) {

        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("query", queryVector);

            // Build filter
            if (filter != null) {
                Map<String, Object> qdrantFilter = buildQdrantFilter(filter);
                if (qdrantFilter != null) {
                    requestBody.put("filter", qdrantFilter);
                }

                if (filter.getMinScore() != null) {
                    requestBody.put("score_threshold", filter.getMinScore());
                } else {
                    requestBody.put("score_threshold", Constants.SEARCH_MIN_SCORE);
                }

                if (filter.getLimit() != null) {
                    requestBody.put("limit", filter.getLimit());
                } else {
                    requestBody.put("limit", Constants.MAX_SEARCH_RESULTS);
                }
            } else {
                requestBody.put("score_threshold", Constants.SEARCH_MIN_SCORE);
                requestBody.put("limit", Constants.MAX_SEARCH_RESULTS);
            }

            Map<String, Object> params = new HashMap<>();
            params.put("hnsw_ef", 128);
            params.put("exact", false);
            requestBody.put("params", params);
            requestBody.put("with_payload", true);

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            RequestBody body = RequestBody.create(jsonBody, JSON);

            Request request = buildRequest("POST", "/collections/" + collectionName + "/points/query", body);

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new IOException("Search request failed: " + response.code() + " - " + errorBody);
                }

                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                JsonNode pointsNode = jsonResponse.path("result").path("points");

                List<VectorStoreSearchResult> results = new ArrayList<>();
                if (pointsNode.isArray()) {
                    for (JsonNode pointNode : pointsNode) {
                        VectorStoreSearchResult result = parseSearchResult(pointNode);
                        if (result != null) {
                            results.add(result);
                        }
                    }
                }

                logger.debug("Search returned {} results for collection {}", results.size(), collectionName);
                return results;
            }

        } catch (Exception e) {
            logger.error("Failed to search points", e);
            throw new RuntimeException("Failed to search points", e);
        }

    }


    public void deletePointsByFilePath(String filePath) {
        deletePointsByMultipleFilePaths(Arrays.asList(filePath));
    }


    public void deletePointsByMultipleFilePaths(List<String> filePaths) {
        try {
            if (filePaths.isEmpty()) {
                return;
            }

            // Build filter for multiple file paths
            List<Map<String, Object>> shouldConditions = new ArrayList<>();
            for (String filePath : filePaths) {
                Map<String, Object> condition = new HashMap<>();
                condition.put("key", "filePath");
                Map<String, Object> match = new HashMap<>();
                match.put("value", filePath);
                condition.put("match", match);
                shouldConditions.add(condition);
            }

            Map<String, Object> filter = new HashMap<>();
            filter.put("should", shouldConditions);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("filter", filter);
            requestBody.put("wait", true);

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            RequestBody body = RequestBody.create(jsonBody, JSON);

            Request request = buildRequest("POST", "/collections/" + collectionName + "/points/delete", body);

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new IOException("Failed to delete points: " + response.code() + " - " + errorBody);
                }
            }

            logger.debug("Successfully deleted points for {} files from collection {}", filePaths.size(), collectionName);

        } catch (Exception e) {
            logger.error("Failed to delete points by file paths", e);
            throw new RuntimeException("Failed to delete points by file paths", e);
        }

    }


    public void clearCollection() {

        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("wait", true);

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            RequestBody body = RequestBody.create(jsonBody, JSON);

            Request request = buildRequest("POST", "/collections/" + collectionName + "/points/delete", body);

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new IOException("Failed to clear collection: " + response.code() + " - " + errorBody);
                }
            }

            logger.info("Successfully cleared collection {}", collectionName);

        } catch (Exception e) {
            logger.error("Failed to clear collection", e);
            throw new RuntimeException("Failed to clear collection", e);
        }

    }


    public void deleteCollection() {

        try {
            Request request = buildRequest("DELETE", "/collections/" + collectionName, null);

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful() && response.code() != 404) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new IOException("Failed to delete collection: " + response.code() + " - " + errorBody);
                }
            }

            logger.info("Successfully deleted collection {}", collectionName);

        } catch (Exception e) {
            logger.error("Failed to delete collection", e);
            throw new RuntimeException("Failed to delete collection", e);
        }

    }


    public boolean collectionExists() {
        try {
            Request request = buildRequest("GET", "/collections/" + collectionName, null);

            try (Response response = httpClient.newCall(request).execute()) {
                return response.isSuccessful();
            }

        } catch (Exception e) {
            logger.debug("Error checking collection existence: {}", e.getMessage());
            return false;
        }
    }


    public List<String> getAllFilePaths() {
        try {
            Set<String> filePaths = new HashSet<>();
            int offset = 0;
            int limit = 1000;
            boolean hasMore = true;

            while (hasMore) {
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("limit", limit);
                requestBody.put("offset", offset);
                requestBody.put("with_payload", Arrays.asList("filePath"));

                String jsonBody = objectMapper.writeValueAsString(requestBody);
                RequestBody body = RequestBody.create(jsonBody, JSON);

                Request request = buildRequest("POST", "/collections/" + collectionName + "/points/scroll", body);

                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                        throw new IOException("Failed to get file paths: " + response.code() + " - " + errorBody);
                    }

                    String responseBody = response.body().string();
                    JsonNode jsonResponse = objectMapper.readTree(responseBody);
                    JsonNode pointsNode = jsonResponse.path("result").path("points");

                    if (pointsNode.isArray() && pointsNode.size() > 0) {
                        for (JsonNode pointNode : pointsNode) {
                            JsonNode payloadNode = pointNode.path("payload");
                            JsonNode filePathNode = payloadNode.path("filePath");
                            if (!filePathNode.isMissingNode()) {
                                filePaths.add(filePathNode.asText());
                            }
                        }
                        offset += pointsNode.size();
                        hasMore = pointsNode.size() == limit;
                    } else {
                        hasMore = false;
                    }
                }
            }

            return new ArrayList<>(filePaths);

        } catch (Exception e) {
            logger.error("Failed to get all file paths", e);
            throw new RuntimeException("Failed to get all file paths", e);
        }
    }

    // Helper methods

    private void createCollection() throws IOException {
        Map<String, Object> vectorConfig = new HashMap<>();
        vectorConfig.put("size", vectorSize);
        vectorConfig.put("distance", DISTANCE_METRIC);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("vectors", vectorConfig);

        String jsonBody = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(jsonBody, JSON);

        Request request = buildRequest("PUT", "/collections/" + collectionName, body);

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("Failed to create collection: " + response.code() + " - " + errorBody);
            }

        }
        logger.info("Successfully created collection {} with vector size {}", collectionName, vectorSize);
    }

    private void createPayloadIndex() {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("field_name", "filePath");
            requestBody.put("field_schema", "keyword");

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            RequestBody body = RequestBody.create(jsonBody, JSON);

            Request request = buildRequest("PUT", "/collections/" + collectionName + "/index", body);

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    if (!errorBody.toLowerCase().contains("already exists")) {
                        logger.warn("Could not create payload index for filePath: {} - {}", response.code(), errorBody);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to create payload index: {}", e.getMessage());
        }
    }

    private JsonNode getCollectionInfo() {
        try {
            Request request = buildRequest("GET", "/collections/" + collectionName, null);

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    JsonNode jsonResponse = objectMapper.readTree(responseBody);
                    return jsonResponse.path("result");
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to get collection info: {}", e.getMessage());
        }
        return null;
    }

    private Map<String, Object> buildQdrantFilter(SearchFilter filter) {
        if (filter.getPathFilters() == null || filter.getPathFilters().isEmpty()) {
            return null;
        }

        List<Map<String, Object>> shouldConditions = new ArrayList<>();
        for (String pathFilter : filter.getPathFilters()) {
            Map<String, Object> condition = new HashMap<>();
            condition.put("key", "filePath");
            Map<String, Object> match = new HashMap<>();
            match.put("text", pathFilter.replace("\\", "/"));
            condition.put("match", match);
            shouldConditions.add(condition);
        }

        Map<String, Object> qdrantFilter = new HashMap<>();
        qdrantFilter.put("should", shouldConditions);
        return qdrantFilter;
    }

    private VectorStoreSearchResult parseSearchResult(JsonNode pointNode) {
        try {
            String id = pointNode.path("id").asText();
            double score = pointNode.path("score").asDouble();
            JsonNode payloadNode = pointNode.path("payload");

            if (payloadNode.isMissingNode()) {
                return null;
            }

//            VectorStoreSearchResult.Payload payload = new VectorStoreSearchResult.Payload();
//            payload.setFilePath(payloadNode.path("filePath").asText());
//            payload.setCodeChunk(payloadNode.path("codeChunk").asText());
//            payload.setStartLine(payloadNode.path("startLine").asInt());
//            payload.setEndLine(payloadNode.path("endLine").asInt());
//            payload.setChunkSource(payloadNode.path("chunkSource").asText());
//            payload.setType(payloadNode.path("type").asText());
//            payload.setIdentifier(payloadNode.path("identifier").asText());
//            payload.setHierarchyDisplay(payloadNode.path("hierarchyDisplay").asText());

            Map<String, String> payload = new HashMap<>();
            payload.put("methodName", payloadNode.path("methodName").asText());
            payload.put("className", payloadNode.path("className").asText());
            payload.put("summary_cn", payloadNode.path("summary_cn").asText());
            payload.put("code", payloadNode.path("code").asText());
            return new VectorStoreSearchResult(id, score, payload);
        } catch (Exception e) {
            logger.warn("Failed to parse search result: {}", e.getMessage());
            return null;
        }
    }

    private Request buildRequest(String method, String path, RequestBody body) {
        Request.Builder builder = new Request.Builder()
                .url(qdrantUrl + path);

        if (apiKey != null && !apiKey.isEmpty()) {
            builder.addHeader("api-key", apiKey);
        }

        switch (method.toUpperCase()) {
            case "GET":
                builder.get();
                break;
            case "POST":
                builder.post(body != null ? body : RequestBody.create("", JSON));
                break;
            case "PUT":
                builder.put(body != null ? body : RequestBody.create("", JSON));
                break;
            case "DELETE":
                builder.delete(body);
                break;
            default:
                throw new IllegalArgumentException("Unsupported HTTP method: " + method);
        }

        return builder.build();
    }
}
