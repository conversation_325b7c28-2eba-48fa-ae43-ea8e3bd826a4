package com.buzz.ai.index;

import java.util.List;

public class EmbeddingResponse {
    private List<double[]> embeddings;
    private Usage usage;

    public EmbeddingResponse() {}

    public EmbeddingResponse(List<double[]> embeddings, Usage usage) {
        this.embeddings = embeddings;
        this.usage = usage;
    }

    public List<double[]> getEmbeddings() { return embeddings; }
    public void setEmbeddings(List<double[]> embeddings) { this.embeddings = embeddings; }

    public Usage getUsage() { return usage; }
    public void setUsage(Usage usage) { this.usage = usage; }

    /**
     * Usage statistics
     */
    public static class Usage {
        private int promptTokens;
        private int totalTokens;

        public Usage() {}

        public Usage(int promptTokens, int totalTokens) {
            this.promptTokens = promptTokens;
            this.totalTokens = totalTokens;
        }

        public int getPromptTokens() { return promptTokens; }
        public void setPromptTokens(int promptTokens) { this.promptTokens = promptTokens; }

        public int getTotalTokens() { return totalTokens; }
        public void setTotalTokens(int totalTokens) { this.totalTokens = totalTokens; }
    }

    @Override
    public String toString() {
        return "EmbeddingResponse{" +
                "embeddingsCount=" + (embeddings != null ? embeddings.size() : 0) +
                ", usage=" + usage +
                '}';
    }
}
