package com.buzz.ai.index;

import java.util.List;
import java.util.Map;
import java.util.Scanner;

public class SearchMain {


    private static void runInteractiveSearch(SearchService manager) {
        Scanner scanner = new Scanner(System.in);

        System.out.println("\n" + "=".repeat(60));
        System.out.println("Interactive Search Mode");
        System.out.println("Type your search queries (or 'quit' to exit)");
        System.out.println("=" + "=".repeat(60));

        while (true) {
            System.out.print("\nSearch> ");
            String input = scanner.nextLine().trim();

            if (input.equalsIgnoreCase("quit") || input.equalsIgnoreCase("exit")) {
                System.exit(-1);
                break;
            }

            if (input.isEmpty()) {
                continue;
            }

            performSearch(manager, input);
        }

        scanner.close();
        System.out.println("Goodbye!");
    }

    private static void performSearch(SearchService manager, String query) {
        System.out.println("\nSearching for: " + query);

        try {
            List<VectorStoreSearchResult> results = manager.searchIndex(query);

            System.out.println("Found " + results.size() + " results:");
            System.out.println("=" + "=".repeat(80));

            for (int i = 0; i < results.size(); i++) {
                VectorStoreSearchResult result = results.get(i);
                Map<String,String> payload = result.getPayload();

                System.out.printf("%d. Score: %.4f | %s %n",
                        i + 1, result.getScore(),
                        payload.get("className")+","+payload.get("methodName")
                        );

//                if (payload.getIdentifier() != null) {
//                    System.out.println("   Identifier: " + payload.getIdentifier());
//                }
//                if (payload.getType() != null) {
//                    System.out.println("   Type: " + payload.getType());
//                }

                // Show first few lines of code
                String code = payload.get("code");
                if (code != null) {
                    String[] lines = code.split("\n");
                    int linesToShow = Math.min(3, lines.length);
                    for (int j = 0; j < linesToShow; j++) {
                        System.out.println("   " + lines[j]);
                    }
                    if (lines.length > linesToShow) {
                        System.out.println("   ... (" + (lines.length - linesToShow) + " more lines)");
                    }
                }
                System.out.println();
            }

        } catch (Exception e) {
            System.err.println("Search failed: " + e.getMessage());
            //logger.error("Search failed", e);
        }
    }

    public static void main(String[] args) {
        runInteractiveSearch(new SearchService());
    }

}
