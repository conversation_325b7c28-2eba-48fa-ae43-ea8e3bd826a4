package com.buzz.ai.index;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

public class OllamaEmbedder {
    private static final Logger logger = LoggerFactory.getLogger(OllamaEmbedder.class);
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private final String baseUrl;
    private final String model;
    private final int dimension;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    public OllamaEmbedder(String baseUrl, String model, int dimension) {
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        this.model = model;
        this.dimension = dimension;
        this.objectMapper = new ObjectMapper();

        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    public OllamaEmbedder(String baseUrl, String model) {
        this(baseUrl, model, Constants.DEFAULT_VECTOR_DIMENSION);
    }

    public OllamaEmbedder() {
        this(Constants.DEFAULT_OLLAMA_URL, Constants.DEFAULT_MODEL);
    }


    public EmbeddingResponse createEmbeddings(List<String> texts) {
        return createEmbeddings(texts, this.model);
    }


    public EmbeddingResponse createEmbeddings(List<String> texts, String model) {
        try {
            List<double[]> allEmbeddings = new ArrayList<>();
            int totalTokens = 0;

            // Process texts in batches to avoid overwhelming the server
            int batchSize = 10;
            for (int i = 0; i < texts.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, texts.size());
                List<String> batch = texts.subList(i, endIndex);

                List<double[]> batchEmbeddings = processBatch(batch, model);
                allEmbeddings.addAll(batchEmbeddings);

                // Estimate token usage (rough approximation)
                for (String text : batch) {
                    totalTokens += estimateTokens(text);
                }

                // Add small delay between batches to be respectful to the server
                if (endIndex < texts.size()) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during batch processing", e);
                    }
                }
            }

            EmbeddingResponse.Usage usage = new EmbeddingResponse.Usage(totalTokens, totalTokens);
            return new EmbeddingResponse(allEmbeddings, usage);

        } catch (Exception e) {
            logger.error("Failed to create embeddings", e);
            throw new RuntimeException("Failed to create embeddings", e);
        }

    }


    /**
     * Processes a batch of texts to get embeddings
     */
    private List<double[]> processBatch(List<String> texts, String model) throws IOException {
        List<double[]> embeddings = new ArrayList<>();

        for (String text : texts) {
            double[] embedding = getSingleEmbedding(text, model);
            embeddings.add(embedding);
        }

        return embeddings;
    }

    public double[] getSingleEmbedding(String text) throws IOException {
        return getSingleEmbedding(text, this.model);
    }
    /**
     * Gets embedding for a single text
     */
    public double[] getSingleEmbedding(String text, String model) throws IOException {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);
        requestBody.put("prompt", text);

        String jsonBody = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(jsonBody, JSON);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/embeddings")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("Ollama API request failed: " + response.code() + " - " + errorBody);
            }

            String responseBody = response.body().string();
            JsonNode jsonResponse = objectMapper.readTree(responseBody);

            JsonNode embeddingNode = jsonResponse.get("embedding");
            if (embeddingNode == null || !embeddingNode.isArray()) {
                throw new IOException("Invalid response format: missing or invalid embedding array");
            }

            double[] embedding = new double[embeddingNode.size()];
            for (int i = 0; i < embeddingNode.size(); i++) {
                embedding[i] = embeddingNode.get(i).asDouble();
            }

            return embedding;
        }
    }

    /**
     * Estimates token count for a text (rough approximation)
     */
    private int estimateTokens(String text) {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return Math.max(1, text.length() / 4);
    }
}
