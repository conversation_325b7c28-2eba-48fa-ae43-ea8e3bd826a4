package com.buzz.ai.index;

import com.buzz.ai.llm.DefaultLLMProvider;
import com.buzz.ai.llm.LLMProvider;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SummaryFileService {

    private final LLMProvider llmProvider;
    private final ObjectMapper objectMapper;

    public SummaryFileService() {
        this.llmProvider = new DefaultLLMProvider();
        this.objectMapper = new ObjectMapper();
    }

    public Map<String, List<Map<String, String>>> readSummary() {
        try {
            String content = Files.readString(Path.of("summary_file.json"));
            Map<String, List<Map<String, String>>> result = objectMapper.readValue(
                    content,
                    new TypeReference<Map<String, List<Map<String, String>>>>() {
                    }
            );
            return result;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    public void summary(String path) throws IOException {
        File rootDir = new File(path);
        Map<String, String> classMap = new HashMap<>();
        List<File> files = findJavaFiles(rootDir);
        log.info("Found {} java files", files.size());

        for (int i = 0; i < files.size(); ++i) {
            File javaFile = files.get(i);
            String content = new String(Files.readAllBytes(javaFile.toPath()));
            try {
                String fileSummary = generateSummaryWithOpenAI(javaFile.getPath(), content);
                classMap.put(FileUtil.getClassNameFromFile(javaFile.getName()), fileSummary);
                log.info("Success to summary {} {}", javaFile.getName(), i);
            } catch (Exception e) {
                log.error("Failed to summary " + javaFile.getName(), e);
            }

        }

        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(classMap);
        Files.write(Paths.get("summary_file.json"), json.getBytes());
    }


    String generateSummaryWithOpenAI(String path, String javaFileContent) throws IOException {
        String prompt = """
                  Your goal is to extract symbols from the given source code. The types of symbols to extract include:
                  - Summary
                  - Functions
                  - Classes
                  - Variables
                  - All import statements
                
                  If there are no symbols, simply return an empty string.
                  If there are symbols, return them in the following format:
                  Summary: Mainly used to provide automatic implementation of function templates.
                  {Symbol Type}: {Symbol Name} {Symbol Name}, ...                
                
                  Notes:
                  1. Output the result directly. Do not attempt to run any code.
                  2. Do not analyze the content or purpose of the code.
                  3. The purpose must not exceed 100 characters in length.
                  4. Use ^^ as the separator for import statements.
                
                  Here is an example:
                
                  Input
                  The following is the source code from file /test.py:
                
                  import os
                  import time
                  from loguru import logger
                  import byzerllm
                
                  a = ""
                
                  @byzerllm.prompt(render="jinja")
                  def auto_implement_function_template(instruction:str, content:str) -> str:
                
                  Output
                  Summary: Mainly used to provide automatic implementation of function templates.
                  Function: auto_implement_function_template
                  Variable: a
                  Class:
                  Import: import os^^import time^^from loguru import logger^^import byzerllm
                
                  Now, let's begin a new task:
                
                  Input
                  The following is the source code from file %s:
                
                  %s
                
                  Output
                """;

        prompt = String.format(prompt, path, javaFileContent);
        String content = llmProvider.chat(prompt);
        return content;
    }


    List<File> findJavaFiles(File dir) {
        List<File> files = new ArrayList<>();
        File[] entries = dir.listFiles();
        if (entries == null) return files;
        for (File entry : entries) {
            if (entry.isDirectory()) {
                files.addAll(findJavaFiles(entry));
            } else if (entry.getName().endsWith(".java") && !entry.getName().contains("Test")) {
                files.add(entry);
            }
        }
        return files;
    }


    public static void main(String[] args) throws IOException {

        //new SummaryService().summary("/work/dist/branch/wacai/middleware/hermes-parent4");

        SummaryFileService ss = new SummaryFileService();
        ss.summary("/work/dist/branch/wacai/middleware/hermes-parent4");
    }
}
