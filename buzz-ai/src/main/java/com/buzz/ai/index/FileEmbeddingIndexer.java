package com.buzz.ai.index;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;


@Slf4j
public class FileEmbeddingIndexer {
    private static final Logger logger = LoggerFactory.getLogger(EmbeddingIndexer.class);
    private final OllamaEmbedder embedder;
    private final QdrantVectorStore vectorStore;
    private Map<String, String> summmaryMap;

    public FileEmbeddingIndexer() {
        this.embedder = new OllamaEmbedder();
        this.vectorStore = new QdrantVectorStore();
        vectorStore.initialize();
    }

    public void index() {
        try {
            String content = Files.readString(Path.of("summary_file.json"));
            ObjectMapper objectMapper = new ObjectMapper();
            summmaryMap = objectMapper.readValue(
                    content,
                    new TypeReference<Map<String, String>>() {
                    });

            List<PointStruct> pointStructList = new ArrayList<>();
            for (Map.Entry<String, String> entry : summmaryMap.entrySet()) {
                String path = entry.getKey();
                String symbol = entry.getValue();
                double[] vector = embedder.getSingleEmbedding(symbol);

                // 输出结果
                Map<String, Object> payload = new HashMap<>();
                payload.put("className", path);

                String pointId = UUID.nameUUIDFromBytes((path).getBytes()).toString();
                PointStruct pointStruct = new PointStruct();
                pointStruct.setId(pointId);
                pointStruct.setVector(vector);
                pointStruct.setPayload(payload);
                pointStructList.add(pointStruct);
                if (pointStructList.size() > 20) {
                    vectorStore.upsertPoints(pointStructList);
                    pointStructList.clear();
                }

                log.info("process {}", path);
            }

            vectorStore.upsertPoints(pointStructList);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public static void main(String[] args) {
        FileEmbeddingIndexer fileEmbeddingIndexer = new FileEmbeddingIndexer();
        fileEmbeddingIndexer.index();
    }
}
