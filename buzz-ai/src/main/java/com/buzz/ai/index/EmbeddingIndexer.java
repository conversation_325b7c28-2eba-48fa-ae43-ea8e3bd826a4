package com.buzz.ai.index;

import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.body.TypeDeclaration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

public class EmbeddingIndexer {
    private static final Logger logger = LoggerFactory.getLogger(EmbeddingIndexer.class);

    private final OllamaEmbedder embedder;
    private final QdrantVectorStore vectorStore;
    private final SummaryMethodService summaryService;
    private final String basePath;
    private Map<String, List<Map<String, String>>> summmaryMap;

    public EmbeddingIndexer(String basePath) {
        this.embedder = new OllamaEmbedder();
        this.vectorStore = new QdrantVectorStore();
        this.summaryService = new SummaryMethodService();
        this.basePath = basePath;

        vectorStore.initialize();
    }

    public void index() {
        File rootDir = new File(basePath);
        List<File> files = findJavaFiles(rootDir);


        for (int i = 0; i < files.size(); ++i) {
            File javaFile = files.get(i);
            try {
                processJavaFile(javaFile);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

        }


    }

    private Map<String, Map<String, String>> getMethodSummary(String className) {
        if (summmaryMap == null) {
            summmaryMap = summaryService.readSummary();
        }
        List<Map<String, String>> jsonList = summmaryMap.get(className);
        if (jsonList == null) {
            return null;
        }
        Map<String, Map<String, String>> methodMap = jsonList.stream()
                .collect(Collectors.toMap(
                        map -> map.get("methodName"), // 使用methodName作为key
                        map -> map,                   // 原始map作为value
                        (existing, replacement) -> existing // 如果有重复key，保留已存在的
                ));
        return methodMap;
    }

    void processJavaFile(File file) throws IOException {
        String content = new String(Files.readAllBytes(file.toPath()));
        CompilationUnit cu = null;
        try {
            cu = StaticJavaParser.parse(content);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        List<PointStruct> pointStructList = new ArrayList<>();

        for (TypeDeclaration<?> type : cu.getTypes()) {
            if (type instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration clazz = (ClassOrInterfaceDeclaration) type;
                String className = clazz.getNameAsString();
                logger.info("process {}", className);

                // 收集所有public方法
                List<MethodDeclaration> publicMethods = clazz.getMethods().stream()
                        .filter(MethodDeclaration::isPublic)
                        .toList();


                if (!publicMethods.isEmpty()) {
                    System.out.println("🧠 Generating summaries for " + className);

                    // 批量生成方法摘要
                    Map<String, Map<String, String>> summaries = getMethodSummary(className);
                    if(summaries==null){
                        System.out.println("skip "+ className +" no summary");
                        continue;
                    }

                    // 处理每个方法的embedding
                    for (int i = 0; i < publicMethods.size(); i++) {
                        MethodDeclaration method = publicMethods.get(i);
                        Map<String, String> summary = summaries.get(method.getName().asString());

                        String methodName = method.getNameAsString();
                        String signature = method.getDeclarationAsString(false, false, true);
                        String summaryCn = summary != null ? summary.get("summary_en") : "unknow";

                        // 构造embedding文本
                        String input = String.format("%s \n class method: %s %s", summaryCn, className, signature);
                        //String input = String.join(" ", summaryCn, signature, className);
//                        Map<String, String> input = new LinkedHashMap<>();
//                        input.put("type", "method");
//                        input.put("methodName", methodName);
//                        input.put("className", className);
//                        input.put("signature", signature);
//                        input.put("summary_cn", summaryCn);
//                        input.put("code", getMethodCodeSnippet(content, method));

                        double[] vector = embedder.getSingleEmbedding(input);

                        // 输出结果
                        Map<String, Object> payload = new HashMap<>();
                        payload.put("className", className);
                        payload.put("methodName", methodName);
                        payload.put("summary_cn", summaryCn);
                        payload.put("signature", signature);
                        payload.put("code", getMethodCodeSnippet(content, method));

                        String pointId = UUID.nameUUIDFromBytes((className + "_" + signature).getBytes()).toString();

                        PointStruct pointStruct = new PointStruct();
                        pointStruct.setId(pointId);
                        pointStruct.setVector(vector);
                        pointStruct.setPayload(payload);
                        pointStructList.add(pointStruct);
                        //System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(payload));
                    }
                }

                if (pointStructList.size() > 20) {
                    vectorStore.upsertPoints(pointStructList);
                    pointStructList.clear();
                }
            }
        }

        vectorStore.upsertPoints(pointStructList);
    }

    String getMethodCodeSnippet(String content, MethodDeclaration method) {
        int start = method.getBegin().map(p -> p.line - 1).orElse(0);
        int end = Math.min(start + 20, content.split("\n").length);
        String[] lines = content.split("\n");
        StringBuilder codeSnippet = new StringBuilder();
        for (int i = start; i < end; i++) codeSnippet.append(lines[i]).append("\n");
        return codeSnippet.toString();
    }


    public static List<File> findJavaFiles(File dir) {
        List<File> files = new ArrayList<>();
        File[] entries = dir.listFiles();
        if (entries == null) return files;
        for (File entry : entries) {
            if (entry.isDirectory()) {
                files.addAll(findJavaFiles(entry));
            } else if (entry.getName().endsWith(".java") && !entry.getName().contains("Test")) {
                files.add(entry);
            }
        }
        return files;
    }

    public static void main(String[] args) {
        EmbeddingIndexer indexer = new EmbeddingIndexer("/work/dist/branch/wacai/middleware/hermes-parent4");
        indexer.index();
    }
}
