package com.buzz.ai.index;

import com.buzz.ai.llm.DefaultLLMProvider;
import com.buzz.ai.llm.LLMProvider;
import com.buzz.ai.util.CodeFence;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SummaryMethodService {

    private final LLMProvider llmProvider;
    private final ObjectMapper objectMapper;

    public SummaryMethodService() {
        this.llmProvider = new DefaultLLMProvider();
        this.objectMapper = new ObjectMapper();
    }

    public Map<String, List<Map<String, String>>> readSummary() {
        try {
            String content = Files.readString(Path.of("summary.json"));
            Map<String, List<Map<String, String>>> result = objectMapper.readValue(
                    content,
                    new TypeReference<Map<String, List<Map<String, String>>>>() {
                    }
            );
            return result;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    public void summary(String path) throws IOException {
        File rootDir = new File(path);
        Map<String, List<Map<String, String>>> classMap = new HashMap<>();
        List<File> files = findJavaFiles(rootDir);
        log.info("Found {} java files", files.size());

        for (int i = 0; i < files.size(); ++i) {
            File javaFile = files.get(i);
            String content = new String(Files.readAllBytes(javaFile.toPath()));
            try {
                List<Map<String, String>> methodSummaries = generateSummaryWithOpenAI(content);
                classMap.put(FileUtil.getClassNameFromFile(javaFile.getName()), methodSummaries);
                log.info("Success to summary {} {}", javaFile.getName(), i);
            } catch (Exception e) {
                log.error("Failed to summary " + javaFile.getName(), e);
            }

        }

        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(classMap);
        System.out.println(json);
        Files.write(Paths.get("summary.json"), json.getBytes());
    }


    List<Map<String, String>> generateSummaryWithOpenAI(String javaFileContent) throws IOException {
        String prompt = """
                你是一个 Java 工程分析助手。以下是一个 Java 类的完整源码，请你为该类中的每个 public 方法生成中文摘要。
                
                要求返回一个 JSON 格式数组，每个对象包含：
                - methodName: 方法名
                - signature: 方法签名
                - summary_cn: 中文摘要
                - summary_en: 英文摘要
                
                请仅返回 JSON ARRAY，无需其他说明。
                
                例子：
                
                ```java
                 public class EditorManagerListener{
                
                    public static boolean isRelatedFile(VirtualFile file){
                        if (file == null) return false; ...
                    }
                 }
                ```
                
                输出：
                ```json
                [
                    {
                        "methodName": "isRelatedFile",
                        "signature": "public static boolean isRelatedFile(VirtualFile file)",
                        "summary_en": "Check if file name ends with .java",
                        "summary_cn": "判断是否是相关的文件"
                    }
                ]
                ```
                
                Java 源码:
                """ + javaFileContent;

        String content = llmProvider.chat(prompt);
        String jsonContent = CodeFence.parseAll(content).get(0).getText();
        // 解析返回的JSON数组

        List<Map<String, String>> jsonList = objectMapper.readValue(jsonContent,
                objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class));

        return jsonList;
    }


    List<File> findJavaFiles(File dir) {
        List<File> files = new ArrayList<>();
        File[] entries = dir.listFiles();
        if (entries == null) return files;
        for (File entry : entries) {
            if (entry.isDirectory()) {
                files.addAll(findJavaFiles(entry));
            } else if (entry.getName().endsWith(".java") && !entry.getName().contains("Test")) {
                files.add(entry);
            }
        }
        return files;
    }


    public static void main(String[] args) throws IOException {

        //new SummaryService().summary("/work/dist/branch/wacai/middleware/hermes-parent4");

        SummaryMethodService ss = new SummaryMethodService();
        ss.summary("/work/dist/branch/wacai/middleware/hermes-parent4");
    }
}
