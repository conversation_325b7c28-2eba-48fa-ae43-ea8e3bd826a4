package com.buzz.ai.index;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

public class SearchService {
    private static final Logger logger = LoggerFactory.getLogger(SearchService.class);

    private final OllamaEmbedder embedder;
    private final QdrantVectorStore vectorStore;

    public SearchService() {
        this.embedder = new OllamaEmbedder();
        this.vectorStore = new QdrantVectorStore();
    }

    /**
     * Searches the code index for relevant content.
     *
     * @param query The search query
     * @return CompletableFuture resolving to list of search results
     */
    public List<VectorStoreSearchResult> searchIndex(String query) {
        return searchIndex(query, null);
    }

    /**
     * Searches the code index for relevant content.
     *
     * @param query  The search query
     * @param filter Search filter options
     * @return CompletableFuture resolving to list of search results
     * @throws RuntimeException if the service is not properly configured or ready
     */
    public List<VectorStoreSearchResult> searchIndex(String query, SearchFilter filter) {
        try {
            if (embedder == null || vectorStore == null) {
                throw new RuntimeException("Search service is not properly configured.");
            }

            // Prefix query for better context
            String prefixedQuery = "search_code: " + query;

            logger.debug("Searching for: {}", prefixedQuery);

            // Generate embedding for query
            EmbeddingResponse embeddingResponse = embedder.createEmbeddings(Arrays.asList(prefixedQuery));
            List<double[]> embeddings = embeddingResponse.getEmbeddings();

            if (embeddings.isEmpty()) {
                throw new RuntimeException("Failed to generate embedding for query.");
            }

            double[] queryVector = embeddings.get(0);

            // Perform search
            List<VectorStoreSearchResult> results = vectorStore.search(queryVector, filter);

            logger.debug("Search returned {} results", results.size());
            return results;

        } catch (Exception e) {
            logger.error("Error during search: {}", e.getMessage(), e);
            throw new RuntimeException("Search failed: " + e.getMessage(), e);
        }
    }


}
