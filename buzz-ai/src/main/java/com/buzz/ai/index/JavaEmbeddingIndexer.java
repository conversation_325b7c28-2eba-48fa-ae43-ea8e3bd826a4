package com.buzz.ai.index;

import com.alibaba.fastjson.JSON;
import com.buzz.ai.llm.DefaultLLMProvider;
import com.buzz.ai.llm.LLMProvider;
import com.buzz.ai.util.CodeFence;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.*;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class JavaEmbeddingIndexer {
    private static final Logger logger = LoggerFactory.getLogger(JavaEmbeddingIndexer.class);
    private static final String DISTANCE_METRIC = "Cosine";
    private static final MediaType JSON_TYPE = MediaType.get("application/json; charset=utf-8");
    public static final String DEFAULT_QDRANT_URL = "http://localhost:6333";
    public static final String DEFAULT_OLLAMA_URL = "http://localhost:11434";
    public static final String DEFAULT_MODEL = "nomic-embed-text";
    public static final int DEFAULT_VECTOR_DIMENSION = 768;
    private final ObjectMapper objectMapper;
    private final LLMProvider llmProvider;
    private final OkHttpClient httpClient;
    private final String collectionName = "my-test";
    private final String model;
    private final String baseUrl;
    private final String qdrantUrl = DEFAULT_QDRANT_URL;
    private final int vectorSize = DEFAULT_VECTOR_DIMENSION;

    public JavaEmbeddingIndexer() throws IOException {
        baseUrl = DEFAULT_OLLAMA_URL;
        model = DEFAULT_MODEL;
        objectMapper = new ObjectMapper();
        llmProvider = new DefaultLLMProvider();

        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        if (!collectionExists()) {
            createCollection();
        }
    }

    public static void main(String[] args) throws Exception {
        JavaEmbeddingIndexer indexer = new JavaEmbeddingIndexer();

        File rootDir = new File("/work/dist/branch/wacai/middleware/hermes-parent4"); // 替换为你的代码目录
        for (File javaFile : indexer.findJavaFiles(rootDir)) {
            indexer.processJavaFile(javaFile);
        }
    }

    List<File> findJavaFiles(File dir) {
        List<File> files = new ArrayList<>();
        File[] entries = dir.listFiles();
        if (entries == null) return files;
        for (File entry : entries) {
            if (entry.isDirectory()) {
                files.addAll(findJavaFiles(entry));
            } else if (entry.getName().endsWith(".java") && !entry.getName().contains("Test")) {
                files.add(entry);
            }
        }
        return files;
    }

    void processJavaFile(File file) throws IOException {
        String content = new String(Files.readAllBytes(file.toPath()));
        CompilationUnit cu = null;
        try {
            cu = StaticJavaParser.parse(content);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        List<PointStruct> pointStructList = new ArrayList<>();

        for (TypeDeclaration<?> type : cu.getTypes()) {
            if (type instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration clazz = (ClassOrInterfaceDeclaration) type;
                String className = clazz.getNameAsString();
                logger.info("process {}",className);

                // 收集所有public方法
                List<MethodDeclaration> publicMethods = clazz.getMethods().stream()
                        .filter(MethodDeclaration::isPublic)
                        .toList();


                if (!publicMethods.isEmpty()) {
                    System.out.println("🧠 Generating summaries for " + className);

                    // 批量生成方法摘要
                    Map<String, Map<String, String>> summaries;
                    try {

                        summaries = generateSummaryWithOpenAI(content);

                    } catch (Exception e) {
                        logger.error("summary error", e);
                        continue;
                    }

                    // 处理每个方法的embedding
                    for (int i = 0; i < publicMethods.size(); i++) {
                        MethodDeclaration method = publicMethods.get(i);
                        Map<String, String> summary = summaries.get(method.getName().asString());

                        String methodName = method.getNameAsString();
                        String signature = method.getDeclarationAsString(false, false, true);
                        String summaryCn = summary != null ? summary.get("summary_cn") : "unKnow";

                        // 构造embedding文本
                        //String input = String.join(" ", summaryCn, signature, className);
                        Map<String, String> input = new LinkedHashMap<>();
                        input.put("type", "method");
                        input.put("methodName", methodName);
                        input.put("className", className);
                        input.put("signature", signature);
                        input.put("summary_cn", summaryCn);
                        input.put("code", getMethodCodeSnippet(content, method));

                        double[] vector = getSingleEmbedding(JSON.toJSONString(input));

                        // 输出结果
                        Map<String, Object> payload = new HashMap<>();
                        payload.put("className", className);
                        payload.put("methodName", methodName);
                        payload.put("summary_cn", summaryCn);
                        payload.put("signature", signature);
                        payload.put("code", getMethodCodeSnippet(content, method));

                        String pointId = UUID.nameUUIDFromBytes((className + "_" + signature).getBytes()).toString();

                        PointStruct pointStruct = new PointStruct();
                        pointStruct.setId(pointId);
                        pointStruct.setVector(vector);
                        pointStruct.setPayload(payload);
                        pointStructList.add(pointStruct);
                        //System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(payload));
                    }
                }

                if (pointStructList.size() > 20) {
                    upsertPoints(pointStructList);
                    pointStructList.clear();
                }
            }
        }

        upsertPoints(pointStructList);
    }


    private boolean collectionExists() {
        try {
            Request request = buildRequest("GET", "/collections/" + collectionName, null);

            try (Response response = httpClient.newCall(request).execute()) {
                return response.isSuccessful();
            }

        } catch (Exception e) {
            logger.debug("Error checking collection existence: {}", e.getMessage());
            return false;
        }
    }


    private void createCollection() throws IOException {
        Map<String, Object> vectorConfig = new HashMap<>();
        vectorConfig.put("size", vectorSize);
        vectorConfig.put("distance", DISTANCE_METRIC);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("vectors", vectorConfig);

        String jsonBody = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(jsonBody, JSON_TYPE);

        Request request = buildRequest("PUT", "/collections/" + collectionName, body);

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("Failed to create collection: " + response.code() + " - " + errorBody);
            }

        }
        logger.info("Successfully created collection {} with vector size {}", collectionName, vectorSize);
    }

    public void upsertPoints(List<PointStruct> points) {
        try {
            if (points.isEmpty()) {
                return;
            }

            // Process points and add path segments
            List<Map<String, Object>> processedPoints = new ArrayList<>();
            for (PointStruct point : points) {
                // Debug: Check if vector is valid
                double[] vector = point.getVector();
                if (vector == null || vector.length == 0) {
                    logger.error("Point {} has empty or null vector!", point.getId());
                    continue;
                }
                if (vector.length != vectorSize) {
                    logger.error("Point {} has vector size {} but expected {}", point.getId(), vector.length, vectorSize);
                    continue;
                }

                Map<String, Object> processedPoint = new HashMap<>();
                processedPoint.put("id", point.getId());
                processedPoint.put("vector", vector);

                Map<String, Object> payload = new HashMap<>(point.getPayload());

                // Add path segments for better filtering
                String filePath = (String) payload.get("filePath");
                if (filePath != null) {
                    String[] segments = filePath.split("[/\\\\]");
                    Map<String, String> pathSegments = new HashMap<>();
                    for (int i = 0; i < segments.length; i++) {
                        if (!segments[i].isEmpty()) {
                            pathSegments.put(String.valueOf(i), segments[i]);
                        }
                    }
                    payload.put("pathSegments", pathSegments);
                }

                processedPoint.put("payload", payload);
                processedPoints.add(processedPoint);

                // Debug: Log first point details
                if (processedPoints.size() == 1) {
                    logger.debug("First point - ID: {}, Vector length: {}, Payload keys: {}",
                            point.getId(), vector.length, payload.keySet());
                }
            }

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("points", processedPoints);
            requestBody.put("wait", true);

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            RequestBody body = RequestBody.create(jsonBody, JSON_TYPE);

            Request request = buildRequest("PUT", "/collections/" + collectionName + "/points", body);

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                    throw new IOException("Failed to upsert points: " + response.code() + " - " + errorBody);
                } else {
                    // Only read body for debugging if successful
                    String responseBody = response.body() != null ? response.body().string() : "";
                    logger.info("PUT {} result={}", collectionName, responseBody);
                }
            }

            logger.debug("Successfully upserted {} points to collection {}", points.size(), collectionName);

        } catch (Exception e) {
            logger.error("Failed to upsert points", e);
            throw new RuntimeException("Failed to upsert points", e);
        }
    }

    private Request buildRequest(String method, String path, RequestBody body) {
        Request.Builder builder = new Request.Builder()
                .url(qdrantUrl + path);


        switch (method.toUpperCase()) {
            case "GET":
                builder.get();
                break;
            case "POST":
                builder.post(body != null ? body : RequestBody.create("", JSON_TYPE));
                break;
            case "PUT":
                builder.put(body != null ? body : RequestBody.create("", JSON_TYPE));
                break;
            case "DELETE":
                builder.delete(body);
                break;
            default:
                throw new IllegalArgumentException("Unsupported HTTP method: " + method);
        }

        return builder.build();
    }


    String getMethodCodeSnippet(String content, MethodDeclaration method) {
        int start = method.getBegin().map(p -> p.line - 1).orElse(0);
        int end = Math.min(start + 20, content.split("\n").length);
        String[] lines = content.split("\n");
        StringBuilder codeSnippet = new StringBuilder();
        for (int i = start; i < end; i++) codeSnippet.append(lines[i]).append("\n");
        return codeSnippet.toString();
    }

    Map<String, Map<String, String>> generateSummaryWithOpenAI(String javaFileContent) throws IOException {
        String prompt = """
                你是一个 Java 工程分析助手。以下是一个 Java 类的完整源码，请你为该类中的每个 public 方法生成中文摘要。
                
                要求返回一个 JSON 格式数组，每个对象包含：
                - methodName: 方法名
                - signature: 方法签名
                - summary_cn: 中文摘要
                
                请仅返回 JSON ARRAY，无需其他说明。
                
                例子：
                
                ```java
                 public class EditorManagerListener{
                
                    public static boolean isRelatedFile(VirtualFile file){
                        if (file == null) return false; ...
                    }
                 }
                ```
                
                输出：
                ```json
                [
                    {
                        "methodName": "isRelatedFile",
                        "signature": "public static boolean isRelatedFile(VirtualFile file)",
                        "summary_cn": "判断是否是相关的文件"
                    }
                ]
                ```
                
                Java 源码:
                """ + javaFileContent;

        String content = llmProvider.chat(prompt);
        String jsonContent = CodeFence.parseAll(content).get(0).getText();
        // 解析返回的JSON数组

        List<Map<String, String>> jsonList = objectMapper.readValue(jsonContent,
                objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class));

        Map<String, Map<String, String>> methodMap = jsonList.stream()
                .collect(Collectors.toMap(
                        map -> map.get("methodName"), // 使用methodName作为key
                        map -> map,                   // 原始map作为value
                        (existing, replacement) -> existing // 如果有重复key，保留已存在的
                ));

        return methodMap;
    }

    private double[] getSingleEmbedding(String text) throws IOException {

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);
        requestBody.put("prompt", text);

        String jsonBody = objectMapper.writeValueAsString(requestBody);
        RequestBody body = RequestBody.create(jsonBody, JSON_TYPE);

        Request request = new Request.Builder()
                .url(baseUrl + "/api/embeddings")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new IOException("Ollama API request failed: " + response.code() + " - " + errorBody);
            }

            String responseBody = response.body().string();
            JsonNode jsonResponse = objectMapper.readTree(responseBody);

            JsonNode embeddingNode = jsonResponse.get("embedding");
            if (embeddingNode == null || !embeddingNode.isArray()) {
                throw new IOException("Invalid response format: missing or invalid embedding array");
            }

            double[] embedding = new double[embeddingNode.size()];
            for (int i = 0; i < embeddingNode.size(); i++) {
                embedding[i] = embeddingNode.get(i).asDouble();
            }

            return embedding;
        }
    }
}

