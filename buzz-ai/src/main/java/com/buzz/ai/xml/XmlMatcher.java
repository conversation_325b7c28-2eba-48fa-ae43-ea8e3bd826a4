package com.buzz.ai.xml;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class XmlMatcher<Result> {
    private int index = 0;
    private List<XmlMatcherResult> chunks = new ArrayList<>();
    private List<String> cached = new ArrayList<>();
    private boolean matched = false;
    private String state = "TEXT";
    private int depth = 0;
    private int pointer = 0;
    private final String tagName;
    private final Function<XmlMatcherResult, Result> transform;
    private final int position;

    public XmlMatcher(String tagName) {
        this(tagName, null);
    }

    public XmlMatcher(String tagName, Function<XmlMatcherResult, Result> transform) {
        this(tagName, transform, 0);
    }

    public XmlMatcher(String tagName, Function<XmlMatcherResult, Result> transform, int position) {
        this.tagName = tagName;
        this.transform = transform;
        this.position = position;
    }

    private void collect() {
        if (this.cached.isEmpty()) {
            return;
        }

        String data = String.join("", this.cached);
        boolean currentMatched = this.matched;

        if (!this.chunks.isEmpty()) {
            XmlMatcherResult last = this.chunks.get(this.chunks.size() - 1);
            if (last.isMatched() == currentMatched) {
                // Combine with last chunk if match status is same
                XmlMatcherResult newResult = new XmlMatcherResult(
                        currentMatched,
                        last.getData() + data
                );
                this.chunks.set(this.chunks.size() - 1, newResult);
                this.cached = new ArrayList<>();
                return;
            }
        }

        this.chunks.add(new XmlMatcherResult(currentMatched, data));
        this.cached = new ArrayList<>();
    }

    private List<Result> pop() {
        List<XmlMatcherResult> currentChunks = new ArrayList<>(this.chunks);
        this.chunks = new ArrayList<>();

        if (this.transform == null) {
            return (List<Result>) currentChunks;
        }

        List<Result> results = new ArrayList<>();
        for (XmlMatcherResult chunk : currentChunks) {
            results.add(this.transform.apply(chunk));
        }
        return results;
    }

    private void updateInternal(String chunk) {
        for (int i = 0; i < chunk.length(); i++) {
            char c = chunk.charAt(i);
            this.cached.add(String.valueOf(c));
            this.pointer++;

            switch (this.state) {
                case "TEXT":
                    if (c == '<' && (this.pointer <= this.position + 1 || this.matched)) {
                        this.state = "TAG_OPEN";
                        this.index = 0;
                    } else {
                        this.collect();
                    }
                    break;

                case "TAG_OPEN":
                    if (c == '>' && this.index == this.tagName.length()) {
                        this.state = "TEXT";
                        if (!this.matched) {
                            this.cached = new ArrayList<>();
                        }
                        this.depth++;
                        this.matched = true;
                    } else if (this.index == 0 && c == '/') {
                        this.state = "TAG_CLOSE";
                    } else if (c == ' ' && (this.index == 0 || this.index == this.tagName.length())) {
                        continue;
                    } else if (this.index < this.tagName.length() && this.tagName.charAt(this.index) == c) {
                        this.index++;
                    } else {
                        this.state = "TEXT";
                        this.collect();
                    }
                    break;

                case "TAG_CLOSE":
                    if (c == '>' && this.index == this.tagName.length()) {
                        this.state = "TEXT";
                        this.depth--;
                        this.matched = this.depth > 0;
                        if (!this.matched) {
                            this.cached = new ArrayList<>();
                        }
                    } else if (c == ' ' && (this.index == 0 || this.index == this.tagName.length())) {
                        continue;
                    } else if (this.index < this.tagName.length() && this.tagName.charAt(this.index) == c) {
                        this.index++;
                    } else {
                        this.state = "TEXT";
                        this.collect();
                    }
                    break;
            }
        }
    }

    public List<Result> finalChunk(String chunk) {
        if (chunk != null) {
            this.updateInternal(chunk);
        }
        this.collect();
        return this.pop();
    }

    public List<Result> update(String chunk) {
        this.updateInternal(chunk);
        return this.pop();
    }
}