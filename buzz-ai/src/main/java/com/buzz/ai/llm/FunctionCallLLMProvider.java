package com.buzz.ai.llm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.SubmissionPublisher;

public class FunctionCallLLMProvider  {
    private static final String API_URL = "http://chatgpt.tiny-test.wke-office.test.wacai.info/";
    private static final String API_KEY = "45a7a7914f0b4bac807f03088d97f20a";
    private OkHttpClient client;
    private List<ChatMessage> messages = new ArrayList<>();
    private int historyMessageLength;
    private String model = "deepseek-v3";
//    private String model="gpt-4o-omni";


    public FunctionCallLLMProvider() {
        client = new OkHttpClient();
    }

    private ExecutorService executor = Executors.newFixedThreadPool(2);



    public SubmissionPublisher<String> stream(String userPrompt, String systemPrompt, boolean keepHistory) {
        List<ChatMessage> messageList = new ArrayList<>();
        if (!StringUtils.isEmpty(systemPrompt)) {
            messageList.add(new ChatMessage("system", systemPrompt));
        }
        messageList.add(new ChatMessage("user", userPrompt));

        return stream(messageList, keepHistory);
    }


    public SubmissionPublisher<String> stream(List<ChatMessage> messageList, boolean keepHistory) {
        messages.addAll(messageList);
        OpenAIBody openAIBody = new OpenAIBody(messages, 0.0f, true);

        // 1. 定义 Function Tool
        JSONObject searchCodeFunction = new JSONObject();
        searchCodeFunction.put("name", "search_code");
        searchCodeFunction.put("description", "在代码库中搜索特定内容（使用 grep/find 命令）");

        JSONObject parameters = new JSONObject();
        parameters.put("type", "object");

        JSONObject properties = new JSONObject();
        properties.put("query", new JSONObject()
                .fluentPut("type", "string")
                .fluentPut("description", "搜索关键词或正则表达式"));
        properties.put("file_pattern", new JSONObject()
                .fluentPut("type", "string")
                .fluentPut("description", "文件匹配模式（如 '*.java'）")
                .fluentPut("default", "*"));
        properties.put("search_path", new JSONObject()
                .fluentPut("type", "string")
                .fluentPut("description", "搜索的根目录路径")
                .fluentPut("default", "."));

        parameters.put("properties", properties);
        parameters.put("required", new JSONArray().fluentAdd("query"));
        searchCodeFunction.put("parameters", parameters);

        JSONArray tools = new JSONArray()
                .fluentAdd(new JSONObject()
                        .fluentPut("type", "function")
                        .fluentPut("function", searchCodeFunction));
        openAIBody.setTools(tools);

        //requestBody.put("tool_choice", "auto");



        // 构造 JSON 请求体
        String requestBody = JSON.toJSONString(openAIBody);
        //DebugLogger.info(this.getClass(), "send prompt:\n {}", JSON.toJSONString(messageList));

        String url = API_URL + "/openai/deployments/" + model + "/chat/completions?api-version=2024-06-01";
        Request request = new Request.Builder()
                .url(url)
                .header("api-key", API_KEY)
                .header("Content-Type", "application/json")
                .post(RequestBody.create(requestBody, MediaType.get("application/json")))
                .build();

        // 异步请求以处理流式响应
        Call call = client.newCall(request);
        SubmissionPublisher<String> publisher = new SubmissionPublisher<>();
        call.enqueue(new StreamResponseCallback(publisher));
        if (historyMessageLength > 8192 || !keepHistory) {
            messages.clear();
        }
        return publisher;
    }


}
