package com.buzz.ai.llm;


import com.buzz.ai.util.DebugLogger;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import  com.alibaba.fastjson.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.concurrent.SubmissionPublisher;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.concurrent.SubmissionPublisher;

public class StreamResponseCallback implements Callback {
    SubmissionPublisher<String> publisher;

    public StreamResponseCallback(SubmissionPublisher<String> publisher) {
        this.publisher = publisher;
    }

    @Override
    public void onFailure(@NotNull Call call, @NotNull IOException e) {
        //DebugLogger.error(this.getClass(),"occur error", e);
        call.cancel();
        //publisher.submit(I18NConstant.NETWORK_ERROR);
        publisher.close();
    }

    @Override
    public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
        if (publisher.isClosed()) {
            call.cancel();
            return;
        }
        if (!response.isSuccessful()) {
            //DebugLogger.error(this.getClass(),"Request failed: {} {}", response.code(), response.body().string());
            //publisher.submit(I18NConstant.NETWORK_ERROR);
            publisher.close();
            return;
        }

        try {
            processResponse(call,response);
        } catch (IOException e) {
            //DebugLogger.error(this.getClass(),"Request failed", e);
            call.cancel();
            //publisher.submit(I18NConstant.NETWORK_ERROR);
            publisher.close();
        }
    }

    protected void processResponse(Call call, Response response) throws IOException {
        StringBuffer contentBuffer = new StringBuffer();
        // 处理流式响应
        try (BufferedReader reader = new BufferedReader(response.body().charStream())) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.isBlank()) {
                    //收到结束关闭
                    if (line.startsWith("data") && line.endsWith("[DONE]")) {
                        call.cancel();
                        publisher.close();
                        break;
                    }
                    //删除"data: "
                    line = line.replace("data:","").trim();
                    JSONObject json = null;
                    try {
                        json = JSON.parseObject(line);
                    }catch (Exception e) {
                        DebugLogger.error(this.getClass(),"parse json error", e);
                        throw new IOException(e);
                    }

                    for (JSONObject item : json.getJSONArray("choices").toArray(new JSONObject[0])) {
                        String finishReason = item.getString("finish_reason");
                        if (finishReason != null) {
                            //finish_reason包括：
                            // null: 生成尚未完成（更多内容会继续流回来）
                            // stop: 模型生成已完成，并返回了完整的内容
                            // length: 生成因长度限制而提前终止。
                            // content_filter: 生成被内容过滤器中断
                            //DebugLogger.info(this.getClass(),"finish_reason:{}, LLM responses completed content: \n{}", finishReason,contentBuffer.toString());
                            break;
                        }
                        //获取内容
                        JSONObject delta = item.getJSONObject("delta");
                        if (delta != null) {
                            String content = delta.getString("content");
                            if (!publisher.isClosed()) {
                                publisher.submit(content);
                                contentBuffer.append(content);
                            }
                        }
                    }
                }
            }
        }
    }
}