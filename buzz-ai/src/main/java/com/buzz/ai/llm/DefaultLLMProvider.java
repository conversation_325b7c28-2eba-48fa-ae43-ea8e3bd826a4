package com.buzz.ai.llm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.buzz.ai.util.DebugLogger;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import okhttp3.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.SubmissionPublisher;

import com.buzz.ai.util.*;

public class DefaultLLMProvider implements LLMProvider {
    private static final String API_URL = "http://chatgpt.tiny-test.wke-office.test.wacai.info/";
    private static final String API_KEY = "45a7a7914f0b4bac807f03088d97f20a";
    private OkHttpClient client;
    private String model = "deepseek-v3-250324";
//    private String model="gpt-4o-omni";
//    private String model="gpt-4o-omni";


    public DefaultLLMProvider() {
        client = new OkHttpClient();
    }


    @Override
    public String chat(String userPrompt) {
        SubmissionPublisher<String> flow = stream(null, List.of(new ChatMessage("user", userPrompt)));
        CountDownLatch latch = new CountDownLatch(1);
        StringBuffer sb = new StringBuffer();
        flow.subscribe(new AutoRequestSubscriber<String>() {
            @Override
            protected void handleItem(String item) {
                sb.append(item);
            }

            public void onComplete() {
                latch.countDown();
            }

            public void onError(Throwable e) {
                e.printStackTrace();
            }
        });

        try {
            latch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return sb.toString();
    }

    @Override
    public SubmissionPublisher<String> stream(String systemPrompt, List<ChatMessage> userPrompt) {
        OpenAIBody openAIBody = createOpenAIBody(systemPrompt, userPrompt);
        String requestBody = JSON.toJSONString(openAIBody);

        //DebugLogger.info(this.getClass(), "send {} user prompt:\n {}", model, JSON.toJSONString(userPrompt));

        String url = API_URL + "/openai/deployments/" + model + "/chat/completions?api-version=2024-06-01";
        Request request = new Request.Builder()
                .url(url)
                .header("api-key", API_KEY)
                .header("Content-Type", "application/json")
                .post(RequestBody.create(requestBody, MediaType.get("application/json")))
                .build();

        Call call = client.newCall(request);
        SubmissionPublisher<String> publisher = new SubmissionPublisher<>();
        call.enqueue(new StreamResponseCallback(publisher));
        return publisher;
    }

    protected OpenAIBody createOpenAIBody(String systemPrompt, List<ChatMessage> userPrompt) {
        List<ChatMessage> messages = new ArrayList<>();
        if (systemPrompt != null) {
            messages.add(new ChatMessage("system", systemPrompt));
        }
        messages.addAll(userPrompt);
        return new OpenAIBody(messages, 0.0f, true);
    }
}




