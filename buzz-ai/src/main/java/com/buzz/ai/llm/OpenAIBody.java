package com.buzz.ai.llm;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.util.List;

@Data
public class OpenAIBody {
    private List<ChatMessage> messages;
    private float temperature;
    private boolean stream;
    private JSONArray tools;

    public OpenAIBody(List<ChatMessage> messages, float temperature, boolean stream) {
        this.messages = messages;
        this.temperature = temperature;
        this.stream = stream;
    }
}