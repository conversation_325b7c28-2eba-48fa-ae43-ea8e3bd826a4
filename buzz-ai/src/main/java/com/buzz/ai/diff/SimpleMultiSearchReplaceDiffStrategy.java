package com.buzz.ai.diff;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

//wapilot实现的第一个版本，不支持模糊匹配，只能精确匹配

public class SimpleMultiSearchReplaceDiffStrategy implements DiffStrategy {

    private static final Pattern DIFF_PATTERN = Pattern.compile(
            "(?:^|\\n)(?<!\\\\)<<<<<<< SEARCH\\s*\\n" +
                    "((?:\\:start_line:\\s*(\\d+)\\s*\\n))?" +
                    "((?:\\:end_line:\\s*(\\d+)\\s*\\n))?" +
                    "((?<!\\\\)-------\\s*\\n)?" +
                    "([\\s\\S]*?)(?:\\n)?" +
                    "(?:(?<=\\n)(?<!\\\\)=======\\s*\\n)" +
                    "([\\s\\S]*?)(?:\\n)?" +
                    "(?:(?<=\\n)(?<!\\\\)>>>>>>> REPLACE)(?=\\n|$)"
    );

    @Override
    public DiffResult applyDiff(String originalContent, String diffContent) {
        List<DiffResult> diffResults = new ArrayList<>();
        List<String> resultLines = splitLines(originalContent);
        //记录行号的偏移量，比如第一个block search 3行，replace 2行，那么下一个block的startLine需要+1
        //在定位后续block的起始行时，会使用这个delta值进行调整：
        int delta = 0;
        int appliedCount = 0;

        // Apply global line range if specified
        //int globalStartIndex =  0;

        Matcher matcher = DIFF_PATTERN.matcher(diffContent);
        while (matcher.find()) {
            int startLine = matcher.group(2) != null ? Integer.parseInt(matcher.group(2)) : 0;
            String searchContent = unescapeMarkers(matcher.group(6));
            String replaceContent = unescapeMarkers(matcher.group(7));

            // Split content into lines
            List<String> searchLines = searchContent.isEmpty() ? new ArrayList<>() : splitLines(searchContent);
            List<String> replaceLines = replaceContent.isEmpty() ? new ArrayList<>() : splitLines(replaceContent);

            // Validate input
            if (searchLines.isEmpty()) {
                diffResults.add(new DiffResult(false, "Empty search content is not allowed"));
                continue;
            }

            if (searchContent.equals(replaceContent)) {
                diffResults.add(new DiffResult(false, "Search and replace content are identical"));
                continue;
            }

            // Adjust start line with delta and apply global range
            startLine = startLine + (startLine == 0 ? 0 : delta);

            // Check bounds
            if (startLine < 1 || startLine > resultLines.size()) {
                diffResults.add(new DiffResult(false, "Start line out of bounds"));
                continue;
            }

            int matchIndex = startLine - 1;
            int endIndex = matchIndex + searchLines.size();

            // Ensure end index is within global range
            endIndex = Math.min(endIndex, resultLines.size());

            if (endIndex > resultLines.size()) {
                diffResults.add(new DiffResult(false, "Search content exceeds file bounds"));
                continue;
            }

            // Get matched lines and their indentation
            List<String> matchedLines = resultLines.subList(matchIndex, endIndex);
            List<String> originalIndents = new ArrayList<>();
            for (String line : matchedLines) {
                originalIndents.add(getIndentation(line));
            }

            // Apply replacement with preserved indentation
            List<String> indentedReplaceLines = new ArrayList<>();
            for (int i = 0; i < replaceLines.size(); i++) {
                String line = replaceLines.get(i);
                String indent = i < originalIndents.size() ? originalIndents.get(i) : originalIndents.get(0);
                indentedReplaceLines.add(indent + line.trim());
            }

            // Apply the change
            List<String> beforeMatch = resultLines.subList(0, matchIndex);
            List<String> afterMatch = resultLines.subList(endIndex, resultLines.size());

            List<String> newContent = new ArrayList<>(beforeMatch);
            newContent.addAll(indentedReplaceLines);
            newContent.addAll(afterMatch);

            resultLines = newContent;
            delta = delta - matchedLines.size() + replaceLines.size();
            appliedCount++;
        }

        if (appliedCount == 0) {

            return new DiffResult(false, "No diffs applied", diffResults);
        }

        return new DiffResult(true, String.join(getLineEnding(originalContent), resultLines), diffResults);
    }

    private String unescapeMarkers(String content) {
        return content
                .replace("\\<<<<<<<", "<<<<<<<")
                .replace("\\=======", "=======")
                .replace("\\>>>>>>>", ">>>>>>>")
                .replace("\\-------", "-------")
                .replace("\\:end_line:", ":end_line:")
                .replace("\\:start_line:", ":start_line:");
    }

    private List<String> splitLines(String content) {
        String[] lines = content.split("\\r?\\n");
        List<String> result = new ArrayList<>();
        for (String line : lines) {
            result.add(line);
        }
        return result;
    }

    private String getIndentation(String line) {
        int i = 0;
        while (i < line.length() && (line.charAt(i) == ' ' || line.charAt(i) == '\t')) {
            i++;
        }
        return line.substring(0, i);
    }

    private String getLineEnding(String content) {
        return content.contains("\r\n") ? "\r\n" : "\n";
    }
}