package com.buzz.ai.diff;
import java.util.*;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 实现基于模糊匹配的多处搜索替换功能。
 *
 * <p>核心工作流程：
 * 1. 解析输入的差异内容，提取多个SEARCH/REPLACE块
 * 2. 对每个SEARCH块：
 *    - 如果指定了起始行号，先尝试精确匹配
 *    - 如果没有精确匹配或相似度不足，使用模糊搜索算法(middle-out策略)
 *    - 使用Levenshtein距离计算相似度，保留超过阈值的匹配
 * 3. 处理替换内容：
 *    - 保留原始内容的缩进格式
 *    - 处理相对缩进级别
 * 4. 应用替换并维护行号偏移量(由于替换可能导致行数变化)
 *
 * <p>主要特性：
 * - 支持模糊匹配(基于相似度阈值)
 * - 支持指定行号范围进行精确匹配
 * - 自动处理缩进格式
 * - 支持多个SEARCH/REPLACE块批量处理
 *
 * <p>使用示例：
 * <pre>
 * MultiSearchReplace replacer = new MultiSearchReplace(1.0); // 设置相似度阈值
 * DiffResult result = replacer.applyDiff(originalContent, diffContent);
 * </pre>
 *
 * @see #applyDiff(String, String) 核心方法，应用差异内容
 * @see #fuzzySearch(List, String, int, int) 模糊搜索实现
 * @see #getSimilarity(String, String) 相似度计算
 */
public class MultiSearchReplace {

    private static final int BUFFER_LINES = 40; // 模糊搜索时的上下文缓冲行数
    private final double fuzzyThreshold; // 相似度阈值(0-1)

    public MultiSearchReplace(double fuzzyThreshold) {
        this.fuzzyThreshold = fuzzyThreshold;
    }

    /**
     * 计算两个字符串的相似度 (0-1)
     * 使用Levenshtein距离算法，值越小表示越相似
     */
    private static double getSimilarity(String original, String search) {
        if (search.isEmpty()) {
            return 0; // 空搜索内容直接返回0
        }

        if (original.equals(search)) {
            return 1; // 完全匹配返回1
        }

        int dist = levenshteinDistance(original, search);
        int maxLength = Math.max(original.length(), search.length());
        return 1 - (double) dist / maxLength; // 归一化为0-1的值
    }

    /**
     * Levenshtein距离算法实现
     * 计算两个字符串的编辑距离(需要多少次编辑才能使两个字符串相同)
     */
    private static int levenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];

        for (int i = 0; i <= s1.length(); i++) {
            for (int j = 0; j <= s2.length(); j++) {
                if (i == 0) {
                    dp[i][j] = j; // 边界条件：s1为空时
                } else if (j == 0) {
                    dp[i][j] = i; // 边界条件：s2为空时
                } else {
                    // 三种操作的最小值：替换、删除、插入
                    dp[i][j] = min(
                            dp[i - 1][j - 1] + (s1.charAt(i - 1) == s2.charAt(j - 1) ? 0 : 1),
                            dp[i - 1][j] + 1,
                            dp[i][j - 1] + 1
                    );
                }
            }
        }
        return dp[s1.length()][s2.length()];
    }

    private static int min(int a, int b, int c) {
        return Math.min(a, Math.min(b, c));
    }

    // 模糊搜索结果封装
    private static class SearchResult {
        final double bestScore;     // 最佳匹配分数
        final int bestMatchIndex;   // 最佳匹配行索引
        final String bestMatchContent; // 匹配到的内容

        SearchResult(double bestScore, int bestMatchIndex, String bestMatchContent) {
            this.bestScore = bestScore;
            this.bestMatchIndex = bestMatchIndex;
            this.bestMatchContent = bestMatchContent;
        }
    }

    /**
     * 模糊搜索算法 - 从中间向两边搜索(middle-out)
     * @param lines 所有行内容
     * @param searchChunk 要搜索的内容块
     * @param startIndex 搜索起始行
     * @param endIndex 搜索结束行
     */
    private static SearchResult fuzzySearch(List<String> lines, String searchChunk,
                                            int startIndex, int endIndex) {
        double bestScore = 0;
        int bestMatchIndex = -1;
        String bestMatchContent = "";
        int searchLen = searchChunk.split("\r?\n").length; // 搜索块的行数

        // 从中间点开始向两边搜索
        int midPoint = (startIndex + endIndex) / 2;
        int leftIndex = midPoint;
        int rightIndex = midPoint + 1;

        while (leftIndex >= startIndex || rightIndex <= endIndex - searchLen) {
            // 向左搜索
            if (leftIndex >= startIndex) {
                String originalChunk = String.join("\n",
                        lines.subList(leftIndex, leftIndex + searchLen));
                double similarity = getSimilarity(originalChunk, searchChunk);
                if (similarity > bestScore) {
                    bestScore = similarity;
                    bestMatchIndex = leftIndex;
                    bestMatchContent = originalChunk;
                }
                leftIndex--;
            }

            // 向右搜索
            if (rightIndex <= endIndex - searchLen) {
                String originalChunk = String.join("\n",
                        lines.subList(rightIndex, rightIndex + searchLen));
                double similarity = getSimilarity(originalChunk, searchChunk);
                if (similarity > bestScore) {
                    bestScore = similarity;
                    bestMatchIndex = rightIndex;
                    bestMatchContent = originalChunk;
                }
                rightIndex++;
            }
        }

        return new SearchResult(bestScore, bestMatchIndex, bestMatchContent);
    }

    /**
     * 去除转义字符(处理用户输入中的特殊标记)
     */
    private String unescapeMarkers(String content) {
        return content.replace("\\<<<<<<<", "<<<<<<<")
                .replace("\\=======", "=======")
                .replace("\\>>>>>>>", ">>>>>>>")
                .replace("\\-------", "-------")
                .replace("\\:end_line:", ":end_line:")
                .replace("\\:start_line:", ":start_line:");
    }

    /**
     * Check if every line in content has line numbers
     */
    private boolean everyLineHasLineNumbers(String content) {
        if (content.isEmpty()) return false;
        String[] lines = content.split("\\r?\\n");
        for (String line : lines) {
            if (!line.matches("^\\s*\\d+\\s+\\|(?!\\|).*")) {
                return false;
            }
        }
        return true;
    }

    /**
     * Strip line numbers from content
     */
    private String stripLineNumbers(String content, boolean aggressive) {
        String[] lines = content.split("\\r?\\n");
        List<String> processedLines = new ArrayList<>();

        for (String line : lines) {
            Pattern pattern = aggressive ?
                    Pattern.compile("^\\s*(?:\\d+\\s)?\\|\\s(.*)$") :
                    Pattern.compile("^\\s*\\d+\\s+\\|(?!\\|)\\s?(.*)$");
            Matcher matcher = pattern.matcher(line);
            if (matcher.matches()) {
                processedLines.add(matcher.group(1));
            } else {
                processedLines.add(line);
            }
        }

        String lineEnding = content.contains("\r\n") ? "\r\n" : "\n";
        return String.join(lineEnding, processedLines);
    }

    private String stripLineNumbers(String content) {
        return stripLineNumbers(content, false);
    }


    /**
     * 核心方法：应用差异内容
     * @param originalContent 原始内容
     * @param diffContent 差异内容(包含多个SEARCH/REPLACE块)
     */
    public DiffResult applyDiff(String originalContent, String diffContent) {
        List<DiffResult> diffResults = new ArrayList<>();
        // 检测换行符类型(Windows或Unix)
        String lineEnding = originalContent.contains("\r\n") ? "\r\n" : "\n";
        List<String> resultLines = new ArrayList<>(
                Arrays.asList(originalContent.split("\r?\n")));
        int delta = 0;  // 行号偏移量(由于前面替换导致的行数变化)
        int appliedCount = 0; // 成功应用的替换次数

        // 解析diff块的正则表达式(匹配SEARCH/REPLACE块)
        Pattern diffPattern = Pattern.compile(
                "(?:^|\\n)(?<!\\\\)<<<<<<< SEARCH\\s*\\n" +  // SEARCH开始标记
                        "(?:(?:\\:start_line:\\s*(\\d+)\\s*\\n))?" + // 可选起始行号
                        "(?:(?:\\:end_line:\\s*(\\d+)\\s*\\n))?" +   // 可选结束行号
                        "(?:(?<!\\\\)-------\\s*\\n)?" +             // 可选分隔线
                        "([\\s\\S]*?)(?:\\n)?" +                     // 搜索内容
                        "(?:(?<=\\n)(?<!\\\\)=======\\s*\\n)" +      // 分隔符
                        "([\\s\\S]*?)(?:\\n)?" +                     // 替换内容
                        "(?:(?<=\\n)(?<!\\\\)>>>>>>> REPLACE)(?=\\n|$)" // REPLACE结束标记
        );

        // 解析所有替换块
        Matcher matcher = diffPattern.matcher(diffContent);
        List<Replacement> replacements = new ArrayList<>();

        while (matcher.find()) {
            int startLine = matcher.group(1) != null ?
                    Integer.parseInt(matcher.group(1)) : 0;
            String searchContent = matcher.group(3) != null ?
                    matcher.group(3) : "";
            String replaceContent = matcher.group(4) != null ?
                    matcher.group(4) : "";
            replacements.add(new Replacement(startLine, searchContent, replaceContent));
        }

        // 没有找到任何有效替换块
        if (replacements.isEmpty()) {
            return new DiffResult(false, null,
                    "Invalid diff format - missing required sections\n" +
                            "Expected Format: <<<<<<< SEARCH\\n:start_line: start line\\n-------\\n[search content]\\n=======\\n[replace content]\\n>>>>>>> REPLACE",
                    null);
        }

        // 按起始行号排序替换块(确保按顺序处理)
        replacements.sort(Comparator.comparingInt(r -> r.startLine));

        // 处理每个替换块
        for (Replacement replacement : replacements) {
            String searchContent = unescapeMarkers(replacement.searchContent);
            String replaceContent = unescapeMarkers(replacement.replaceContent);
            int startLine = replacement.startLine + (replacement.startLine == 0 ? 0 : delta);

            // Strip line numbers from search and replace content if every line starts with a line number
            boolean hasAllLineNumbers =
                    (everyLineHasLineNumbers(searchContent) && everyLineHasLineNumbers(replaceContent)) ||
                            (everyLineHasLineNumbers(searchContent) && replaceContent.trim().isEmpty());

            if (hasAllLineNumbers && startLine == 0) {
                String firstLine = searchContent.split("\\n")[0];
                startLine = Integer.parseInt(firstLine.split("\\|")[0].trim());
            }

             if (hasAllLineNumbers) {
                searchContent = stripLineNumbers(searchContent);
                replaceContent = stripLineNumbers(replaceContent);
            }

            // 检查搜索和替换内容是否相同
            if (searchContent.equals(replaceContent)) {
                diffResults.add(new DiffResult(false, null,
                        "Search and replace content are identical - no changes would be made", null));
                continue;
            }

            // 分割为行
            List<String> searchLines = searchContent.isEmpty() ?
                    Collections.emptyList() : Arrays.asList(searchContent.split("\r?\n"));
            List<String> replaceLines = replaceContent.isEmpty() ?
                    Collections.emptyList() : Arrays.asList(replaceContent.split("\r?\n"));

            // 检查搜索内容是否为空
            if (searchLines.isEmpty()) {
                diffResults.add(new DiffResult(false, null,
                        "Empty search content is not allowed", null));
                continue;
            }

            int endLine = replacement.startLine + searchLines.size() - 1;
            String searchChunk = String.join("\n", searchLines);

            // 确定搜索范围
            int searchStartIndex = 0;
            int searchEndIndex = resultLines.size();

            int matchIndex = -1;
            double bestMatchScore = 0;
            String bestMatchContent = "";

            // 如果有指定起始行，先尝试精确匹配
            if (startLine > 0) {
                int exactStartIndex = startLine - 1; // 转换为0-based索引
                int exactEndIndex = exactStartIndex + searchLines.size() - 1;

                // 检查是否在有效范围内
                if (exactEndIndex < resultLines.size()) {
                    String originalChunk = String.join("\n",
                            resultLines.subList(exactStartIndex, exactEndIndex + 1));
                    double similarity = getSimilarity(originalChunk, searchChunk);
                    // 如果相似度达到阈值，则使用精确匹配
                    if (similarity >= fuzzyThreshold) {
                        matchIndex = exactStartIndex;
                        bestMatchScore = similarity;
                        bestMatchContent = originalChunk;
                    } else {
                        // 否则扩大搜索范围(添加缓冲行)
                        searchStartIndex = Math.max(0, startLine - (BUFFER_LINES + 1));
                        searchEndIndex = Math.min(resultLines.size(),
                                startLine + searchLines.size() + BUFFER_LINES);
                    }
                }
            }

            // 如果没有找到精确匹配，则进行模糊搜索
            if (matchIndex == -1) {
                SearchResult searchResult = fuzzySearch(resultLines, searchChunk,
                        searchStartIndex, searchEndIndex);
                matchIndex = searchResult.bestMatchIndex;
                bestMatchScore = searchResult.bestScore;
                bestMatchContent = searchResult.bestMatchContent;
            }

            // 检查是否找到足够相似的匹配
            if (matchIndex == -1 || bestMatchScore < fuzzyThreshold) {
                String errorMsg = String.format(
                        "No sufficiently similar match found at line: %d (%.0f%% similar, needs %.0f%%)",
                        startLine, bestMatchScore * 100, fuzzyThreshold * 100);
                diffResults.add(new DiffResult(false, null, errorMsg, null));
                continue;
            }

            // 获取匹配的行内容
            List<String> matchedLines = resultLines.subList(matchIndex,
                    matchIndex + searchLines.size());

            // 保留原始缩进 - 获取每行的缩进空格/制表符
            List<String> originalIndents = matchedLines.stream()
                    .map(line -> {
                        Matcher m = Pattern.compile("^[\\t ]*").matcher(line);
                        return m.find() ? m.group() : "";
                    })
                    .collect(Collectors.toList());

            // 获取搜索内容的缩进
            List<String> searchIndents = searchLines.stream()
                    .map(line -> {
                        Matcher m = Pattern.compile("^[\\t ]*").matcher(line);
                        return m.find() ? m.group() : "";
                    })
                    .collect(Collectors.toList());

            // 处理替换内容的缩进
            List<String> indentedReplaceLines = new ArrayList<>();
            for (int i = 0; i < replaceLines.size(); i++) {
                String line = replaceLines.get(i);
                String matchedIndent = originalIndents.isEmpty() ? "" : originalIndents.get(0);
                String searchBaseIndent = searchIndents.isEmpty() ? "" : searchIndents.get(0);

                // 获取当前行的缩进
                Matcher m = Pattern.compile("^[\\t ]*").matcher(line);
                String currentIndent = m.find() ? m.group() : "";

                // 计算相对缩进级别
                int searchBaseLevel = searchBaseIndent.length();
                int currentLevel = currentIndent.length();
                int relativeLevel = currentLevel - searchBaseLevel;

                // 调整缩进
                String finalIndent;
                if (relativeLevel < 0) {
                    // 如果相对级别为负，从匹配缩进中移除相应数量
                    finalIndent = matchedIndent.substring(0,
                            Math.max(0, matchedIndent.length() + relativeLevel));
                } else {
                    // 如果为正，添加到匹配缩进中
                    finalIndent = matchedIndent + currentIndent.substring(
                            Math.min(searchBaseLevel, currentIndent.length()));
                }

                // 保留原有内容(去除原有缩进后添加新缩进)
                String trimmedLine = line.trim();
                indentedReplaceLines.add(finalIndent + trimmedLine);
            }

            // 构建新内容：匹配前的内容 + 替换内容 + 匹配后的内容
            List<String> newContent = new ArrayList<>();
            newContent.addAll(resultLines.subList(0, matchIndex));
            newContent.addAll(indentedReplaceLines);
            newContent.addAll(resultLines.subList(matchIndex + searchLines.size(), resultLines.size()));

            // 更新结果和偏移量
            resultLines = newContent;
            delta = delta - matchedLines.size() + replaceLines.size();
            appliedCount++;
        }

        // 检查是否有成功的替换
        if (appliedCount == 0) {
            return new DiffResult(false, null, null, diffResults);
        }

        // 返回成功结果
        return new DiffResult(true, String.join(lineEnding, resultLines), null, diffResults);
    }

    // 替换块封装类
    private static class Replacement {
        final int startLine;       // 起始行号
        final String searchContent;  // 搜索内容
        final String replaceContent; // 替换内容

        Replacement(int startLine, String searchContent, String replaceContent) {
            this.startLine = startLine;
            this.searchContent = searchContent;
            this.replaceContent = replaceContent;
        }
    }
}