package com.buzz.ai.diff;

import java.util.ArrayList;
import java.util.List;

public class DiffResult {
    private final boolean success;
    private final String content;
    private final String error;
    private final List<DiffResult> failParts;


    public DiffResult(boolean success, String content, String error) {
        this(success, content, error, null);
    }

    public DiffResult(boolean success, String content) {
        this(success, content, null, null);
    }

    public DiffResult(boolean success, String content, List<DiffResult> failParts) {
        this(success, content, null, failParts);
    }

    public DiffResult(boolean success, String content, String error, List<DiffResult> failParts) {
        this.success = success;
        this.content = content;
        this.error = error;
        this.failParts = failParts;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getContent() {
        return content;
    }

    public String getError() {
        return error;
    }

    public List<DiffResult> getFailParts() {
        return failParts;
    }
}