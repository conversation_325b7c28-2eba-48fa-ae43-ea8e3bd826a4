package com.buzz.ai.diff;

import lombok.Data;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Java implementation of the multi-search-replace diff strategy
 * Translated from TypeScript multi-search-replace.ts
 */
public class MultiSearchReplace_V1 {

    private static final int BUFFER_LINES = 40; // Number of extra context lines to show before and after matches

    private final double fuzzyThreshold;
    private final int bufferLines;

    /**
     * Result class for diff operations
     */
    @Data
    public static class DiffResult {
        public final boolean success;
        public final String content;
        public final String error;
        public final List<DiffResult> failParts;

        public DiffResult(boolean success, String content, String error, List<DiffResult> failParts) {
            this.success = success;
            this.content = content;
            this.error = error;
            this.failParts = failParts;
        }

        public static DiffResult success(String content, List<DiffResult> failParts) {
            return new DiffResult(true, content, null, failParts);
        }

        public static DiffResult failure(String error) {
            return new DiffResult(false, null, error, null);
        }

        public static DiffResult failure(List<DiffResult> failParts) {
            return new DiffResult(false, null, null, failParts);
        }
    }

    /**
     * Result class for fuzzy search operations
     */
    private static class FuzzySearchResult {
        public final double bestScore;
        public final int bestMatchIndex;
        public final String bestMatchContent;

        public FuzzySearchResult(double bestScore, int bestMatchIndex, String bestMatchContent) {
            this.bestScore = bestScore;
            this.bestMatchIndex = bestMatchIndex;
            this.bestMatchContent = bestMatchContent;
        }
    }

    /**
     * Replacement data structure
     */
    private static class Replacement {
        public final int startLine;
        public final String searchContent;
        public final String replaceContent;

        public Replacement(int startLine, String searchContent, String replaceContent) {
            this.startLine = startLine;
            this.searchContent = searchContent;
            this.replaceContent = replaceContent;
        }
    }

    public MultiSearchReplace_V1() {
        this(1.0, BUFFER_LINES);
    }

    public MultiSearchReplace_V1(double fuzzyThreshold, int bufferLines) {
        this.fuzzyThreshold = fuzzyThreshold;
        this.bufferLines = bufferLines;
    }

    /**
     * Calculate similarity between two strings using Levenshtein distance
     */
    private double getSimilarity(String original, String search) {
        // Empty searches are no longer supported
        if (search.isEmpty()) {
            return 0.0;
        }

        // Use normalization to handle smart quotes and other special characters
        String normalizedOriginal = normalizeString(original);
        String normalizedSearch = normalizeString(search);

        if (normalizedOriginal.equals(normalizedSearch)) {
            return 1.0;
        }

        // Calculate Levenshtein distance
        int dist = levenshteinDistance(normalizedOriginal, normalizedSearch);

        // Calculate similarity ratio (0 to 1, where 1 is an exact match)
        int maxLength = Math.max(normalizedOriginal.length(), normalizedSearch.length());
        return 1.0 - (double) dist / maxLength;
    }

    /**
     * Calculate Levenshtein distance between two strings
     */
    private int levenshteinDistance(String s1, String s2) {
        int len1 = s1.length();
        int len2 = s2.length();

        // Create a matrix to store distances
        int[][] dp = new int[len1 + 1][len2 + 1];

        // Initialize first row and column
        for (int i = 0; i <= len1; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= len2; j++) {
            dp[0][j] = j;
        }

        // Fill the matrix
        for (int i = 1; i <= len1; i++) {
            for (int j = 1; j <= len2; j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }

        return dp[len1][len2];
    }

    /**
     * Normalize string by handling smart quotes and other special characters
     */
    private String normalizeString(String str) {
        if (str == null) return "";

        String normalized = str;

        // Replace smart quotes
        normalized = normalized.replace("\u201C", "\""); // Left double quote
        normalized = normalized.replace("\u201D", "\""); // Right double quote
        normalized = normalized.replace("\u2018", "'");  // Left single quote
        normalized = normalized.replace("\u2019", "'");  // Right single quote

        // Replace typographic characters
        normalized = normalized.replace("\u2026", "..."); // Ellipsis
        normalized = normalized.replace("\u2014", "-");   // Em dash
        normalized = normalized.replace("\u2013", "-");   // En dash
        normalized = normalized.replace("\u00A0", " ");   // Non-breaking space

        // Normalize whitespace
        normalized = normalized.replaceAll("\\s+", " ");

        // Trim whitespace
        normalized = normalized.trim();

        return normalized;
    }

    /**
     * Performs a "middle-out" search of lines to find the slice that is most similar to searchChunk
     */
    private FuzzySearchResult fuzzySearch(List<String> lines, String searchChunk, int startIndex, int endIndex) {
        double bestScore = 0.0;
        int bestMatchIndex = -1;
        String bestMatchContent = "";
        int searchLen = searchChunk.split("\\r?\\n").length;

        // Middle-out from the midpoint
        int midPoint = (startIndex + endIndex) / 2;
        int leftIndex = midPoint;
        int rightIndex = midPoint + 1;

        while (leftIndex >= startIndex || rightIndex <= endIndex - searchLen) {
            if (leftIndex >= startIndex) {
                String originalChunk = String.join("\n",
                        lines.subList(leftIndex, Math.min(leftIndex + searchLen, lines.size())));
                double similarity = getSimilarity(originalChunk, searchChunk);
                if (similarity > bestScore) {
                    bestScore = similarity;
                    bestMatchIndex = leftIndex;
                    bestMatchContent = originalChunk;
                }
                leftIndex--;
            }

            if (rightIndex <= endIndex - searchLen) {
                String originalChunk = String.join("\n",
                        lines.subList(rightIndex, Math.min(rightIndex + searchLen, lines.size())));
                double similarity = getSimilarity(originalChunk, searchChunk);
                if (similarity > bestScore) {
                    bestScore = similarity;
                    bestMatchIndex = rightIndex;
                    bestMatchContent = originalChunk;
                }
                rightIndex++;
            }
        }

        return new FuzzySearchResult(bestScore, bestMatchIndex, bestMatchContent);
    }

    /**
     * Unescape markers in content
     */
    private String unescapeMarkers(String content) {
        return content
                .replaceAll("^\\\\<<<<<<<", "<<<<<<<")
                .replaceAll("^\\\\=======", "=======")
                .replaceAll("^\\\\>>>>>>>", ">>>>>>>")
                .replaceAll("^\\\\-------", "-------")
                .replaceAll("^\\\\:end_line:", ":end_line:")
                .replaceAll("^\\\\:start_line:", ":start_line:");
    }

    /**
     * Check if every line in content has line numbers
     */
    private boolean everyLineHasLineNumbers(String content) {
        if (content.isEmpty()) return false;
        String[] lines = content.split("\\r?\\n");
        for (String line : lines) {
            if (!line.matches("^\\s*\\d+\\s+\\|(?!\\|).*")) {
                return false;
            }
        }
        return true;
    }

    /**
     * Strip line numbers from content
     */
    private String stripLineNumbers(String content, boolean aggressive) {
        String[] lines = content.split("\\r?\\n");
        List<String> processedLines = new ArrayList<>();

        for (String line : lines) {
            Pattern pattern = aggressive ?
                    Pattern.compile("^\\s*(?:\\d+\\s)?\\|\\s(.*)$") :
                    Pattern.compile("^\\s*\\d+\\s+\\|(?!\\|)\\s?(.*)$");
            Matcher matcher = pattern.matcher(line);
            if (matcher.matches()) {
                processedLines.add(matcher.group(1));
            } else {
                processedLines.add(line);
            }
        }

        String lineEnding = content.contains("\r\n") ? "\r\n" : "\n";
        return String.join(lineEnding, processedLines);
    }

    private String stripLineNumbers(String content) {
        return stripLineNumbers(content, false);
    }

    /**
     * Main method to apply diff - only accepts originalContent and diffContent parameters
     */
    public DiffResult applyDiff(String originalContent, String diffContent) {
        //System.out.println("applyDiff: diffContent: " + diffContent);

        // Validate marker sequencing (simplified version)
        if (!isValidDiffFormat(diffContent)) {
            return DiffResult.failure("Invalid diff format - missing required sections");
        }

        // Parse diff content using regex
        Pattern diffPattern = Pattern.compile(
                "(?:^|\\n)(?<!\\\\)<<<<<<< SEARCH\\s*\\n" +
                        "((?::start_line:\\s*(\\d+)\\s*\\n))?" +
                        "((?::end_line:\\s*(\\d+)\\s*\\n))?" +
                        "((?<!\\\\)-------\\s*\\n)?" +
                        "([\\s\\S]*?)(?:\\n)?" +
                        "(?:(?<=\\n)(?<!\\\\)=======\\s*\\n)" +
                        "([\\s\\S]*?)(?:\\n)?" +
                        "(?:(?<=\\n)(?<!\\\\)>>>>>>> REPLACE)(?=\\n|$)",
                Pattern.MULTILINE
        );

        Matcher matcher = diffPattern.matcher(diffContent);
        List<Replacement> replacements = new ArrayList<>();

        while (matcher.find()) {
            int startLine = matcher.group(2) != null ? Integer.parseInt(matcher.group(2)) : 0;
            String searchContent = matcher.group(6) != null ? matcher.group(6) : "";
            String replaceContent = matcher.group(7) != null ? matcher.group(7) : "";
            replacements.add(new Replacement(startLine, searchContent, replaceContent));
        }

        if (replacements.isEmpty()) {
            return DiffResult.failure("Invalid diff format - missing required sections");
        }

        // Detect line ending from original content
        String lineEnding = originalContent.contains("\r\n") ? "\r\n" : "\n";
        List<String> resultLines = new ArrayList<>(Arrays.asList(originalContent.split("\\r?\\n")));
        int delta = 0;
        List<DiffResult> diffResults = new ArrayList<>();
        int appliedCount = 0;

        // Sort replacements by start line
        replacements.sort(Comparator.comparingInt(r -> r.startLine));

        for (Replacement replacement : replacements) {
            String searchContent = replacement.searchContent;
            String replaceContent = replacement.replaceContent;
            int startLine = replacement.startLine + (replacement.startLine == 0 ? 0 : delta);

            // First unescape any escaped markers in the content
            searchContent = unescapeMarkers(searchContent);
            replaceContent = unescapeMarkers(replaceContent);

            // Strip line numbers from search and replace content if every line starts with a line number
            boolean hasAllLineNumbers =
                    (everyLineHasLineNumbers(searchContent) && everyLineHasLineNumbers(replaceContent)) ||
                            (everyLineHasLineNumbers(searchContent) && replaceContent.trim().isEmpty());

            if (hasAllLineNumbers && startLine == 0) {
                String firstLine = searchContent.split("\\n")[0];
                startLine = Integer.parseInt(firstLine.split("\\|")[0].trim());
            }

            if (hasAllLineNumbers) {
                searchContent = stripLineNumbers(searchContent);
                replaceContent = stripLineNumbers(replaceContent);
            }

            // Validate that search and replace content are not identical
            if (searchContent.equals(replaceContent)) {
                diffResults.add(DiffResult.failure(
                        "Search and replace content are identical - no changes would be made"));
                continue;
            }

            // Process the replacement
            DiffResult result = processReplacement(resultLines, searchContent, replaceContent, startLine);
            if (result.success) {
                resultLines = new ArrayList<>(Arrays.asList(result.content.split("\\r?\\n")));
                appliedCount++;
            } else {
                diffResults.add(result);
            }
        }

        String finalContent = String.join(lineEnding, resultLines);
        if (appliedCount == 0) {
            return DiffResult.failure(diffResults);
        }

        return DiffResult.success(finalContent, diffResults.isEmpty() ? null : diffResults);
    }

    /**
     * Simple validation for diff format
     */
    private boolean isValidDiffFormat(String diffContent) {
        return diffContent.contains("<<<<<<< SEARCH") &&
                diffContent.contains("=======") &&
                diffContent.contains(">>>>>>> REPLACE");
    }

    /**
     * Process a single replacement operation
     */
    private DiffResult processReplacement(List<String> resultLines, String searchContent,
                                          String replaceContent, int startLine) {
        // Split content into lines
        List<String> searchLines = searchContent.isEmpty() ?
                new ArrayList<>() : Arrays.asList(searchContent.split("\\r?\\n"));
        List<String> replaceLines = replaceContent.isEmpty() ?
                new ArrayList<>() : Arrays.asList(replaceContent.split("\\r?\\n"));

        // Validate that search content is not empty
        if (searchLines.isEmpty()) {
            return DiffResult.failure("Empty search content is not allowed");
        }

        // Initialize search variables
        int matchIndex = -1;
        double bestMatchScore = 0.0;
        String bestMatchContent = "";
        String searchChunk = String.join("\n", searchLines);

        // Determine search bounds
        int searchStartIndex = 0;
        int searchEndIndex = resultLines.size();

        // Validate and handle line range if provided
        if (startLine > 0) {
            // Convert to 0-based index
            int exactStartIndex = startLine - 1;
            int searchLen = searchLines.size();
            int exactEndIndex = exactStartIndex + searchLen - 1;

            // Try exact match first
            if (exactStartIndex >= 0 && exactEndIndex < resultLines.size()) {
                String originalChunk = String.join("\n",
                        resultLines.subList(exactStartIndex, exactEndIndex + 1));
                double similarity = getSimilarity(originalChunk, searchChunk);
                if (similarity >= fuzzyThreshold) {
                    matchIndex = exactStartIndex;
                    bestMatchScore = similarity;
                    bestMatchContent = originalChunk;
                } else {
                    // Set bounds for buffered search
                    searchStartIndex = Math.max(0, startLine - (bufferLines + 1));
                    searchEndIndex = Math.min(resultLines.size(), startLine + searchLines.size() + bufferLines);
                }
            }
        }

        // If no match found yet, try middle-out search within bounds
        if (matchIndex == -1) {
            FuzzySearchResult searchResult = fuzzySearch(resultLines, searchChunk, searchStartIndex, searchEndIndex);
            matchIndex = searchResult.bestMatchIndex;
            bestMatchScore = searchResult.bestScore;
            bestMatchContent = searchResult.bestMatchContent;
        }

        // Try aggressive line number stripping as a fallback
        if (matchIndex == -1 || bestMatchScore < fuzzyThreshold) {
            String aggressiveSearchContent = stripLineNumbers(searchContent, true);
            String aggressiveReplaceContent = stripLineNumbers(replaceContent, true);

            if (!aggressiveSearchContent.isEmpty()) {
                List<String> aggressiveSearchLines = Arrays.asList(aggressiveSearchContent.split("\\r?\\n"));
                String aggressiveSearchChunk = String.join("\n", aggressiveSearchLines);

                FuzzySearchResult searchResult = fuzzySearch(resultLines, aggressiveSearchChunk,
                        searchStartIndex, searchEndIndex);
                if (searchResult.bestMatchIndex != -1 && searchResult.bestScore >= fuzzyThreshold) {
                    matchIndex = searchResult.bestMatchIndex;
                    bestMatchScore = searchResult.bestScore;
                    bestMatchContent = searchResult.bestMatchContent;
                    searchContent = aggressiveSearchContent;
                    replaceContent = aggressiveReplaceContent;
                    searchLines = aggressiveSearchLines;
                    replaceLines = replaceContent.isEmpty() ?
                            new ArrayList<>() : Arrays.asList(replaceContent.split("\\r?\\n"));
                }
            }
        }

        // Check if we found a suitable match
        if (matchIndex == -1 || bestMatchScore < fuzzyThreshold) {
            return DiffResult.failure(String.format(
                    "No sufficiently similar match found (%.0f%% similar, needs %.0f%%)",
                    bestMatchScore * 100, fuzzyThreshold * 100));
        }

        // Apply the replacement with proper indentation handling
        return applyReplacementWithIndentation(resultLines, matchIndex, searchLines, replaceLines);
    }

    /**
     * Apply replacement while preserving indentation
     */
    private DiffResult applyReplacementWithIndentation(List<String> resultLines, int matchIndex,
                                                       List<String> searchLines, List<String> replaceLines) {
        // Get the matched lines from the original content
        List<String> matchedLines = resultLines.subList(matchIndex, matchIndex + searchLines.size());

        // Get the exact indentation of each line
        List<String> originalIndents = new ArrayList<>();
        for (String line : matchedLines) {
            Pattern pattern = Pattern.compile("^[\\t ]*");
            Matcher matcher = pattern.matcher(line);
            originalIndents.add(matcher.find() ? matcher.group() : "");
        }

        // Get the exact indentation of each line in the search block
        List<String> searchIndents = new ArrayList<>();
        for (String line : searchLines) {
            Pattern pattern = Pattern.compile("^[\\t ]*");
            Matcher matcher = pattern.matcher(line);
            searchIndents.add(matcher.find() ? matcher.group() : "");
        }

        // Apply the replacement while preserving exact indentation
        List<String> indentedReplaceLines = new ArrayList<>();
        for (String line : replaceLines) {
            // Get the matched line's exact indentation
            String matchedIndent = originalIndents.isEmpty() ? "" : originalIndents.get(0);

            // Get the current line's indentation relative to the search content
            Pattern pattern = Pattern.compile("^[\\t ]*");
            Matcher matcher = pattern.matcher(line);
            String currentIndent = matcher.find() ? matcher.group() : "";
            String searchBaseIndent = searchIndents.isEmpty() ? "" : searchIndents.get(0);

            // Calculate the relative indentation level
            int searchBaseLevel = searchBaseIndent.length();
            int currentLevel = currentIndent.length();
            int relativeLevel = currentLevel - searchBaseLevel;

            // If relative level is negative, remove indentation from matched indent
            // If positive, add to matched indent
            String finalIndent;
            if (relativeLevel < 0) {
                finalIndent = matchedIndent.substring(0, Math.max(0, matchedIndent.length() + relativeLevel));
            } else {
                finalIndent = matchedIndent +
                        (currentIndent.length() > searchBaseLevel ?
                                currentIndent.substring(searchBaseLevel) : "");
            }

            indentedReplaceLines.add(finalIndent + line.trim());
        }

        // Construct the final content
        List<String> newResultLines = new ArrayList<>();
        newResultLines.addAll(resultLines.subList(0, matchIndex));
        newResultLines.addAll(indentedReplaceLines);
        newResultLines.addAll(resultLines.subList(matchIndex + searchLines.size(), resultLines.size()));

        String finalContent = String.join("\n", newResultLines);
        return DiffResult.success(finalContent, null);
    }
}