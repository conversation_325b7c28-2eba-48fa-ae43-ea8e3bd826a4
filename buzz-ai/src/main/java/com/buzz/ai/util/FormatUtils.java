package com.buzz.ai.util;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.Date;
import java.util.Locale;

public class FormatUtils {

    /**
     * 从java.util.Date获取一年中的第几周
     * @param date java.util.Date对象
     * @return 一年中的周数(1-53)
     */
    public static int getWeekOfYear(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("Date cannot be null");
        }
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return getWeekOfYear(localDate);
    }

    /**
     * 获取指定日期是一年中的第几周
     * @param date 要计算的日期
     * @return 一年中的周数(1-53)
     */
    public static int getWeekOfYear(LocalDate date) {
        if (date == null) {
            throw new IllegalArgumentException("Date cannot be null");
        }
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        return date.get(weekFields.weekOfWeekBasedYear());
    }

    /**
     * 获取当前日期是一年中的第几周
     * @return 当前日期所在的周数(1-53)
     */
    public static int getCurrentWeekOfYear() {
        return getWeekOfYear(LocalDate.now());
    }


    public static void main(String[] args) {

        System.out.println(getCurrentWeekOfYear());
    }


    
}
