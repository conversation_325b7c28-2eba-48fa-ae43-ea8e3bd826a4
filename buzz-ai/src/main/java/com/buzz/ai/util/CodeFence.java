package com.buzz.ai.util;

import com.buzz.ai.tools.ToolDefinition;
import lombok.Data;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
public class CodeFence {
    // 正则表达式用于匹配顶层XML标签
    private static final Pattern ROOT_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)(?=</\\1>|$)", Pattern.DOTALL);
    // 正则表达式用于匹配嵌套的子标签
    private static final Pattern NESTED_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)</\\1>", Pattern.DOTALL);

    // 正则表达式用于匹配Markdown代码块
    private static final Pattern MARKDOWN_CODE_PATTERN = Pattern.compile("```\\s*(\\w*)(?:\\s+([^\\s]+))?\\s*([\\s\\S]*?)```", Pattern.DOTALL);

    private final String name;
    private String text;
    private boolean complete = false;
    private final Map<String, String> attributes = new HashMap<>();
    private final Type type;

    public enum Type {
        PLAIN_TEXT,
        XML,
        CODE
    }

    public CodeFence(String name, boolean complete, Type type) {
        this.name = name;
        this.complete = complete;
        this.type = type;
    }

    public void addAttribute(String key, String value) {
        attributes.put(key, value);
    }

    public String getAttribute(String key) {
        return attributes.get(key);
    }

    private static void processMixedContent(String remainingText, List<CodeFence> chunkList) {
        // 在剩余文本中查找Markdown代码块
        Matcher mdMatcher = MARKDOWN_CODE_PATTERN.matcher(remainingText);
        int mdLastEnd = 0;

        while (mdMatcher.find()) {
            // 添加Markdown前的普通文本
            if (mdMatcher.start() > mdLastEnd) {
                String plainText = remainingText.substring(mdLastEnd, mdMatcher.start());
                if (plainText.trim().length() > 0) {
                    CodeFence codeFence = new CodeFence("", true, Type.PLAIN_TEXT);
                    codeFence.setText(plainText);
                    chunkList.add(codeFence);
                }
            }

            // 添加Markdown代码块
            String language = mdMatcher.group(1);
            String path = mdMatcher.group(2);
            String code = mdMatcher.group(3);
            CodeFence mdChunk = new CodeFence(language, true, Type.CODE);
            mdChunk.setText(code);
            if (path != null) {
                mdChunk.addAttribute("path", path);
            }
            chunkList.add(mdChunk);

            mdLastEnd = mdMatcher.end();
        }

        // 添加最后的普通文本
        if (mdLastEnd < remainingText.length()) {
            String finalText = remainingText.substring(mdLastEnd).trim();

            if (finalText.length() > 0 && !checkMaybeCode(finalText)) {
                CodeFence codeFence = new CodeFence("", true, Type.PLAIN_TEXT);
                codeFence.setText(finalText);
                chunkList.add(codeFence);
            }
        }

    }

    //避免流式渲染的时候把```作为普通文本渲染
    //只要可能是代码块，就不要作为普通文本处理
    private static boolean checkMaybeCode(String text) {
        return text.length() < 3 && text.startsWith("`")
                || (text.length() >= 3 && text.startsWith("```"));
    }

    public static List<CodeFence> parseAll(String content) {
        List<CodeFence> chunkList = new ArrayList<>();
        int lastEnd = 0;

        // 先处理XML标签
        Matcher xmlMatcher = ROOT_TAG_PATTERN.matcher(content);
        while (xmlMatcher.find()) {
            // 添加XML前的普通文本
            if (xmlMatcher.start() > lastEnd) {
                String plainText = content.substring(lastEnd, xmlMatcher.start());
                if (plainText.trim().length() > 0) {
                    processMixedContent(plainText, chunkList);
                }
            }

            // 处理XML标签
            String tagName = xmlMatcher.group(1);
            String tagContent = xmlMatcher.group(2);
            boolean hasEndTag = content.indexOf("</" + tagName + ">", xmlMatcher.start()) != -1;

            CodeFence codeFence = new CodeFence(tagName, hasEndTag, Type.XML);

            // 解析嵌套标签作为属性
            Matcher nestedMatcher = NESTED_TAG_PATTERN.matcher(tagContent);

            while (nestedMatcher.find()) {
                String attrName = nestedMatcher.group(1);
                String attrValue = nestedMatcher.group(2);
                codeFence.addAttribute(attrName, attrValue);
            }

            String text = tagContent;
            if (!tagName.equals(ToolDefinition.THINKING)) {
                text = "<" + tagName + ">\n" + tagContent + (hasEndTag ? "</" + tagName + ">" : "");
            }
            codeFence.setText(text);
            chunkList.add(codeFence);

            if (hasEndTag) {
                lastEnd = content.indexOf("</" + tagName + ">", xmlMatcher.start()) + tagName.length() + 3;
            } else {
                lastEnd = xmlMatcher.end();
            }
        }

        // 处理剩余的文本
        if (lastEnd < content.length() - 1) {
            String remainingText = content.substring(lastEnd).trim();
            if (!remainingText.startsWith("<")) {
                processMixedContent(remainingText, chunkList);
            }

        }

        return chunkList;
    }


}