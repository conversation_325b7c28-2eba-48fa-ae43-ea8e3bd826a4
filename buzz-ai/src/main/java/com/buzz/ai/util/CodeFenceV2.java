package com.buzz.ai.util;


import com.buzz.ai.tools.ToolDefinition;
import lombok.Data;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
public class CodeFenceV2 {
    // 正则表达式用于匹配顶层XML标签
    private static final Pattern ROOT_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)(?=</\\1>|$)", Pattern.DOTALL);
    // 正则表达式用于匹配嵌套的子标签
    private static final Pattern NESTED_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)</\\1>", Pattern.DOTALL);

    private final String name;
    private String text;
    private boolean complete = false;
    private final Map<String, String> attributes = new HashMap<>();

    public CodeFenceV2(String name, boolean complete) {
        this.name = name;
        this.complete = complete;
    }

    public void addAttribute(String key, String value) {
        attributes.put(key, value);
    }

    public String getAttribute(String key) {
        return attributes.get(key);
    }

    public static List<CodeFenceV2> parseAll(String content) {
        List<CodeFenceV2> chunkList = new ArrayList<>();
        Matcher xmlMatcher = ROOT_TAG_PATTERN.matcher(content);

        while (xmlMatcher.find()) {
            String tagName = xmlMatcher.group(1);
            String tagContent = xmlMatcher.group(2);
            boolean hasEndTag = content.indexOf("</" + tagName + ">", xmlMatcher.start()) != -1;

            CodeFenceV2 codeFence = new CodeFenceV2(tagName, hasEndTag);

            // 解析嵌套标签作为属性
            Matcher nestedMatcher = NESTED_TAG_PATTERN.matcher(tagContent);
            StringBuilder plainText = new StringBuilder(tagContent);

            while (nestedMatcher.find()) {
                String attrName = nestedMatcher.group(1);
                String attrValue = nestedMatcher.group(2);
                codeFence.addAttribute(attrName, attrValue);
            }
            String text = null;
            if (tagName.equals(ToolDefinition.THINKING)) {
                text = plainText.toString();
            } else {
                text = "<" + tagName + ">\n" + plainText + (hasEndTag ? "</" + tagName + ">" : "");
            }
            codeFence.setText(text);
            chunkList.add(codeFence);
        }

        return chunkList;
    }
}