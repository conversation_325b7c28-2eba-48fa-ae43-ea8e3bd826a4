package com.buzz.ai.util;


import lombok.Data;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
public class CodeFenceBackup {

    // 正则表达式用于匹配顶层XML标签
    private static final Pattern ROOT_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)(?=</\\1>|$)", Pattern.DOTALL);
    // 正则表达式用于匹配嵌套的子标签
    private static final Pattern NESTED_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)</\\1>", Pattern.DOTALL);
    // 正则表达式用于匹配Markdown代码块
    private static final Pattern MARKDOWN_CODE_PATTERN =
            Pattern.compile("```\\s*(\\w*)(?:\\s+([^\\s\\[\\]{}()]*))?\\s*([\\s\\S]*?)```", Pattern.DOTALL);
    private final String name;
    private String text;
    private boolean complete = false;
    private final Map<String, String> attributes = new HashMap<>();
    private Type type;


    public enum Type {
        PLAIN_TEXT,
        XML,
        CODE
    }

    public CodeFenceBackup(String name, boolean complete, Type type) {
        this.name = name;
        this.complete = complete;
        this.type = type;
    }

    public void addAttribute(String key, String value) {
        attributes.put(key, value);
    }

    public String getAttribute(String key) {
        return attributes.get(key);
    }



    public static List<CodeFenceBackup> parseAll(String content) {
        List<CodeFenceBackup> chunkList = new ArrayList<>();
        int lastEnd = 0;

        // 1. 先处理Markdown代码块
        Matcher mdMatcher = MARKDOWN_CODE_PATTERN.matcher(content);
        while (mdMatcher.find()) {
            // 处理Markdown前的文本（可能包含普通文本和XML）
            if (mdMatcher.start() > lastEnd) {
                String beforeMd = content.substring(lastEnd, mdMatcher.start());
                processMixedContent(beforeMd, chunkList);
            }

            // 解析Markdown块
            String language = mdMatcher.group(1) != null ? mdMatcher.group(1) : "";
            String path = mdMatcher.group(2) != null ? mdMatcher.group(2) : "";
            String code = mdMatcher.group(3) != null ? mdMatcher.group(3).trim() : "";

            // 创建Markdown块
            CodeFenceBackup mdChunk = new CodeFenceBackup(language, true, Type.CODE);
            mdChunk.setText(code);
            if (!path.isEmpty()) {
                mdChunk.addAttribute("path", path);
            }
            chunkList.add(mdChunk);

            lastEnd = mdMatcher.end();
        }

        // 2. 处理最后剩余的文本（可能包含普通文本和XML）
        if (lastEnd < content.length()) {
            String remainingText = content.substring(lastEnd);
            processMixedContent(remainingText, chunkList);
        }

        return chunkList;
    }

    private static void processMixedContent(String content, List<CodeFenceBackup> chunkList) {
        Matcher xmlMatcher = ROOT_TAG_PATTERN.matcher(content);

        while (xmlMatcher.find()) {
            // 处理XML标签
            String tagName = xmlMatcher.group(1);
            String tagContent = xmlMatcher.group(2);
            boolean hasEndTag = content.indexOf("</" + tagName + ">", xmlMatcher.start()) != -1;

            CodeFenceBackup CodeFenceBackup = new CodeFenceBackup(tagName, hasEndTag, Type.XML);

            // 解析嵌套标签作为属性
            Matcher nestedMatcher = NESTED_TAG_PATTERN.matcher(tagContent);
            StringBuilder plainText = new StringBuilder(tagContent);

            while (nestedMatcher.find()) {
                String attrName = nestedMatcher.group(1);
                String attrValue = nestedMatcher.group(2);
                CodeFenceBackup.addAttribute(attrName, attrValue);
            }

            CodeFenceBackup.setText(plainText.toString().trim());
            chunkList.add(CodeFenceBackup);
        }

    }
}

