package com.buzz.ai.tools.command;

import com.buzz.ai.tools.ToolCommand;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Map;

public class ReadFileToolCommand implements ToolCommand {

    private static final Path PROJECT_ROOT = Paths.get("/work/dist/branch/wacai/middleware/hermes-parent4").toAbsolutePath();

    @Override
    public String execute(Map<String, String> attributes) {
         String path = attributes.get("path");

        if (path == null || path.trim().isEmpty()) {
            throw new IllegalArgumentException("File path cannot be empty");
        }

        Path filePath = PROJECT_ROOT.resolve(path.trim());
        if (!Files.exists(filePath)) {
            return "File "+path+" Not Found!  \n";
        }

        StringBuilder output = new StringBuilder();
        output.append("#File:").append(filePath.toAbsolutePath()).append("\n");

        try {
            byte[] content = Files.readAllBytes(filePath);
            if (content.length > 1024 * 1024) {
                content = Arrays.copyOf(content, 1024 * 1024);
                output.append(new String(content)).append("\n[File truncated at 1MB]\n");
            } else {
                output.append(new String(content)).append("\n");
            }
        } catch (IOException e) {
            output.append("[Error reading file content]\n");
        }

        return output.toString();
    }
}
