package com.buzz.ai.tools.command;

import com.buzz.ai.util.CodeFence;
import java.io.IOException;
import java.nio.file.Path;

public class ToolExecutor {

    public static String execute(CodeFence codeFence) throws IOException {
        String name = codeFence.getName().trim();

        String result = switch (name) {
            case "read_file" ->
                    new ReadFileToolCommand().execute(codeFence.getAttributes());
            case "file_list" ->
                    new FileListToolCommand(Path.of("/work/dist/branch/wacai/middleware/hermes-parent")).execute(codeFence.getAttributes());
            case "search_files"->
                    new SearchFilesToolCommand(Path.of("/work/dist/branch/wacai/middleware/hermes-parent4")).execute(codeFence.getAttributes());
            default ->
                    throw new IllegalStateException("Unexpected value: " + codeFence.getName());
        };

        return result;
    }
}
