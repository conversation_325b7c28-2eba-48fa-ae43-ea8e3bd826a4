package com.buzz.ai.tools;

import java.util.List;

public class ToolDefinition {
    public static final String READ_FILE = "read_file";
    public static final String SEARCH_FILES = "search_files";
    public static final String LIST_FILE = "list_files";
    public static final String THINKING = "thinking";
    public static final String ATTEMPT_COMPLETION = "attempt_completion";

    public static final List<String> read_tools = List.of(READ_FILE, SEARCH_FILES, LIST_FILE);

    public static final List<String> write_tools = List.of("write_to_file", "apply_diff", "insert_content");

    //attempt_completion
    public static final List<String> response_tools = List.of("thinking", "attempt_completion");

}
