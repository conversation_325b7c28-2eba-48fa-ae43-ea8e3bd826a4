package com.buzz.ai.devin.command;


import com.buzz.ai.devin.*;

import java.nio.file.Path;
import java.util.List;

public class DevinEngineer {

    public String process(String str) throws Exception {
        StringBuilder out = new StringBuilder();
        List<DevinLang> devinLangList = DevinParser.parseMultiple(str);
        for (DevinLang devinLang : devinLangList) {
            if (devinLang.getType() == 0) {
                Command cmd = createCommand(devinLang.getCommand());
                cmd.setArguments(devinLang.getArg().split(" "));
                cmd.setCode(devinLang.getText());
                out.append(cmd.execute() + "\n");
            } else if (devinLang.getType() == 1) {
                out.append(devinLang.getText() + "\n");
            } else {
                throw new IllegalArgumentException("Unknown devinLang type: " + devinLang.getType());
            }


        }

        return out.toString();
    }

    private Command createCommand(String commandType) {
        switch (commandType) {
            case "dir":
                return new DirCommand(Path.of("/work/dist/branch/wacai/middleware/hermes-parent"));
            case "file":
                return new FileCommand();
            case "localSearch":
                return new LocalSearchCommand();
            default:
                throw new IllegalArgumentException("Unknown command: " + commandType);
        }
    }

}
