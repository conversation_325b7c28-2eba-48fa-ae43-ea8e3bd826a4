package com.buzz.ai.devin;


import java.util.ArrayList;
import java.util.List;

public class DevinParser {

    public static List<DevinLang> parseMultiple(String input) {
        if (input == null || input.trim().isEmpty()) {
            throw new IllegalArgumentException("Input cannot be empty");
        }

        List<DevinLang> commands = new ArrayList<>();
        String[] lines = input.split("\n");

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            // 跳过空行
            if (line.isEmpty()) {
                continue;
            }

            // 检测到新命令
            if (line.startsWith("/")) {
                // 解析命令和参数
                int colonIndex = line.indexOf(":");
                if (colonIndex == -1) {
                    throw new IllegalArgumentException("Invalid command format at line " + (i + 1)+ " line:"+line);
                }

                String command = line.substring(1, colonIndex);
                String arg = line.substring(colonIndex + 1);

                // 向前看一行，检查是否是多行命令（是否有代码块）
                String code = null;
                if (i + 1 < lines.length && lines[i + 1].trim().equals("```")) {
                    StringBuilder codeBuilder = new StringBuilder();
                    i += 2; // 跳过 ``` 行

                    // 收集代码块内容直到遇到结束的 ```
                    while (i < lines.length && !lines[i].trim().equals("```")) {
                        codeBuilder.append(lines[i]).append("\n");
                        i++;
                    }

                    if (i >= lines.length) {
                        throw new IllegalArgumentException("Unclosed code block for command: " + command);
                    }

                    code = codeBuilder.toString();
                }

                commands.add(new DevinLang(command, 0, arg, code));
            } else {
                commands.add(new DevinLang.DevinLangBuilder().type(1).text(line).build());
            }
        }

        return commands;
    }
}
