package com.buzz.ai.devin;

import com.buzz.ai.devin.command.Command;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

//支持从某个子模块搜索：/localSearch:blog | hermes-parent
public class LocalSearchCommand implements Command {
    private static final Path PROJECT_ROOT = Paths.get("/work/dist/branch/wacai/middleware/hermes-parent4").toAbsolutePath();
    private static final PathMatcher HIDDEN_DIR_MATCHER = FileSystems.getDefault().getPathMatcher("glob:**/.*/**");

    private String keyword;

    @Override
    public String execute() throws IOException {

        if (keyword == null || keyword.trim().isEmpty()) {
            throw new IllegalArgumentException("Search keyword cannot be empty");
        }

        List<String> matches = searchFiles(keyword.toLowerCase());
        return formatResults(matches);
    }

    @Override
    public void setArguments(String[] args) {
        this.keyword = args[0];
    }

    private List<String> searchFiles(String keyword) throws IOException {
        List<String> matches = new ArrayList<>();
        Files.walkFileTree(PROJECT_ROOT, new SimpleFileVisitor<>() {
            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) {
                if (shouldSkipDirectory(dir)) {
                    return FileVisitResult.SKIP_SUBTREE;
                }
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (isTextFile(file) && matchesKeyword(file, keyword)) {
                    matches.add(PROJECT_ROOT.relativize(file).toString());
                }
                return FileVisitResult.CONTINUE;
            }
        });
        return matches;
    }

    private boolean shouldSkipDirectory(Path dir) {
        String dirName = dir.getFileName().toString();
        if(dirName.startsWith(".")){
            return true;
        }
        return HIDDEN_DIR_MATCHER.matches(dir) ||
                dirName.equals("node_modules") ||
                dirName.equals("build") ||
                dirName.equals("target") ||
                dirName.equals(".git") ||
                dirName.equals(".idea");
    }

    private boolean isTextFile(Path file) {
        String fileName = file.getFileName().toString().toLowerCase();
        return !fileName.endsWith(".class") &&
                !fileName.endsWith(".jar") &&
                !fileName.endsWith(".war") &&
                !fileName.endsWith(".ear") &&
                !fileName.endsWith(".zip") &&
                !fileName.endsWith(".exe") &&
                !fileName.endsWith(".dll");
    }

    private boolean matchesKeyword(Path file, String keyword) throws IOException {
        String fileName = file.getFileName().toString().toLowerCase();
        if (fileName.contains(keyword)) {
            return true;
        }

        // 只读取文件的前1MB来搜索，避免处理大文件
        try {
            byte[] content = Files.readAllBytes(file);
            if (content.length > 1024 * 1024) {
                content = Arrays.copyOf(content, 1024 * 1024);
            }
            return new String(content).toLowerCase().contains(keyword);
        } catch (IOException e) {
            return false;
        }
    }

    private String formatResults(List<String> matches) throws IOException {
        if (matches.isEmpty()) {
            return "No matches found.\n";
        }

        StringBuilder output = new StringBuilder();
        for (String match : matches) {
            Path filePath = PROJECT_ROOT.resolve(match);
            output.append("#").append(filePath.toAbsolutePath()).append("\n");
//            try {
//                byte[] content = Files.readAllBytes(filePath);
//                if (content.length > 1024 * 1024) {
//                    content = Arrays.copyOf(content, 1024 * 1024);
//                    output.append(new String(content)).append("\n[File truncated at 1MB]\n\n");
//                } else {
//                    output.append(new String(content)).append("\n\n");
//                }
//            } catch (IOException e) {
//                output.append("[Error reading file content]\n\n");
//            }
        }
        return output.toString();
    }
}
