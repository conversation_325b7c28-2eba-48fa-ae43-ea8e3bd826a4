package com.buzz.ai.devin;

import com.buzz.ai.devin.command.Command;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

public class DirCommand implements Command {
    private static final Set<String> EXCLUDED_DIRS = Set.of(
            "build", "target", "node_modules"
    );

    private Path path;
    private boolean recursive = false;
    private Path basePath;
    private final StringBuilder output = new StringBuilder();
    private Set<Path> gitIgnoredPaths;
    private static final int MAX_ENTRIES = 200;

    private int entryCount = 0;


    public DirCommand(Path basePath) {
        this.basePath = basePath;
    }


    @Override
    public void setArguments(String[] arguments) {
        if (arguments == null || arguments.length == 0) {
            throw new IllegalArgumentException("Path argument is required");
        }
        // 添加对"."的判断
        if (".".equals(arguments[0])) {
            this.path = basePath;
        } else {
            this.path = basePath.resolve(arguments[0]);
        }
        if (arguments.length > 1) {
            // Fixed recursive parameter parsing
            this.recursive = Boolean.parseBoolean(arguments[1]);
        }
        try {
            this.gitIgnoredPaths = loadGitIgnored();
        } catch (IOException e) {
            this.gitIgnoredPaths = new HashSet<>();
        }
    }


    @Override
    public String execute() throws IOException {
        output.setLength(0);
        // Reset counter for each execution
        entryCount = 0;
        if (!Files.exists(basePath)) {
            output.append("/dir:" + path + " not found!\n");
            return output.toString();
        }
        output.append(path).append("/\n");
        listDirectory(path, "");
        return output.toString();
    }

    private Set<Path> loadGitIgnored() throws IOException {
        Set<Path> ignored = new HashSet<>();
        Path gitIgnore = basePath.resolve(".gitignore");
        if (Files.exists(gitIgnore)) {
            List<String> patterns = Files.readAllLines(gitIgnore);
            for (String pattern : patterns) {
                if (!pattern.startsWith("#") && !pattern.isEmpty()) {
                    ignored.add(basePath.resolve(pattern));
                }
            }
        }
        return ignored;
    }


    private boolean shouldSkipDirectory(Path path) {
        String name = path.getFileName().toString();
        return name.startsWith(".") || EXCLUDED_DIRS.contains(name) || gitIgnoredPaths.contains(path);
    }

    private void listDirectory(Path path, String indent) throws IOException {
        if (shouldSkipDirectory(path)) {
            return;
        }
        File[] files = path.toFile().listFiles();
        if (files == null) {
            return;
        }
        Arrays.sort(files, Comparator.comparing(File::getName));
        for (int i = 0; i < files.length; i++) {
            if (entryCount >= MAX_ENTRIES) {
                output.append(indent).append("└── (File list truncated. Use dir on specific subdirectories if you need to explore further.)\n");
                return;
            }
            File file = files[i];
            if (file.getName().startsWith(".") || EXCLUDED_DIRS.contains(file.getName())) {
                continue;
            }
            boolean isLast = (i == files.length - 1);
            String prefix = indent + (isLast ? "└── " : "├── ");
            if (file.isFile()) {
                output.append(prefix).append(file.getName()).append("\n");
                entryCount++;
            } else if (file.isDirectory()) {
                output.append(prefix).append(file.getName()).append("/\n");
                entryCount++;
                if (recursive) {
                    // Only recurse if recursive flag is true
                    String newIndent = indent + (isLast ? "    " : "│   ");
                    listDirectory(file.toPath(), newIndent);
                }
            }
        }
    }


}
