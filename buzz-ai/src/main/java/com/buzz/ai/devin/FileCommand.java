package com.buzz.ai.devin;

import com.buzz.ai.devin.command.Command;

import java.io.IOException;
import java.nio.file.*;
import java.util.Arrays;


public class FileCommand implements Command {
    private static final Path PROJECT_ROOT = Paths.get("/work/dist/branch/wacai/middleware/hermes-parent4").toAbsolutePath();

    private String file;
    @Override
    public String execute() throws IOException {
        if (file == null || file.trim().isEmpty()) {
            throw new IllegalArgumentException("File path cannot be empty");
        }

        Path filePath = PROJECT_ROOT.resolve(file.trim());
        if (!Files.exists(filePath)) {
            return "/file:"+file+" Not Found! 建议检查路径，通过/dir:.可获取整个项目路径 \n";
        }

        StringBuilder output = new StringBuilder();
        output.append("#File:").append(filePath.toAbsolutePath()).append("\n");

        try {
            byte[] content = Files.readAllBytes(filePath);
            if (content.length > 1024 * 1024) {
                content = Arrays.copyOf(content, 1024 * 1024);
                output.append(new String(content)).append("\n[File truncated at 1MB]\n");
            } else {
                output.append(new String(content)).append("\n");
            }
        } catch (IOException e) {
            output.append("[Error reading file content]\n");
        }

        return output.toString();
    }

    @Override
    public void setArguments(String[] args) {
        this.file = args[0];
    }
}