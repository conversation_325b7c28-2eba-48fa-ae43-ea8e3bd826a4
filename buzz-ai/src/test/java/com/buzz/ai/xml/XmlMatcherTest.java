package com.buzz.ai.xml;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

public class XmlMatcherTest {

    @Test
    public void testBasicXmlMatching() {
        XmlMatcher<XmlMatcherResult> matcher = new XmlMatcher<>("div");
        
        // Process XML in chunks
        matcher.update("<div>");
        List<XmlMatcherResult> results = matcher.finalChunk("Hello</div>");
        
        assertEquals(1, results.size());
        assertTrue(results.get(0).isMatched());
        assertEquals("Hello", results.get(0).getData());
    }
    
    @Test
    public void testNestedTags() {
        XmlMatcher<XmlMatcherResult> matcher = new XmlMatcher<>("div");
        
        List<XmlMatcherResult> results = matcher.finalChunk("<div>Outer<div>Inner</div>End</div>");
        System.out.println(results.get(0).getData());

        assertEquals(3, results.size());
        assertTrue(results.get(0).isMatched());
        assertEquals("Outer", results.get(0).getData());
        
        assertTrue(results.get(1).isMatched());
        assertEquals("Inner", results.get(1).getData());
        
        assertTrue(results.get(2).isMatched());
        assertEquals("End", results.get(2).getData());
    }
    
    @Test
    public void testMultipleUpdates() {
        XmlMatcher<XmlMatcherResult> matcher = new XmlMatcher<>("code");
        
        matcher.update("<code>");
        matcher.update("function test() {");
        matcher.update("  console.log('hello');");
        List<XmlMatcherResult> results = matcher.finalChunk("}</code>");
        
        assertEquals(1, results.size());
        assertTrue(results.get(0).isMatched());
        assertEquals("function test() {  console.log('hello');}", results.get(0).getData());
    }
    
    @Test
    public void testWithTransform() {
        // Create a matcher with a transform function that converts the result
        XmlMatcher<String> matcher = new XmlMatcher<>("pre", 
            result -> result.isMatched() ? result.getData().toUpperCase() : result.getData());
        
        List<String> results = matcher.finalChunk("Text before <pre>code sample</pre> text after");
        
        assertEquals(3, results.size());
        assertEquals("Text before ", results.get(0));
        assertEquals("CODE SAMPLE", results.get(1));
        assertEquals(" text after", results.get(2));
    }
    
    @Test
    public void testWithPosition() {
        // Test with position parameter to start matching from a specific position
        XmlMatcher<XmlMatcherResult> matcher = new XmlMatcher<>("span", null, 20);
        
        List<XmlMatcherResult> results = matcher.finalChunk(
            "Ignore this <span>tag</span> but match <span>this one</span>");
        
        assertEquals(3, results.size());
        assertFalse(results.get(0).isMatched());
        assertEquals("Ignore this <span>tag</span> but match ", results.get(0).getData());
        
        assertTrue(results.get(1).isMatched());
        assertEquals("this one", results.get(1).getData());
        
        assertFalse(results.get(2).isMatched());
        assertEquals("", results.get(2).getData());
    }
    
    @Test
    public void testWithAttributes() {
        XmlMatcher<XmlMatcherResult> matcher = new XmlMatcher<>("div");
        
        List<XmlMatcherResult> results = matcher.finalChunk(
            "<div class=\"container\">Content with attributes</div>");
        
        assertEquals(1, results.size());
        assertTrue(results.get(0).isMatched());
        assertEquals("Content with attributes", results.get(0).getData());
    }
    
    @Test
    public void testIncompleteXml() {
        XmlMatcher<XmlMatcherResult> matcher = new XmlMatcher<>("p");
        
        List<XmlMatcherResult> results = matcher.finalChunk("<p>Incomplete XML");
        
        assertEquals(1, results.size());
        assertTrue(results.get(0).isMatched());
        assertEquals("Incomplete XML", results.get(0).getData());
    }
}