package com.buzz.ai;

import org.junit.jupiter.api.Test;
import com.buzz.ai.agent.*;

public class AgentTest {

    private Agent agent = new Agent();

    @Test
    public void testProjectSummary(){
        agent.submit("这个项目干嘛的");
    }

    @Test
    public void testCase1(){
        agent.submit("优化 ProducerController，为发送消息增加 header，并设置到 kafka");
    }

    @Test
    public void testCase2(){
        agent.submit("为 HermesMessage 新增一个header字段，并修改相关方法");
    }

    @Test
    public void testCase3(){
        agent.submit("为 HermesMessage 创建一个单元测试，使用junit4，简单一点");
    }


    @Test
    public void testCase4(){
        agent.submit(" 为当前应用接入dubbo");
    }
}
