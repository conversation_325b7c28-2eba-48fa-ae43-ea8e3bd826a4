package com.buzz.ai;

import com.buzz.ai.apply.Code;
import com.buzz.ai.apply.SmartMerger;
import org.junit.Test;

public class SmartMergerTest {

    @Test
    public void test101(){
        String result = new SmartMerger().merge(Code.ProducerControllerOriginal, Code.ProducerControllerLLM);
        System.out.println(result);
    }

    @Test
    public void test102(){
        String result = new SmartMerger().merge(Code.hermesMessageOriginal, Code.hermesMessageLLM);
        System.out.println(result);
    }


    @Test
    public void test103(){
        String result = new SmartMerger().merge(Code.StoppedPanel_Original, Code.StoppedPanel_LLM);
        System.out.println(result);
    }

    @Test
    public void test104(){
        String result = new SmartMerger().merge(Code.StreamTestCodeOriginal, Code.StreamTestCodeLLM);
        System.out.println(result);
    }

    //接口合并
    @Test
    public void test5(){
        String result = new SmartMerger().merge(Code.InterfaceOriginal, Code.InterfaceLLM);
        System.out.println(result);
    }
}
