package com.buzz.ai.llm;

import com.buzz.ai.util.AutoRequestSubscriber;
import org.junit.Test;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.SubmissionPublisher;

public class LLMProviderTest {

    private LLMProvider llmProvider = new DefaultLLMProvider();

    @Test
    public void test101() throws InterruptedException {
        SubmissionPublisher<String> flow = llmProvider.stream("你负责合并用户的代码",
                List.of(new ChatMessage("user","你是谁？你是什么大模型")));

        CountDownLatch latch = new CountDownLatch(1);

        flow.subscribe(new AutoRequestSubscriber<String>() {
            @Override
            protected void handleItem(String item) {
                System.out.print(item);
            }
            public void onComplete() {
                System.out.println();
                System.out.println("onComplete");
                latch.countDown();
            }
            public void onError(Throwable e){
                e.printStackTrace();
            }
        });

        latch.await();
        System.out.println("end");
    }
}
