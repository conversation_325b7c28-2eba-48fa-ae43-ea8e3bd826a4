package com.buzz.ai;

import com.buzz.ai.apply.Code;
import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.printer.lexicalpreservation.LexicalPreservingPrinter;
import org.junit.Test;

import java.io.IOException;
import java.util.List;

public class JavaParserTest {


    @Test
    public void test101() throws IOException {
        // 1. 解析Java文件

        String code = Code.StreamTestCodeLLM;
        CompilationUnit cu =  LexicalPreservingPrinter.setup(StaticJavaParser.parse(code));
        // 2. 获取所有方法声明
        List<MethodDeclaration> methods = cu.findAll(MethodDeclaration.class);
        // 3. 遍历并输出方法体
        for (MethodDeclaration method : methods) {
            System.out.println("Method: " + method.getName());
            System.out.println("Body:");

            String formattedBody = LexicalPreservingPrinter.print(method.getBody().get());
            System.out.println(formattedBody);


            System.out.println("---------------------");
        }
    }

}
