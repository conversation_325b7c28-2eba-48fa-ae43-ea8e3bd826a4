package com.buzz.ai;

import org.junit.Test;

public class TempTest {


    @Test
    public void test1() {
        String fileContent = """
                <file><path>hermes-proxy/pom.xml</path>\n<content lines=\"1-153\">\n  1 | <?xml version=\"1.0\" encoding=\"UTF-8\"?>\n  2 | <project xmlns=\"http://maven.apache.org/POM/4.0.0\"\n  3 |          xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n  4 |          xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n  5 |     <parent>\n  6 |         <artifactId>hermes-parent</artifactId>\n  7 |         <groupId>com.wacai.middleware</groupId>\n  8 |         <version>4.0-SNAPSHOT</version>\n  9 |     </parent>\n 10 |     <modelVersion>4.0.0</modelVersion>\n 11 | \n 12 |     <artifactId>hermes-proxy</artifactId>\n 13 | \n 14 |     <properties>\n 15 |         <springboot.version>2.2.6.RELEASE</springboot.version>\n 16 |     </properties>\n 17 | \n 18 |     <dependencies>\n 19 |         <dependency>\n 20 |             <groupId>com.wacai.middleware</groupId>\n 21 |             <artifactId>hermes-api</artifactId>\n 22 |         </dependency>\n 23 |         <dependency>\n 24 |             <groupId>com.wacai.middleware</groupId>\n 25 |             <artifactId>hermes-core</artifactId>\n 26 |         </dependency>\n 27 | \n 28 |         <dependency>\n 29 |             <groupId>io.netty</groupId>\n 30 |             <artifactId>netty-all</artifactId>\n 31 |         </dependency>\n 32 | \n 33 |         <dependency>\n 34 |             <groupId>org.apache.kafka</groupId>\n 35 |             <artifactId>kafka-clients</artifactId>\n 36 |         </dependency>\n 37 | \n 38 |         <dependency>\n 39 |             <groupId>ch.qos.logback</groupId>\n 40 |             <artifactId>logback-classic</artifactId>\n 41 |             <version>1.2.3</version>\n 42 |             <scope>compile</scope>\n 43 |         </dependency>\n 44 | \n 45 |         <dependency>\n 46 |             <groupId>com.wacai</groupId>\n 47 |             <artifactId>redis-client-all-in</artifactId>\n 48 |             <version>3.14.2</version>\n 49 |             <exclusions>\n 50 |                 <exclusion>\n 51 |                     <groupId>log4j</groupId>\n 52 |                     <artifactId>log4j</artifactId>\n 53 |                 </exclusion>\n 54 |             </exclusions>\n 55 |         </dependency>\n 56 | \n 57 |         <dependency>\n 58 |             <groupId>com.google.inject</groupId>\n 59 |             <artifactId>guice</artifactId>\n 60 |             <version>5.0.1</version>\n 61 |         </dependency>\n 62 | \n 63 |         <!--注意：之前hermes使用的zk server为3.4，不能依赖高版本的curator -->\n 64 |         <dependency>\n 65 |             <groupId>org.apache.curator</groupId>\n 66 |             <artifactId>curator-framework</artifactId>\n 67 |         </dependency>\n 68 | \n 69 |         <dependency>\n 70 |             <groupId>org.apache.curator</groupId>\n 71 |             <artifactId>curator-recipes</artifactId>\n 72 |             <version>2.13.0</version>\n 73 |         </dependency>\n 74 | \n 75 |         <dependency>\n 76 |             <groupId>io.jsonwebtoken</groupId>\n 77 |             <artifactId>jjwt</artifactId>\n 78 |         </dependency>\n 79 | \n 80 |         <!--\n 81 |         <dependency>\n 82 |             <groupId>org.springframework.boot</groupId>\n 83 |             <artifactId>spring-boot-starter-actuator</artifactId>\n 84 |             <version>${springboot.version}</version>\n 85 |         </dependency>\n 86 |          -->\n 87 |     </dependencies>\n 88 | \n 89 | \n 90 |     <build>\n 91 |         <plugins>\n 92 |             <plugin>\n 93 |                 <groupId>org.springframework.boot</groupId>\n 94 |                 <artifactId>spring-boot-maven-plugin</artifactId>\n 95 |                 <version>${springboot.version}</version>\n 96 |                 <configuration>\n 97 |                     <!-- 工程主入口-->\n 98 |                     <mainClass>com.wacai.hermes.proxy.HermesProxyApplication</mainClass>\n 99 |                 </configuration>\n100 |                 <executions>\n101 |                     <execution>\n102 |                         <goals>\n103 |                             <goal>repackage</goal>\n104 |                         </goals>\n105 |                     </execution>\n106 |                 </executions>\n107 |             </plugin>\n108 |             <plugin>\n109 |                 <groupId>org.apache.maven.plugins</groupId>\n110 |                 <artifactId>maven-compiler-plugin</artifactId>\n111 |                 <version>3.7.0</version>\n112 |                 <configuration>\n113 |                     <source>1.8</source>\n114 |                     <target>1.8</target>\n115 |                     <encoding>UTF-8</encoding>\n116 |                     <compilerArgument>-parameters</compilerArgument>\n117 |                 </configuration>\n118 |             </plugin>\n119 |             <plugin>\n120 |                 <groupId>org.apache.maven.plugins</groupId>\n121 |                 <artifactId>maven-source-plugin</artifactId>\n122 |                 <version>3.0.1</version>\n123 |                 <executions>\n124 |                     <execution>\n125 |                         <id>attach-sources</id>\n126 |                         <goals>\n127 |                             <goal>jar</goal>\n128 |                         </goals>\n129 |                     </execution>\n130 |                 </executions>\n131 |             </plugin>\n132 |             <plugin>\n133 |                 <groupId>org.apache.maven.plugins</groupId>\n134 |                 <artifactId>maven-surefire-plugin</artifactId>\n135 |                 <configuration>\n136 |                     <skip>true</skip>\n137 |                 </configuration>\n138 |             </plugin>\n139 |         </plugins>\n140 |         <resources>\n141 |             <resource>\n142 |                 <directory>src/main/java</directory>\n143 |                 <includes>\n144 |                     <include>**/*.xml</include>\n145 |                 </includes>\n146 |             </resource>\n147 |             <resource>\n148 |                 <directory>src/main/resources</directory>\n149 |             </resource>\n150 |         </resources>\n151 |     </build>\n152 | \n153 | </project>\n</content>\n</file>
                """;
        System.out.println(fileContent);

        String codeBlock = """
                <apply_diff>\n<path>hermes-proxy/pom.xml</path>\n<diff>\n<<<<<<< SEARCH\n:start_line:14\n-------\n    <properties>\n        <springboot.version>2.2.6.RELEASE</springboot.version>\n    </properties>\n=======\n    <properties>\n        <springboot.version>2.2.6.RELEASE</springboot.version>\n        <dubbo.version>2.7.15</dubbo.version>\n        <nacos.version>1.4.1</nacos.version>\n    </properties>\n>>>>>>> REPLACE\n\n<<<<<<< SEARCH\n:start_line:87\n-------\n    </dependencies>\n=======\n        <!-- Dubbo dependencies -->\n        <dependency>\n            <groupId>org.apache.dubbo</groupId>\n            <artifactId>dubbo-spring-boot-starter</artifactId>\n            <version>${dubbo.version}</version>\n        </dependency>\n        \n        <!-- Dubbo Registry Nacos -->\n        <dependency>\n            <groupId>org.apache.dubbo</groupId>\n            <artifactId>dubbo-registry-nacos</artifactId>\n            <version>${dubbo.version}</version>\n        </dependency>\n        \n        <!-- Nacos Client -->\n        <dependency>\n            <groupId>com.alibaba.nacos</groupId>\n            <artifactId>nacos-client</artifactId>\n            <version>${nacos.version}</version>\n        </dependency>\n    </dependencies>\n>>>>>>> REPLACE\n</diff>\n</apply_diff>
                """;
        //System.out.println(codeBlock);
    }

    @Test
    public void test2() {
        String message = """
                <file><path>hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>\n<content lines=\"1-140\">\n  1 | package com.wacai.hermes.message;\n  2 | \n  3 | import com.wacai.hermes.util.StringUtils;\n  4 | import lombok.Getter;\n  5 | import lombok.Setter;\n  6 | \n  7 | import java.util.Arrays;\n  8 | import java.util.HashMap;\n  9 | import java.util.Map;\n 10 | import java.util.Objects;\n 11 | import java.util.UUID;\n 12 | \n 13 | import static com.wacai.hermes.constants.HermesHeader.TAG;\n 14 | \n 15 | @Getter\n 16 | @Setter\n 17 | public class HermesMessage {\n 18 | \n 19 |     private String topic;\n 20 |     private Integer partition;\n 21 |     private byte[] key;\n 22 |     private byte[] data;\n 23 |     private long timestamp;\n 24 |     private UUID messageId = UUID.randomUUID();\n 25 | \n 26 |     public HermesMessage() {\n 27 |     }\n 28 | \n 29 |     public HermesMessage(byte[] key, byte[] data) {\n 30 |         this.key = key;\n 31 |         this.data = data;\n 32 |     }\n 33 | \n 34 |     public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {\n 35 |         this.topic = topic;\n 36 |         this.partition = partition;\n 37 |         this.key = key;\n 38 |         this.data = data;\n 39 |         this.timestamp = timestamp;\n 40 |     }\n 41 | \n 42 |     public static Builder builder() {\n 43 |         return new Builder();\n 44 |     }\n 45 | \n 46 |     public long getPayLoad() {\n 47 |         long size = 0;\n 48 |         if (key != null) {\n 49 |             size += key.length;\n 50 |         }\n 51 |         if (data != null) {\n 52 |             size += data.length;\n 53 |         }\n 54 |         return size;\n 55 |     }\n 56 | \n 57 |     public String getTraceId() {\n 58 |         return null;\n 59 |     }\n 60 | \n 61 | \n 62 |     @Override\n 63 |     public boolean equals(Object o) {\n 64 |         if (this == o) {\n 65 |             return true;\n 66 |         }\n 67 |         if (o == null || getClass() != o.getClass()) {\n 68 |             return false;\n 69 |         }\n 70 |         HermesMessage message = (HermesMessage)o;\n 71 |         return Objects.equals(partition, message.partition) && timestamp == message.timestamp\n 72 |                 && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(\n 73 |                 messageId, message.messageId);\n 74 |     }\n 75 | \n 76 |     @Override\n 77 |     public int hashCode() {\n 78 | \n 79 |         int result = Objects.hash( topic, partition, timestamp, messageId);\n 80 |         result = 31 * result + Arrays.hashCode(key);\n 81 |         result = 31 * result + Arrays.hashCode(data);\n 82 |         return result;\n 83 |     }\n 84 | \n 85 |     @Override\n 86 |     public String toString() {\n 87 |         return \"HermesMessage:{\" + messageId.toString() + (key == null ? \"\" : \",key=\" + new String(key)) + \"}\";\n 88 |     }\n 89 | \n 90 |     public static class Builder {\n 91 |         private String topic;\n 92 |         private Integer partition;\n 93 |         private byte[] key;\n 94 |         private byte[] data;\n 95 |         private Map<String, byte[]> headers = new HashMap<>();\n 96 |         private long timestamp;\n 97 | \n 98 |         public Builder setHeaders(Map<String, byte[]> headers) {\n 99 |             // headers.entrySet().removeIf(entry -> entry.getKey().startsWith(\"hermes.\"));\n100 |             this.headers.putAll(headers);\n101 |             return this;\n102 |         }\n103 | \n104 |         public Builder setTopic(String topic) {\n105 |             this.topic = topic;\n106 |             return this;\n107 |         }\n108 | \n109 |         public Builder setPartition(Integer partition) {\n110 |             this.partition = partition;\n111 |             return this;\n112 |         }\n113 | \n114 |         public Builder setKey(byte[] key) {\n115 |             this.key = key;\n116 |             return this;\n117 |         }\n118 | \n119 |         public Builder setData(byte[] data) {\n120 |             this.data = data;\n121 |             return this;\n122 |         }\n123 | \n124 |         public Builder setTimestamp(long timestamp) {\n125 |             this.timestamp = timestamp;\n126 |             return this;\n127 |         }\n128 | \n129 |         public Builder setTag(String tag) {\n130 |             if (null != tag && !\"\".equals(tag = tag.trim())) {\n131 |                 this.headers.put(TAG, tag.getBytes());\n132 |             }\n133 |             return this;\n134 |         }\n135 | \n136 |         public HermesMessage build() {\n137 |             return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);\n138 |         }\n139 |     }\n140 | }\n</content>\n</file>
                """;
        System.out.println(message);


        String message2 = """
                <apply_diff>\n<path>hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java</path>\n<diff>\n<<<<<<< SEARCH\n:start_line:17\n-------\n@Getter\n@Setter\npublic class HermesMessage {\n\n    private String topic;\n    private Integer partition;\n    private byte[] key;\n    private byte[] data;\n    private long timestamp;\n    private UUID messageId = UUID.randomUUID();\n=======\n@Getter\n@Setter\npublic class HermesMessage {\n\n    private String topic;\n    private Integer partition;\n    private byte[] key;\n    private byte[] data;\n    private long timestamp;\n    private UUID messageId = UUID.randomUUID();\n    private Map<String, byte[]> headers = new HashMap<>();\n>>>>>>> REPLACE\n\n<<<<<<< SEARCH\n:start_line:34\n-------\n    public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {\n        this.topic = topic;\n        this.partition = partition;\n        this.key = key;\n        this.data = data;\n        this.timestamp = timestamp;\n=======\n    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp) {\n        this(topic, partition, key, data, timestamp, new HashMap<>());\n    }\n\n    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp, Map<String, byte[]> headers) {\n        this.topic = topic;\n        this.partition = partition;\n        this.key = key;\n        this.data = data;\n        this.timestamp = timestamp;\n        this.headers = headers;\n>>>>>>> REPLACE\n\n<<<<<<< SEARCH\n:start_line:71\n-------\n        return Objects.equals(partition, message.partition) && timestamp == message.timestamp\n                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(\n                messageId, message.messageId);\n=======\n        return Objects.equals(partition, message.partition) && timestamp == message.timestamp\n                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) \n                && Objects.equals(messageId, message.messageId) && Objects.equals(headers, message.headers);\n>>>>>>> REPLACE\n\n<<<<<<< SEARCH\n:start_line:79\n-------\n        int result = Objects.hash( topic, partition, timestamp, messageId);\n=======\n        int result = Objects.hash(topic, partition, timestamp, messageId, headers);\n>>>>>>> REPLACE\n\n<<<<<<< SEARCH\n:start_line:87\n-------\n        return \"HermesMessage:{\" + messageId.toString() + (key == null ? \"\" : \",key=\" + new String(key)) + \"}\";\n=======\n        return \"HermesMessage:{\" + messageId.toString() + (key == null ? \"\" : \",key=\" + new String(key)) \n                + (headers.isEmpty() ? \"\" : \",headers=\" + headers) + \"}\";\n>>>>>>> REPLACE\n\n<<<<<<< SEARCH\n:start_line:137\n-------\n            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);\n=======\n            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp, this.headers);\n>>>>>>> REPLACE\n</diff>\n</apply_diff>
                """;
        //System.out.println(message2);

    }


    @Test
    public void test3() {
        String message = """
                Generate a curl command for testing an API request based on given code and request/response info.\nSo that we can use it to test for APIs.\nYou should try to create semantically meaningful test data based on the code variables.\nIf there is base URL route, the final url should be base URL + java code @XXXMapping\nIf @RequestParam has defaultValue in java code, do not set\n\nrequest to example server: http://localhost:8080\nbase URL route: \ncompare this request body relate info:\nclass RestRequest {\n\n}\n\n\nGiven code:\n```Java\n/**\n     * @param restRequest\n     * @param restChannel\n     */\n    @RequestMapping(value = \"/hermes-proxy/sendSingle\", method = POST)\n    public void sendSingle(RestRequest restRequest, RestChannel restChannel) {\n        String topic = restRequest.param(\"topic\");\n        String ack = restRequest.param(\"ack\", \"1\");\n        long timeout = restRequest.paramAsLong(\"timeout\", 5000l);\n        HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);\n        validateSingleMessage(hermesMessage, topic);\n        StopWatch watch = StopWatch.create().start();\n        ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {\n            log.info(\"sendSingle, topic {} client {} offset {} cost(ms) {}\",\n                    topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed());\n        });\n        client.send(ack, hermesMessage, timeout, actionListener);\n    }\n```\n\n\n<environment_details>\n# JDK version\nOracle OpenJDK 1.8.0_391 - aarch64\n# Current Time\n2025-07-25 17:45\n# IDEA Open Tabs\n/hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java\n</environment_details>
                """;
        System.out.println(message);
    }


}
