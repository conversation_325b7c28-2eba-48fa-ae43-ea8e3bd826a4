package com.buzz.ai.diff;

public class DiffCode {

    public  static String pom_xml_original = """
              1 | <?xml version="1.0" encoding="UTF-8"?>
              2 | <project xmlns="http://maven.apache.org/POM/4.0.0"
              3 |          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              4 |          xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
              5 |     <parent>
              6 |         <artifactId>hermes-parent</artifactId>
              7 |         <groupId>com.wacai.middleware</groupId>
              8 |         <version>4.0-SNAPSHOT</version>
              9 |     </parent>
             10 |     <modelVersion>4.0.0</modelVersion>
             11 |\s
             12 |     <artifactId>hermes-proxy</artifactId>
             13 |\s
             14 |     <properties>
             15 |         <springboot.version>2.2.6.RELEASE</springboot.version>
             16 |     </properties>
             17 |\s
             18 |     <dependencies>
             19 |         <dependency>
             20 |             <groupId>com.wacai.middleware</groupId>
             21 |             <artifactId>hermes-api</artifactId>
             22 |         </dependency>
             23 |         <dependency>
             24 |             <groupId>com.wacai.middleware</groupId>
             25 |             <artifactId>hermes-core</artifactId>
             26 |         </dependency>
             27 |\s
             28 |         <dependency>
             29 |             <groupId>io.netty</groupId>
             30 |             <artifactId>netty-all</artifactId>
             31 |         </dependency>
             32 |\s
             33 |         <dependency>
             34 |             <groupId>org.apache.kafka</groupId>
             35 |             <artifactId>kafka-clients</artifactId>
             36 |         </dependency>
             37 |\s
             38 |         <dependency>
             39 |             <groupId>ch.qos.logback</groupId>
             40 |             <artifactId>logback-classic</artifactId>
             41 |             <version>1.2.3</version>
             42 |             <scope>compile</scope>
             43 |         </dependency>
             44 |\s
             45 |         <dependency>
             46 |             <groupId>com.wacai</groupId>
             47 |             <artifactId>redis-client-all-in</artifactId>
             48 |             <version>3.14.2</version>
             49 |             <exclusions>
             50 |                 <exclusion>
             51 |                     <groupId>log4j</groupId>
             52 |                     <artifactId>log4j</artifactId>
             53 |                 </exclusion>
             54 |             </exclusions>
             55 |         </dependency>
             56 |\s
             57 |         <dependency>
             58 |             <groupId>com.google.inject</groupId>
             59 |             <artifactId>guice</artifactId>
             60 |             <version>5.0.1</version>
             61 |         </dependency>
             62 |\s
             63 |         <!--注意：之前hermes使用的zk server为3.4，不能依赖高版本的curator -->
             64 |         <dependency>
             65 |             <groupId>org.apache.curator</groupId>
             66 |             <artifactId>curator-framework</artifactId>
             67 |         </dependency>
             68 |\s
             69 |         <dependency>
             70 |             <groupId>org.apache.curator</groupId>
             71 |             <artifactId>curator-recipes</artifactId>
             72 |             <version>2.13.0</version>
             73 |         </dependency>
             74 |\s
             75 |         <dependency>
             76 |             <groupId>io.jsonwebtoken</groupId>
             77 |             <artifactId>jjwt</artifactId>
             78 |         </dependency>
             79 |\s
             80 |         <!--
             81 |         <dependency>
             82 |             <groupId>org.springframework.boot</groupId>
             83 |             <artifactId>spring-boot-starter-actuator</artifactId>
             84 |             <version>${springboot.version}</version>
             85 |         </dependency>
             86 |          -->
             87 |     </dependencies>
             88 |\s
             89 |\s
             90 |     <build>
             91 |         <plugins>
             92 |             <plugin>
             93 |                 <groupId>org.springframework.boot</groupId>
             94 |                 <artifactId>spring-boot-maven-plugin</artifactId>
             95 |                 <version>${springboot.version}</version>
             96 |                 <configuration>
             97 |                     <!-- 工程主入口-->
             98 |                     <mainClass>com.wacai.hermes.proxy.HermesProxyApplication</mainClass>
             99 |                 </configuration>
            100 |                 <executions>
            101 |                     <execution>
            102 |                         <goals>
            103 |                             <goal>repackage</goal>
            104 |                         </goals>
            105 |                     </execution>
            106 |                 </executions>
            107 |             </plugin>
            108 |             <plugin>
            109 |                 <groupId>org.apache.maven.plugins</groupId>
            110 |                 <artifactId>maven-compiler-plugin</artifactId>
            111 |                 <version>3.7.0</version>
            112 |                 <configuration>
            113 |                     <source>1.8</source>
            114 |                     <target>1.8</target>
            115 |                     <encoding>UTF-8</encoding>
            116 |                     <compilerArgument>-parameters</compilerArgument>
            117 |                 </configuration>
            118 |             </plugin>
            119 |             <plugin>
            120 |                 <groupId>org.apache.maven.plugins</groupId>
            121 |                 <artifactId>maven-source-plugin</artifactId>
            122 |                 <version>3.0.1</version>
            123 |                 <executions>
            124 |                     <execution>
            125 |                         <id>attach-sources</id>
            126 |                         <goals>
            127 |                             <goal>jar</goal>
            128 |                         </goals>
            129 |                     </execution>
            130 |                 </executions>
            131 |             </plugin>
            132 |             <plugin>
            133 |                 <groupId>org.apache.maven.plugins</groupId>
            134 |                 <artifactId>maven-surefire-plugin</artifactId>
            135 |                 <configuration>
            136 |                     <skip>true</skip>
            137 |                 </configuration>
            138 |             </plugin>
            139 |         </plugins>
            140 |         <resources>
            141 |             <resource>
            142 |                 <directory>src/main/java</directory>
            143 |                 <includes>
            144 |                     <include>**/*.xml</include>
            145 |                 </includes>
            146 |             </resource>
            147 |             <resource>
            148 |                 <directory>src/main/resources</directory>
            149 |             </resource>
            150 |         </resources>
            151 |     </build>
            152 |\s
            153 | </project>
    """;
    public  static String hermes_message_original= """
package com.wacai.hermes.message;

import com.wacai.hermes.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.wacai.hermes.constants.HermesHeader.TAG;

@Getter
@Setter
public class HermesMessage {

    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    private long timestamp;
    private UUID messageId = UUID.randomUUID();

    public HermesMessage() {
    }

    public HermesMessage(byte[] key, byte[] data) {
        this.key = key;
        this.data = data;
    }

    public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
    }

    public static Builder builder() {
        return new Builder();
    }

    public long getPayLoad() {
        long size = 0;
        if (key != null) {
            size += key.length;
        }
        if (data != null) {
            size += data.length;
        }
        return size;
    }

    public String getTraceId() {
        return null;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HermesMessage message = (HermesMessage)o;
        return Objects.equals(partition, message.partition) && timestamp == message.timestamp
                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
                messageId, message.messageId);
    }

    @Override
    public int hashCode() {

        int result = Objects.hash( topic, partition, timestamp, messageId);
        result = 31 * result + Arrays.hashCode(key);
        result = 31 * result + Arrays.hashCode(data);
        return result;
    }

    @Override
    public String toString() {
        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
    }

    public static class Builder {
        private String topic;
        private Integer partition;
        private byte[] key;
        private byte[] data;
        private Map<String, byte[]> headers = new HashMap<>();
        private long timestamp;

        public Builder setHeaders(Map<String, byte[]> headers) {
            // headers.entrySet().removeIf(entry -> entry.getKey().startsWith("hermes."));
            this.headers.putAll(headers);
            return this;
        }

        public Builder setTopic(String topic) {
            this.topic = topic;
            return this;
        }

        public Builder setPartition(Integer partition) {
            this.partition = partition;
            return this;
        }

        public Builder setKey(byte[] key) {
            this.key = key;
            return this;
        }

        public Builder setData(byte[] data) {
            this.data = data;
            return this;
        }

        public Builder setTimestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder setTag(String tag) {
            if (null != tag && !"".equals(tag = tag.trim())) {
                this.headers.put(TAG, tag.getBytes());
            }
            return this;
        }

        public HermesMessage build() {
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
        }
    }
}        
    """;


    public  static String hermes_message_diff= """
<<<<<<< SEARCH
:start_line:17
-------
@Getter
@Setter
public class HermesMessage {

    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    private long timestamp;
    private UUID messageId = UUID.randomUUID();
=======
@Getter
@Setter
public class HermesMessage {

    private String topic;
    private Integer partition;
    private byte[] key;
    private byte[] data;
    private long timestamp;
    private UUID messageId = UUID.randomUUID();
    private Map<String, byte[]> headers = new HashMap<>();
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:34
-------
    public HermesMessage( String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
=======
    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp) {
        this(topic, partition, key, data, timestamp, new HashMap<>());
    }

    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp, Map<String, byte[]> headers) {
        this.topic = topic;
        this.partition = partition;
        this.key = key;
        this.data = data;
        this.timestamp = timestamp;
        this.headers = headers;
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:71
-------
        return Objects.equals(partition, message.partition) && timestamp == message.timestamp
                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
                messageId, message.messageId);
=======
        return Objects.equals(partition, message.partition) && timestamp == message.timestamp
                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data)\s
                && Objects.equals(messageId, message.messageId) && Objects.equals(headers, message.headers);
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:79
-------
        int result = Objects.hash( topic, partition, timestamp, messageId);
=======
        int result = Objects.hash(topic, partition, timestamp, messageId, headers);
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:87
-------
        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
=======
        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key))\s
                + (headers.isEmpty() ? "" : ",headers=" + headers) + "}";
>>>>>>> REPLACE

<<<<<<< SEARCH
:start_line:137
-------
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
=======
            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp, this.headers);
>>>>>>> REPLACE

                 
    """;
    public  static String pom_xml_diff= """
            <<<<<<< SEARCH
            :start_line:14
            -------
                <properties>
                    <springboot.version>2.2.6.RELEASE</springboot.version>
                </properties>
            =======
                <properties>
                    <springboot.version>2.2.6.RELEASE</springboot.version>
                    <dubbo.version>2.7.15</dubbo.version>
                    <nacos.version>1.4.1</nacos.version>
                </properties>
            >>>>>>> REPLACE
            
            <<<<<<< SEARCH
            :start_line:87
            -------
                </dependencies>
            =======
                    <!-- Dubbo dependencies -->
                    <dependency>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-spring-boot-starter</artifactId>
                        <version>${dubbo.version}</version>
                    </dependency>
                   \s
                    <!-- Dubbo Registry Nacos -->
                    <dependency>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-registry-nacos</artifactId>
                        <version>${dubbo.version}</version>
                    </dependency>
                   \s
                    <!-- Nacos Client -->
                    <dependency>
                        <groupId>com.alibaba.nacos</groupId>
                        <artifactId>nacos-client</artifactId>
                        <version>${nacos.version}</version>
                    </dependency>
                </dependencies>
            >>>>>>> REPLACE
            """;



}
