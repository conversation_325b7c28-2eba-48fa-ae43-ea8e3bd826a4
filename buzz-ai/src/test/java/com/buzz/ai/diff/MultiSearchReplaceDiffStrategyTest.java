package com.buzz.ai.diff;
import static org.junit.Assert.*;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
public class MultiSearchReplaceDiffStrategyTest {
    private MultiSearchReplace strategy;

    @Before
    public void setUp() {
        strategy = new MultiSearchReplace(0.9);
    }

    @Test
    public void testBasicReplacement() {
        String original = DiffCode.hermes_message_original;
        String diff = DiffCode.hermes_message_diff ;

        DiffResult result = strategy.applyDiff(original, diff);

        assertTrue(result.isSuccess());
        System.out.println(result.getContent());
        //assertEquals("line1\nnew line2\nline3\nline4", result.getContent());
    }

    @Test
    public void test101() throws IOException {
        String original = Files.readString(Path.of("/tmp/original"));
        String diff = Files.readString(Path.of("/tmp/diff"));
        DiffResult result = strategy.applyDiff(original, diff);
        System.out.println(result.isSuccess());
        System.out.println(result.getContent());
    }

}