package com.buzz.ai.devin;
import com.buzz.ai.devin.command.DevinEngineer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class DevinEngineerTest {
    private DevinEngineer devinEngineer;

    @BeforeEach
    void setUp() {
        devinEngineer = new DevinEngineer();
    }

    @Test
    void testProcessDirCommand() throws Exception {
        String input = "/dir:src/main/java";
        String result = devinEngineer.process(input);
        System.out.println(result);
        assertNotNull(result);
        assertTrue(result.contains("java"));
    }

    @Test
    void testProcessLocalSearchCommand() throws Exception {
        String input = "/localSearch:Command";
        String result = devinEngineer.process(input);
        assertNotNull(result);
        assertTrue(result.contains("Found") || result.contains("No matches found"));
    }

    @Test
    void testExecuteMultipleCommands() throws Exception {
        String input = "/dir:src/main/java\n" +
                "/localSearch:Devin";
        String result = devinEngineer.process(input);
        System.out.println(result);
        assertNotNull(result);
        assertTrue(result.contains("java"));
    }

    @Test
    void testProcessInvalidCommand() {
        String input = "/invalid:test";
        Exception exception = assertThrows(IllegalArgumentException.class,
                () -> devinEngineer.process(input));
        assertTrue(exception.getMessage().contains("Unsupported command"));
    }
}
