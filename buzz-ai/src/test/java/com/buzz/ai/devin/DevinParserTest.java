package com.buzz.ai.devin;

import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DevinParserTest {

    @Test
    void test101() {
        String input = """
                根据 `HermesMessage.java` 文件的内容，我发现 `HermesMessage` 类已经支持 `headers`，但它是通过 `Builder` 类来设置的。为了优化 `ProducerController` 并为发送的消息增加 header，我们需要在 `ProducerController` 中从 `RestRequest` 中提取 header 并设置到 `HermesMessage` 中。
                
                ### 计划更新
                ```plan
                1. 修改 `ProducerController` 类以支持从请求中提取 header 并设置到 `HermesMessage` 中
                 - [ ] 在 `sendSingle` 方法中，从 `RestRequest` 中提取 header 并设置到 `HermesMessage` 中
                 - [ ] 在 `sendBatch` 方法中，从 `RestRequest` 中提取 header 并设置到 `HermesMessage` 中
                 - [ ] 在 `publish` 方法中，从 `RestRequest` 中提取 header 并设置到 `HermesMessage` 中
                ```
                
                ### 第一步：修改 `sendSingle` 方法
                在 `sendSingle` 方法中，我们将从 `RestRequest` 中提取 header 并设置到 `HermesMessage` 中。
                
                <devin>
                /patch:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                ```patch
                Index: hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                ===================================================================
                --- hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                +++ hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                @@ -1,6 +1,7 @@
                 package com.wacai.hermes.proxy.controller;
                
                 import com.google.inject.Inject;
                +import com.wacai.hermes.constants.HermesHeader;
                 import com.wacai.hermes.async.ActionListener;
                 import com.wacai.hermes.core.rest.RestController;
                 import com.wacai.hermes.core.rest.handler.BaseRestHandler;
                @@ -15,6 +16,7 @@
                 import com.wacai.hermes.rest.RestResponse;
                 import com.wacai.hermes.runtime.ISettings;
                 import com.wacai.hermes.util.Assert;
                +import java.util.Map;
                 import lombok.extern.slf4j.Slf4j;
                 import org.apache.commons.lang3.StringUtils;
                
                @@ -51,6 +53,12 @@
                         String ack = restRequest.param("ack", "1");
                         long timeout = restRequest.paramAsLong("timeout", 5000l);
                         HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);
                +        // Extract headers from request and set to HermesMessage
                +        Map<String, String> headers = new HashMap<>();
                +        for (String headerName : restRequest.getHeaders().keySet()) {
                +            headers.put(headerName, restRequest.header(headerName));
                +        }
                +        hermesMessage.setHeaders(headers);
                         validateSingleMessage(hermesMessage, topic);
                         StopWatch watch = StopWatch.create().start();
                         ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
                ```
                </devin>
                
                ### 第二步：修改 `sendBatch` 方法
                在 `sendBatch` 方法中，我们将从 `RestRequest` 中提取 header 并设置到 `HermesMessage` 中。
                
                <devin>
                /patch:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                ```patch
                Index: hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                ===================================================================
                --- hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                +++ hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                @@ -82,6 +82,12 @@
                         String trace = restRequest.param("trace", "off");
                         List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);
                
                +        // Extract headers from request and set to each HermesMessage
                +        Map<String, String> headers = new HashMap<>();
                +        for (String headerName : restRequest.getHeaders().keySet()) {
                +            headers.put(headerName, restRequest.header(headerName));
                +        }
                +        messages.forEach(message -> message.setHeaders(headers));
                         validateBatchMessages(messages, topic);
                         StopWatch watch = StopWatch.create().start();
                         ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {
                ```
                </devin>
                
                ### 第三步：修改 `publish` 方法
                在 `publish` 方法中，我们将从 `RestRequest` 中提取 header 并设置到 `HermesMessage` 中。
                
                <devin>
                /patch:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                ```patch
                Index: hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                ===================================================================
                --- hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                +++ hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                @@ -142,6 +142,12 @@
                         }
                         hermesMessage.setData(msgVal.getBytes("UTF-8"));
                         StopWatch watch = StopWatch.create().start();
                +        // Extract headers from request and set to HermesMessage
                +        Map<String, String> headers = new HashMap<>();
                +        for (String headerName : restRequest.getHeaders().keySet()) {
                +            headers.put(headerName, restRequest.header(headerName));
                +        }
                +        hermesMessage.setHeaders(headers);
                         ActionListener<MessageMeta> actionListener = ActionListener.wrap((ret, e) -> {
                             if (ret != null) {
                                 log.info("publish, topic {} client {} offset {} cost(ms) {}",
                ```
                </devin>
                
                ### 第四步：更新 `HermesMessage` 类
                我们需要在 `HermesMessage` 类中添加 `setHeaders` 方法，以便能够设置 header。
                
                <devin>
                /patch:hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
                ```patch
                Index: hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
                ===================================================================
                --- hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
                +++ hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
                @@ -13,6 +13,7 @@
                 @Setter
                 public class HermesMessage {
                
                +    private Map<String, String> headers;
                     private String topic;
                     private Integer partition;
                     private byte[] key;
                ```
                </devin>
                
                ### 第五步：提交更改
                完成所有修改后，我将生成提交信息：
                
                <devin>
                /commit
                ```markdown
                feat: add headers support for Kafka messages in ProducerController
                ```
                </devin>
                
                现在，您可以运行代码并测试这些更改。如果有任何问题或需要进一步的修改，请告诉我！
                """;
        List<DevinLang> results = DevinParser.parseMultiple(input);
    }

    @Test
    void testMultiCommand() {
        String input = """
                为Blog新增删除API
                /dir:src/main/java/com/wacai/hermes/producer
                /dir:src/main/
                /localSearch:java
                """;
        List<DevinLang> results = DevinParser.parseMultiple(input);
        for (DevinLang dev : results) {
            if (dev.getType() == 1) {
                System.out.println(dev.getText());
            } else {
                System.out.println(dev.getCommand() + "\t" + dev.getArg());
            }
        }
    }

    @Test
    void testMultiLineCommand() {
        String input = """
                /patch:src/main/java/com/wacai/hermes/producer/Main.java
                ```
                <<<<<<< SEARCH
                原始代码
                =======
                新代码
                >>>>>>> REPLACE
                ```
                /patch:src/main/java/Main.java
                ```
                main code
                ```
                """;

        List<DevinLang> results = DevinParser.parseMultiple(input);

        assertEquals(2, results.size());

        // 验证第一个命令 (dir)
        assertEquals("patch", results.get(0).getCommand());
        assertEquals("src/main/java/com/wacai/hermes/producer/Main.java", results.get(0).getArg());
        assertNotNull(results.get(0).getText());

        // 验证第二个命令 (patch with code)
        assertEquals("patch", results.get(1).getCommand());
        assertEquals("src/main/java/Main.java", results.get(1).getArg());
        assertNotNull(results.get(0).getText());
    }


    @Test
    void testMixedCommands() {
        String input = """
                /dir:src/main/java
                /patch:src/test/java/Test.java
                ```
                test code
                ```
                /localSearch:keyword
                /patch:src/main/java/Main.java
                ```
                main code
                ```
                """;

        System.out.println(input);
        List<DevinLang> results = DevinParser.parseMultiple(input);

        assertEquals(4, results.size());

        // 验证第一个命令 (dir)
        assertEquals("dir", results.get(0).getCommand());
        assertEquals("src/main/java", results.get(0).getArg());
        assertNull(results.get(0).getText());

        // 验证第二个命令 (patch with code)
        assertEquals("patch", results.get(1).getCommand());
        assertEquals("src/test/java/Test.java", results.get(1).getArg());
        assertEquals("test code", results.get(1).getText().trim());

        // 验证第三个命令 (localSearch)
        assertEquals("localSearch", results.get(2).getCommand());
        assertEquals("keyword", results.get(2).getArg());
        assertNull(results.get(2).getText());

        // 验证第四个命令 (patch with code)
        assertEquals("patch", results.get(3).getCommand());
        assertEquals("src/main/java/Main.java", results.get(3).getArg());
        assertEquals("main code", results.get(3).getText().trim());


    }


}