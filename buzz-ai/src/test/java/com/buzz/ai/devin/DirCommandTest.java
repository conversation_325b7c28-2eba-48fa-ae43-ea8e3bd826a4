package com.buzz.ai.devin;

import org.junit.Test;

import java.nio.file.Path;

public class DirCommandTest {

    @Test
    public void test101() {
        DirCommand dirCommand = new DirCommand(Path.of("/System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4"));
        dirCommand.setArguments(new String[]{".","true"});
        try {
            String result = dirCommand.execute();
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
