package com.buzz.ai.util;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

public class FastJsonTest {


    @Test
    public void test() {
        HistoryDialogue historyDialogue = new HistoryDialogue();
        historyDialogue.setId("111");
        historyDialogue.setName("jack ma");
        System.out.println(JSON.toJSONString(historyDialogue));
    }


    @Data
    public static class HistoryDialogue {
        private String id;
        private String name;
        private String role;

    }


    @Test
    public void test2() throws IOException {
        String json = new String(Files.readAllBytes(Path.of("/Users/<USER>/.wapilot/hermes-parent4/history/202524.json")));
        List<HistoryDialogue> list = JSON.parseArray(json, HistoryDialogue.class);
        for (HistoryDialogue item : list) {
            if (item == null) {
                System.out.println("null");
            } else {
                System.out.println(item.getId() + "," + item.getRole());
            }
        }
    }

}
