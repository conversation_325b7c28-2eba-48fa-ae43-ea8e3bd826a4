package com.buzz.ai.util;

import com.buzz.ai.util.XMLCommandParser;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class XmlCommandParserTest {
    
    @Test
    public void testParseSingleCommand() throws Exception {
        String xml = """
                <thinking>
                1. 需要优化的文件是 `hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`
                2. 任务要求：
                   - 为发送消息增加 header
                   - 将 header 设置到 Kafka 消息中
                3. 需要先查看当前 ProducerController 的实现，了解消息发送逻辑
                4. 需要了解 Kafka Producer 如何设置消息头（headers）
                5. 修改后需要确保不影响现有功能
                </thinking>
                
                <read_file>
                <path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
                </read_file>                
                """;

        XMLCommandParser parser = new XMLCommandParser();
        parser.process(xml);

        for (XMLCommandParser.Command cmd : parser.getCommands()) {
            System.out.println("- " + cmd.language() + " (complete: " + cmd.isComplete() + ")");
            if (cmd.isComplete()) {
                System.out.println("  Content: " + cmd.text().substring(0, Math.min(20, cmd.text().length())) + "...");
                if (!cmd.getAttributes().isEmpty()) {
                    System.out.println("  Attributes: " + cmd.getAttributes());
                }
            }
        }
    }

    @Test
    public void testChunks(){

        // 模拟流式接收
        XMLCommandParser parser = new XMLCommandParser();

        // 分块处理
        String[] chunks = {
                "<thi", "nking>\n1. 需要优化", " `ProducerController` 为消息发送增加 header 功能\n",
                "2. 根据环境信息，目标文件路径是: [`hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`](hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java)\n",
                "3. 需要先查看当前 ProducerController 的实现，了解消息发送逻辑\n",
                "4. 然后修改代码增加 header 支持，并确保正确设置到 Kafka 消息中\n",
                "5. 需要确认 Kafka 客户端版本和 API 以确定如何添加 headers\n</thinking>\n\n",
                "<read_file>\n<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>\n</read_file>"
        };

        for (String chunk : chunks) {
            parser.process(chunk);
            System.out.println("Processed chunk: " + chunk);
            System.out.println("Current commands:");
            for (XMLCommandParser.Command cmd : parser.getCommands()) {
                System.out.println("- " + cmd.language() + " (complete: " + cmd.isComplete() + ")");
                if (cmd.isComplete()) {
                    System.out.println("  Content: " + cmd.text().substring(0, Math.min(20, cmd.text().length())) + "...");
                    if (!cmd.getAttributes().isEmpty()) {
                        System.out.println("  Attributes: " + cmd.getAttributes());
                    }
                }
            }
            System.out.println();
        }
    }


}