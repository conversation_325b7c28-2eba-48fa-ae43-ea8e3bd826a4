package com.buzz.ai.util;

import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class XmlChunkTest {

    @Test
    public void testParseCompleteXml() {
        String xml = """
                <thinking>
                1. 测试完整XML解析
                2. 验证嵌套标签处理
                <nested>value</nested>
                </thinking>
                
                <command>
                <path>test/path</path>
                </command>
                """;

        List<CodeFence> chunks = CodeFence.parseAll(xml);
        assertEquals(2, chunks.size());

        // 验证第一个标签
        CodeFence thinkingChunk = chunks.get(0);
        assertEquals("thinking", thinkingChunk.getName());
        assertTrue(thinkingChunk.isComplete());
        assertEquals("1. 测试完整XML解析\n2. 验证嵌套标签处理", thinkingChunk.getText().trim());
        assertEquals(1, thinkingChunk.getAttributes().size());
        assertEquals("value", thinkingChunk.getAttributes().get("nested"));

        // 验证第二个标签
        CodeFence commandChunk = chunks.get(1);
        assertEquals("command", commandChunk.getName());
        assertTrue(commandChunk.isComplete());
        assertEquals("", commandChunk.getText().trim());
        assertEquals(1, commandChunk.getAttributes().size());
        assertEquals("test/path", commandChunk.getAttributes().get("path"));
    }

    @Test
    public void testParseIncompleteXml() {
        String incompleteXml = """
                <thinking>
                1. 测试不完整XML
                2. 验证部分内容处理
                <nested>value
                """;

        List<CodeFence> chunks = CodeFence.parseAll(incompleteXml);
        assertEquals(1, chunks.size());

        CodeFence chunk = chunks.get(0);
        assertEquals("thinking", chunk.getName());
        assertFalse(chunk.isComplete());
        assertEquals("1. 测试不完整XML\n2. 验证部分内容处理\n<nested>value", chunk.getText().trim());
        assertTrue(chunk.getAttributes().isEmpty());
    }

    @Test
    public void testParseMultipleNestedTags() {
        String xml = """
                <message>
                <type>request</type>
                <content>测试内容</content>
                <priority>high</priority>
                </message>
                """;

        List<CodeFence> chunks = CodeFence.parseAll(xml);
        assertEquals(1, chunks.size());

        CodeFence messageChunk = chunks.get(0);
        assertEquals("message", messageChunk.getName());
        assertTrue(messageChunk.isComplete());
        assertEquals("", messageChunk.getText().trim());
        assertEquals(3, messageChunk.getAttributes().size());
        assertEquals("request", messageChunk.getAttributes().get("type"));
        assertEquals("测试内容", messageChunk.getAttributes().get("content"));
        assertEquals("high", messageChunk.getAttributes().get("priority"));
    }

    @Test
    public void testChunkProcessing() {
        // 模拟流式接收的分块XML
        String[] chunks = {
                "<thi", "nking>\n1. 需要优化", " `ProducerController` 为消息发送增加 header 功能\n",
                "2. 根据环境信息，目标文件路径是: [`hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`](hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java)\n",
                "3. 需要先查看当前 ProducerController 的实现，了解消息发送逻辑\n",
                "4. 然后修改代码增加 header 支持，并确保正确设置到 Kafka 消息中\n",
                "5. 需要确认 Kafka 客户端版本和 API 以确定如何添加 headers\n</thinking>\n\n",
                "<read_file>\n<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>\n</read_file>"
        };

        StringBuilder builder = new StringBuilder();
        for (String chunk : chunks) {
            builder.append(chunk);
            List<CodeFence> parsedChunks = CodeFence.parseAll(builder.toString());
            
            System.out.println("Processed chunk: " + chunk);
            System.out.println("Current parsed chunks:");
            for (CodeFence xmlChunk : parsedChunks) {
                System.out.println("- " + xmlChunk.getName() + 
                        " (complete: " + xmlChunk.isComplete() + ")");
                if (!xmlChunk.getAttributes().isEmpty()) {
                    System.out.println("  Attributes: " + xmlChunk.getAttributes());
                }
            }
            System.out.println();
        }
    }


    @Test
    public void testParseIncompleteOuterWithCompleteInnerTags() {
        String xml = """
            <message>
                <type>request</type>
                <content>测试内容。。。。
            """;

        List<CodeFence> chunks = CodeFence.parseAll(xml);
        assertEquals(1, chunks.size());

        CodeFence messageChunk = chunks.get(0);
        assertEquals("message", messageChunk.getName());
        assertFalse(messageChunk.isComplete());  // 外层标签不完整

        // 验证内嵌的完整标签被解析为属性
        assertEquals(1, messageChunk.getAttributes().size());
        assertEquals("request", messageChunk.getAttributes().get("type"));

        // 验证剩余文本内容
        assertTrue(messageChunk.getText().contains("<content>测试内容。。。"));
    }
}