package com.buzz.ai.util;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.Data;
import org.junit.Test;

import java.util.Date;

public class JacksonTest {

    private static final ObjectMapper objectMapper = new ObjectMapper();


    static {
        SimpleModule module = new SimpleModule();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(module)
                .configure(JsonParser.Feature.ALLOW_COMMENTS, true)
                .configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);


    }

    @Test
    public void test101() throws JsonProcessingException {
        Event event = new Event("jack","wapilot","test","hello");
        String result = objectMapper.writeValueAsString(event);
        System.out.println(result);
    }


    @Data
    public static class Event {
        private String user;
        private String project;
        private String eventType;
        private Integer value;
        private String message;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'",timezone="GMT+8")
        private Date timestamp = new Date();

        public Event(String user, String project, String eventType, Integer value) {
            this.user = user;
            this.project = project;
            this.eventType = eventType;
            this.value = value;
        }

        public Event(String user, String project, String eventType, String message) {
            this.user = user;
            this.project = project;
            this.eventType = eventType;
            this.message = message;
        }


    }
}
