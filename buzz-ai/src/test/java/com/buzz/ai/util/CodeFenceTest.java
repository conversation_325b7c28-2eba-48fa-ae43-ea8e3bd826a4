package com.buzz.ai.util;

import com.buzz.ai.devin.DevinParser;
import com.buzz.ai.devin.command.*;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class CodeFenceTest {

    @Test
    public void testPlan() {
        String input = """
                <thinking>
                1. 分析问题：
                - 用户询问conda命令中`-c conda-forge`参数的含义
                - 这是conda安装包时常用的参数
                
                2. 需要解释：
                - `-c`参数的作用
                - `conda-forge`渠道的特殊性
                - 为什么安装jina需要指定这个渠道
                
                3. 额外信息：
                - conda默认渠道与conda-forge的区别
                - 使用conda-forge的优势
                </thinking>
                
                `-c conda-forge`参数解释：
                
                1. `-c`是`--channel`的缩写，指定从哪个conda渠道安装包
                2. `conda-forge`是一个社区维护的软件包仓库，特点：
                   - 包含大量最新版本的软件包
                   - 更新频率高于conda默认渠道
                   - 特别适合科学计算、机器学习等领域的包
                3. 对于jina这样的AI工具，conda-forge通常：
                   - 提供预编译的二进制包
                   - 解决复杂的依赖关系
                   - 确保在Mac M1等ARM架构上正常工作
                
                其他常用conda渠道：
                - `defaults`：Anaconda官方渠道
                - `pytorch`：PyTorch专用渠道
                """;
        List<CodeFence> results = CodeFence.parseAll(input);
        printAll(results);

    }

    @Test
    public void testWapilot() {
        try {
            String input = Files.readString(Path.of("/tmp/test.log"));
            List<CodeFence> results = CodeFence.parseAll(input);
            printAll(results);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void testParseCompleteXml() {
        String content = """
                我需要查看`DebtorTagService.java`文件的内容才能进行代码优化分析。请允许我先获取该文件内容：
                <devin>
                /file:src/main/java/com/wacai/loan/blackshark/preasset/service/DebtorTagService.java
                </devin>
                获取到文件内容后，我可以分析其中的代码结构、业务逻辑和潜在优化点，然后提供具体的优化建议。代码如下：
 
                ```java src/main/java/com/buzz/ai/index/JavaEmbeddingIndexer.java
                public class JavaEmbeddingIndexer {
                    // ... 保留原有字段 ...
                    private final SummaryService summaryService;
        
                    public JavaEmbeddingIndexer() throws IOException {
                        // ... 保留原有初始化 ...
                        this.summaryService = new SummaryService();
                    }
                }
                ```
                """;

        List<CodeFence> parsedChunks = CodeFence.parseAll(content);
        printAll(parsedChunks);

    }

    @Test
    public void testCodeWithComment() {
        String input = """
                ```java src/main/java/com/buzz/ai/index/JavaEmbeddingIndexer.java
                        // ... 保留原有导入 ...
                
                        public class JavaEmbeddingIndexer {
                            // ... 保留原有字段 ...
                            private final SummaryService summaryService;
                
                            public JavaEmbeddingIndexer() throws IOException {
                                // ... 保留原有初始化 ...
                                this.summaryService = new SummaryService();
                            }
                
                            void processJavaFile(File file) throws IOException {
                                // ... 保留原有代码直到生成摘要部分 ...
                
                                // 修改为使用 SummaryService
                                Map<String, Map<String, String>> summaries;
                                try {
                                    summaries = summaryService.generateSummary(content);
                                    // 保存摘要到JSON文件
                                    String jsonPath = file.getAbsolutePath().replace(".java", "_summary.json");
                                    summaryService.saveSummaryToJson(summaries, jsonPath);
                                } catch (Exception e) {
                                    logger.error("summary error", e);
                                    continue;
                                }
                
                                // ... 保留剩余代码 ...
                            }
                
                            // 删除原来的 generateSummaryWithOpenAI 方法
                        }
                        ```                
                """;
        printAll(CodeFence.parseAll(input));

    }

    @Test
    public void testMarkdownCodeBlockParsing() {
        // Test case 1: With language and path
        String content1 = """
                ```java /path/to/file
                public class Test {
                    public static void main(String[] args) {
                        System.out.println("Hello");
                    }
                }
                ```
                """;
        List<CodeFence> result1 = CodeFence.parseAll(content1);
        assertEquals(1, result1.size());
        assertEquals("java", result1.get(0).getName());
        assertEquals("/path/to/file", result1.get(0).getAttribute("path"));
        assertTrue(result1.get(0).getText().contains("public class Test"));

        // Test case 2: With language only
        String content2 = """
                ```json
                [
                    {
                        "key": "value"
                    }
                ]
                ```
                """;
        List<CodeFence> result2 = CodeFence.parseAll(content2);
        assertEquals(1, result2.size());
        assertEquals("json", result2.get(0).getName());
        assertTrue(result2.get(0).getText().contains("\"key\": \"value\""));
        assertNull(result2.get(0).getAttribute("path"));

        // Test case 3: No language or path (raw content)
        String content3 = """
                ```
                [{
                    "key": "value"
                }]
                ```
                """;
        List<CodeFence> result3 = CodeFence.parseAll(content3);
        assertEquals(1, result3.size());
        assertEquals("", result3.get(0).getName());
        assertTrue(result3.get(0).getText().contains("\"key\": \"value\""));
        assertNull(result3.get(0).getAttribute("path"));

        // Test case 4: Multiple spaces between language and path (should not match path)
        String content4 = """
                ```java /path/to/file
                public class Test {}
                ```
                """;
        List<CodeFence> result4 = CodeFence.parseAll(content4);
        assertEquals(1, result4.size());
        assertEquals("java", result4.get(0).getName());
        assertNotNull(result4.get(0).getAttribute("path"));
        assertTrue(result4.get(0).getText().contains("public class Test"));

        // Test case 5: Special characters in content (should not be parsed as path)
        String content5 = """
                ```java
                [{
                    "key": "value"
                }]
                ```
                """;
        List<CodeFence> result5 = CodeFence.parseAll(content5);
        assertEquals(1, result5.size());
        assertEquals("java", result5.get(0).getName());
        assertTrue(result5.get(0).getText().contains("[{"));
        assertNull(result5.get(0).getAttribute("path"));
    }

    /**
     * 支持<devin></devin> 和```devin 两种
     */
    @Test
    void test101() {
        String input = """
                <thinking>
                1. 需要优化的文件是 `hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`
                2. 任务要求：
                   - 为发送消息增加 header
                   - 将 header 设置到 Kafka 消息中
                3. 需要先查看当前 ProducerController 的实现，了解消息发送逻辑
                4. 需要了解 Kafka Producer 如何设置消息头（headers）
                5. 修改后需要确保不影响现有功能
                </thinking>
                
                <read_file>
                <path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>
                </read_file>
                """;
        List<CodeFence> result = CodeFence.parseAll(input);
        printAll(result);
    }

    @Test
    void test102() {
        String input = """
                <devin>
                /file:hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
                </devin>
                
                ```java 
                package com.wacai.hermes.message;
                
                import com.wacai.hermes.util.StringUtils;
                @Getter
                @Setter
                public class HermesMessage {
                
                }
                ```
                
                <thinking>
                现在需要修改 `ProducerController` 来支持从请求中提取 headers 并设置到消息中。主要修改点：
                1. 在 `sendSingle` 方法中从请求中提取 headers
                2. 在 `sendBatch` 方法中从请求中提取 headers
                3. 在 `publish` 方法中从请求中提取 headers
                </thinking>
                
                <devin>
                /file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                </devin>
                
                ```java hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java
                package com.wacai.hermes.proxy.controller;
                
                import com.google.inject.Inject;
                import com.wacai.hermes.async.ActionListener;
                import com.wacai.hermes.core.rest.RestController;
                ```
                
                <thinking>
                修改说明：
                1. 在 `HermesMessage` 类中添加了 headers 字段和相关方法
                2. 在 `ProducerController` 中添加了 `extractHeaders` 方法来从请求中提取以 "x-" 或 "X-" 开头的 headers
                3. 修改了三个消息发送方法，在构建消息时设置 headers
                4. 保持了与现有代码的兼容性
                
                这些修改将使 Kafka 消息能够携带 headers 信息，便于消息追踪和其他用途
                </thinking>
                
                根据代码分析，我将为 Kafka 消息添加 header 支持，以下是具体修改方案：
                这些修改将：
                1. 允许从 HTTP 请求头中提取以 "x-" 或 "X-" 开头的 header
                2. 将 header 添加到 Kafka 消息中
                3. 保持向后兼容性
                4. 提供灵活的 header 处理方式
                
                header 将可用于消息追踪、路由等高级功能，同时不会影响现有消息处理逻辑。
                """;
        List<CodeFence> result = CodeFence.parseAll(input);
        printAll(result);
        //findDevinAndParse(result);

    }

    void printAll(List<CodeFence> result) {
        for (CodeFence codeFence : result) {
            System.out.println("====================");
            System.out.println("type:" + codeFence.getType() + "\t name:" + codeFence.getName() + "\n" + codeFence.getText());
            System.out.println("====================");
        }
    }

    void findDevinAndParse(List<CodeFence> result) {
        CodeFence devinCode = result.stream().filter(i -> i.getName().equals("devin")).findAny().get();

        DevinEngineer devinEngineer = new DevinEngineer();
        try {
            devinEngineer.process(devinCode.getText());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testInvalidDevin() {
        String input = """
                <devin>
                /search_code
                ```json
                {"query": "ProducerController.java"}
                ```
                </devin>                
                """;

        List<CodeFence> result = CodeFence.parseAll(input);
        assertEquals(1, result.size());
        assertEquals("devin", result.get(0).getName());
        String devinInput = result.get(0).getText();
        try {
            System.out.println(DevinParser.parseMultiple(devinInput));
        } catch (Exception e) {
        }

    }

    @Test
    void testParseDevinBlock() {
        String input = """
                <devin>
                /dir:src/main/java
                /file:pom.xml
                ```java
                public class Test {}
                ```
                </devin>
                """;

        CodeFence result = CodeFence.parseAll(input).get(0);
        assertEquals("devin", result.getName());
        assertTrue(result.isComplete());
        assertTrue(result.getText().contains("/dir:src/main/java"));
    }

    @Test
    void testParseMarkdownCodeBlock() {
        String input = """
                ```java
                public class Example {
                    private String test;
                }
                ```
                """;

        CodeFence result = CodeFence.parseAll(input).get(0);
        assertEquals("java", result.getName());
        assertTrue(result.isComplete());
        assertTrue(result.getText().contains("public class Example"));
    }

    @Test
    void testParseAllMixedContent() {
        String input = """
                Some markdown text
                ```java
                class Test {}
                ```
                <devin>
                /dir:src
                </devin>
                More text
                ```python
                def hello():
                    pass
                ```
                """;

        List<CodeFence> results = CodeFence.parseAll(input);
        assertEquals(5, results.size());

        // Check markdown text
        assertEquals("markdown", results.get(0).getName());
        assertTrue(results.get(0).getText().contains("Some markdown text"));

        // Check Java code block
        assertEquals("java", results.get(1).getName());
        assertTrue(results.get(1).getText().contains("class Test"));

        // Check devin block
        assertEquals("devin", results.get(2).getName());
        assertTrue(results.get(2).getText().contains("/dir:src"));

        // Check second markdown text
        assertEquals("markdown", results.get(3).getName());
        assertTrue(results.get(3).getText().contains("More text"));

        // Check Python code block
        assertEquals("python", results.get(4).getName());
        assertTrue(results.get(4).getText().contains("def hello"));
    }

    @Test
    void testIncompleteCodeBlock() {
        String input = """
                ```java
                public class Test {
                    // No closing fence
                """;

        CodeFence result = CodeFence.parseAll(input).get(0);
        assertEquals("java", result.getName());
        assertFalse(result.isComplete());
        assertTrue(result.getText().contains("public class Test"));
    }

    @Test
    void testThinkingTagParsing() throws IOException {
        String input = """
                <thinking>\n1. 需要找到 ProducerController 的代码文件位置\n2. 需要了解当前 Kafka 消息发送的实现方式\n3. 需要修改代码以支持 header 设置\n4. 需要确保修改符合 Kafka 客户端 API 规范\n\n首先需要定位 ProducerController 文件位置，可以通过搜索文件名来查找\n</thinking>\n\n<devin>\n/localSearch:ProducerController\n</devin>
                """;

        //String input = Files.readString(Path.of("/tmp/log"));
        List<CodeFence> results = CodeFence.parseAll(input);

        // 打印结果以检查
        for (CodeFence fence : results) {
            System.out.println("Language: " + fence.getName() + ", Text: " + fence.getText().substring(0, Math.min(20, fence.getText().length())));
        }

        // 验证thinking标签内容被正确解析
        Optional<CodeFence> thinkingFence = results.stream()
                .filter(fence -> "thinking".equals(fence.getName()))
                .findFirst();

        assertTrue(thinkingFence.isPresent(), "应该存在thinking类型的CodeFence");
        //assertEquals("这是思考内容", thinkingFence.get().getText());
    }
}
